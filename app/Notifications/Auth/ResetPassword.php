<?php

namespace App\Notifications\Auth;

use Illuminate\Auth\Notifications\ResetPassword as BaseResetPassword;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Lang;

class ResetPassword extends BaseResetPassword
{
    /**
     * Get the reset password notification mail message for the given URL.
     *
     * @param  string  $url
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    #[\Override]
    protected function buildMailMessage($url)
    {
        return (new MailMessage())
            ->subject(Lang::get('Reset Your GravityWrite Password'))
            ->greeting('Hello!')
            ->line(Lang::get('You are receiving this email because we received a password reset request for your GravityWrite account.'))
            ->action(Lang::get('Reset Password'), $url)
            ->line(Lang::get('This password reset link will expire in :count minutes.', ['count' => config('auth.passwords.' . config('auth.defaults.passwords') . '.expire')]))
            ->line(Lang::get('If you did not request a password reset, please ignore this email or contact support if you have concerns.'))
            ->salutation('<PERSON><PERSON>, The GravityWrite Team');
    }
}
