<?php

namespace App\Abstracts;

use App\Traits\JsonResponseTrait;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest as HttpFormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

abstract class JsonRequest extends HttpFormRequest
{
    use JsonResponseTrait;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    abstract public function rules(): array;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Handle a failed authorization attempt.
     *
     * @return JsonResponse|void
     */
    protected function failedAuthorization()
    {
        try {
            throw new AuthorizationException('Unauthorized.');
        } catch (\Throwable) {
            return $this->errorResponse(null, 'Unauthorized', 401, 'unauthorized');
        }
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    #[\Override]
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'status' => false,
            'message' => $validator->errors()->first(),
            'type' => 'validation_error',
            'errors' => $validator->errors(),
        ], 422));
    }
}
