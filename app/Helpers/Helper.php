<?php

if ( ! function_exists('createInitSubscription')) {
    /**
     * Create an initial subscription for the user.
     *
     * @param \App\Models\User|null $user
     *
     * @return void
     */
    function createInitSubscription($user = null): void
    {
        if ( ! $user) {
            $user = auth()->user();
        }

        if ($user->subscription) {
            return;
        }

        $user->subscription()->create([
            'status' => "active",
            'user_id' => $user->id,
            'plan_id' => 1,
            'is_react' => '1',
        ]);
    }
}
