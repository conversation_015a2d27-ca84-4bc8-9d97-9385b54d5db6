<?php

namespace App\Repositories;

use <PERSON><PERSON>\Passport\Bridge\ClientRepository as PassportClientRepository;

class CustomClientRepository extends PassportClientRepository
{
    /**
     * Validate a client's credentials.
     *
     * @param  string  $clientIdentifier The client's identifier
     * @param  string  $clientSecret The client's secret
     * @param  string  $grantType The type of grant the client is using (authorization_code, password, etc)
     *
     * @return bool
     */
    #[\Override]
    public function validateClient($clientIdentifier, $clientSecret, $grantType)
    {
        // First, we will verify that the client exists and is authorized to create tokens
        $record = $this->clients->findActive($clientIdentifier);

        if ( ! $record) {
            return false;
        }

        // For the 'authorization_code' grant type, we will skip the client secret validation
        if ($grantType === 'authorization_code') {
            return $clientSecret === $record->secret || password_verify($clientSecret, (string) $record->secret) || hash_equals($record->secret, $clientSecret);
        }

        // For other grant types, use the default validation
        return parent::validateClient($clientIdentifier, $clientSecret, $grantType);
    }
}
