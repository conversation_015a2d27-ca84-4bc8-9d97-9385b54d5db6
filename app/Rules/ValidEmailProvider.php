<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Http;

class ValidEmailProvider implements Rule
{
    /**
     * Check if the given value is a valid email address according to the email provider.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (str_contains((string) $value, '@gmail.com') || str_contains((string) $value, '.ru') || str_contains((string) $value, '@tutanota') || app()->environment('local')) {
            return true;
        }

        $response = Http::get('https://emailverifier.reoon.com/api/v1/verify', [
            'email' => $value,
            'key' => config('services.email_verifier_key'),
            'mode' => 'power',
        ])->json();

        return ! $response['is_disposable'];
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The provided email address is not allowed or appears to be disposable.';
    }
}
