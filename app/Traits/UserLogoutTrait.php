<?php

namespace App\Traits;

use App\Models\Passport\OAuthSession;
use App\Models\Passport\Token;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Laravel\Passport\PersonalAccessTokenFactory;

trait UserLogoutTrait
{
    /**
     * Revoke all tokens for a user.
     *
     * @param  int  $userId
     *
     * @return int|null
     */
    public function revokeAllTokens(string|null $token = null, int|null $userId = null): int|null
    {
        if ($token) {
            $response['access_token'] = $token;

            $token = app(PersonalAccessTokenFactory::class)->findAccessToken($response);
            $userId = $token->user->id;
        }

        // Get all tokens for the user
        $tokens = Token::where('user_id', $userId)
            ->where('name', null)
            ->where('revoked', false)
            ->get();

        // Revoke each token and its refresh tokens
        foreach ($tokens as $token) {
            $token->revoke();
            $token->refreshToken?->revoke();
        }

        return $userId;
    }

    /**
     * Revoke all sessions for a user.
     *
     * @param  int  $userId
     *
     * @return void
     */
    public function revokeAllSessions($userId)
    {
        DB::table('sessions')
            ->where('user_id', $userId)
            ->delete();

        if (Auth::check() && Auth::id() == $userId) {
            Auth::logout();
        }
    }
    /**
     * Revoke all tokens issued by the gateway client.
     *
     * The tokens are identified by the client ID stored in the session
     * and the user ID of the authenticated user.
     *
     * @return void
     */
    public function revokeGWToken()
    {
        if ( ! session('redirect_uri')) {
            return;
        }

        $parsedUrl = parse_url((string) session('redirect_uri'));
        parse_str($parsedUrl['query'], $queryParams);

        if ( ! str_contains($queryParams['redirect_uri'], (string) config('app.gw_url'))) {
            return;
        }

        $tokens = Token::where('client_id', $queryParams['client_id'])
            ->where('name', null)
            ->where('user_id', auth()->id())
            ->where('revoked', false)
            ->get();

        // Revoke all GW token and its refresh tokens
        foreach ($tokens as $token) {
            $token->revoke();
            $token->refreshToken?->revoke();
        }
    }

    /**
     * Revoke all tokens, logout the user and remove their session data.
     *
     * This is a convenience method that revokes all tokens, logs the user out and removes all session
     * data for the user.
     */
    public function revokeAll(): void
    {
        $userId = auth()->id();

        $this->revokeAllTokens(userId: $userId);

        $this->revokeAllSessions($userId);

        Auth::guard()->logout();

        Token::where('user_id', $userId)->update(['revoked' => true]);
        DB::table('sessions')->where('user_id', $userId)->delete();
        OAuthSession::where('user_id', $userId)->delete();
    }
}
