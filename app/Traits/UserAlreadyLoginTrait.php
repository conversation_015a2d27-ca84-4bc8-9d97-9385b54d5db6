<?php

namespace App\Traits;

use App\Models\Passport\Token;
use Illuminate\Support\Facades\DB;

trait UserAlreadyLoginTrait
{
    public const PRO_PLAN = 3;
    public const CUSTOMIZED_PLAN = 11;

    /**
     * Checks if the user is already logged in to the same client.
     * If the user is already logged in, it checks if the user is on a pro or customized plan.
     * If the user is not on a pro or customized plan and is already logged in, it returns an error message.
     *
     * @return bool
     */
    public function isUserAlreadyLogin()
    {
        return $this->getLoggedInDevicesCount() > 0 &&
            ! in_array(auth()->user()->subscription->plan_id, [self::PRO_PLAN, self::CUSTOMIZED_PLAN]);
    }

    /**
     * Get the count of logged-in devices for the current user.
     *
     * @return int The total count of logged-in devices
     */
    public function getLoggedInDevicesCount()
    {
        $userId = auth()->id();

        // Count active OAuth tokens (API sessions)
        $tokenCount = Token::where('user_id', $userId)
            ->where('name', null)
            ->where('revoked', false)
            ->count();

        // Count web sessions
        $sessionCount = 0;
        if ($tokenCount > 0) {
            $sessionCount = DB::table('sessions')->where('user_id', $userId)->count();
        }

        return $tokenCount + $sessionCount;
    }
}
