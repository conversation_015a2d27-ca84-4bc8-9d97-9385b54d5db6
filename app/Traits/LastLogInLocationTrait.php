<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON><PERSON>\Location\Facades\Location;

trait LastLogInLocationTrait
{
    /**
     * Updates the last login location and IP address of the authenticated user.
     *
     * This method retrieves the user's current IP address and uses it to determine
     * their location. It then updates the user's record with their login status,
     * last known IP address, and country location.
     *
     * @return void
     */
    public function updateLastLogInLocation()
    {
        try {
            $user = Auth::user();

            $ipAddress = request()->ip();
            $location = Location::get($ipAddress);

            $user->is_logged_in = 1;
            $user->last_ip_address = $ipAddress;
            $user->last_location = $location?->countryName ?? "";
            $user->save();
        } catch (\Exception $e) {
            Log::error('Failed to update last login location', [$e->getMessage()]);
        }
    }
}
