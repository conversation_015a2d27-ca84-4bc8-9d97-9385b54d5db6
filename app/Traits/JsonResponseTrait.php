<?php

namespace App\Traits;

use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Throwable;

trait JsonResponseTrait
{
    // These URLs will return JSON responses
    private const RETURN_JSON = ['api'];
    private const MESSAGE = 'Not Found';
    private const TYPE = 'error';

    /**
     * Response for success.
     *
     * @param mixed|null  $data
     * @param string|null $message
     * @param int         $statusCode
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function successResponse($data = null, $message = null, $statusCode = Response::HTTP_OK)
    {
        return response()->json([
            'status' => true,
            'data' => $data,
            'message' => $message,
        ], $statusCode);
    }

    /**
     * Response for failure.
     *
     * @param mixed|null  $data
     * @param string|null $message
     * @param int|null    $statusCode
     * @param string|null $type
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function errorResponse($data = null, $message = self::MESSAGE, $statusCode = Response::HTTP_INTERNAL_SERVER_ERROR, $type = self::TYPE)
    {
        return response()->json(data: [
            'status' => false,
            'data' => ! empty($data) ? $data : null,
            'message' => $message,
            'type' => $type,
        ], status: $statusCode);
    }

    public function isJsonRequest(): bool
    {
        return request()->expectsJson() || request()->ajax() || Str::startsWith(request()->path(), self::RETURN_JSON);
    }

    /**
     * Generates a response for an exception.
     *
     * @param $exception the exception to generate the response for
     *
     * @return array the generated response as an associative array
     */
    public function getResponse($exception)
    {
        $statusCode = $this->isJsonRequest() ? Response::HTTP_UNAUTHORIZED : Response::HTTP_INTERNAL_SERVER_ERROR;
        $errorMessage = $this->isJsonRequest()
            ? 'Unauthorized error'
            : 'Something went wrong';

        if (app()->environment(['local', 'development'])) {
            $errorMessage = $exception->getMessage() ?? $errorMessage;
        }

        return [
            'message' => $errorMessage ?: 'Unauthorized error',
            'status' => method_exists($exception, 'getStatusCode') ? $exception->getStatusCode() : $statusCode,
        ];
    }

    /**
     * Return the appropriate response based on the given Throwable and request type.
     *
     * @param \Throwable $exception the throwable object
     * @param array      $response  the response array
     *
     * @return mixed the appropriate response based on the request type and throwable
     */
    public function returnResponse(\Throwable $exception, $response)
    {
        if ($this->isJsonRequest()) {
            return $this->errorResponse(null, $response['message'], $response['status']);
        }

        return $this->handleNonApiRequest($exception, $response);
    }

    /**
     * Handles non-API requests.
     *
     * @param       $exception the throwable object
     * @param array $response  the response array
     *
     * @return \Illuminate\Routing\Redirector|\Illuminate\Http\RedirectResponse
     */
    public function handleNonApiRequest($exception, $response)
    {
        if (method_exists($exception, 'redirectTo')) {
            return redirect($exception->redirectTo())->with('error', $response['message']);
        }

        return back()->with('error', $response['message']);
    }
}
