<?php

namespace App\Traits;

use App\Models\User;
use Illuminate\Support\Facades\Log;
use MailerLite\MailerLite;

trait MailerLiteRegisterTrait
{
    public const GROUP_ID = 94835880713258212;

    public function registerMailerLite(User $user, string $plan = 'free')
    {
        if ($user->mailerlite_subscriber_id) {
            return;
        }

        try {
            $mailerLite = new MailerLite(['api_key' => config('services.mailer_lite_api_key')]);
            $data = [
                'email' => $user->email,
                'fields' => [
                    'name' => $user->name,
                    'subscription_status' => $plan,
                ],
            ];

            $response = $mailerLite->subscribers->create($data);
            if ( ! isset($response['body']['data']['id'])) {
                Log::error('Failed to create subscriber', $response);
            }

            $subscriberId = $response['body']['data']['id'] ?? null;
            $user->mailerlite_subscriber_id = $subscriberId;
            $user->save();

            $groupResponse = $mailerLite->groups->assignSubscriber(self::GROUP_ID, $subscriberId);

            if ($groupResponse['status_code'] !== 200) {
                Log::error('Failed to assign subscriber to group', [$groupResponse]);
            } else {
                Log::error('Success to assign subscriber to group', [$groupResponse]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to create subscriber', [$e->getMessage()]);

            return;
        }
    }
}
