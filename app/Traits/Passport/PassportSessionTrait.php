<?php

namespace App\Traits\Passport;

use App\Models\Passport\OAuthSession;
use Illuminate\Http\Response;
use League\OAuth2\Server\Exception\OAuthServerException;

trait PassportSessionTrait
{
    /**
     * Store the user agent and IP address of the request.
     *
     * @param \Illuminate\Http\Response $response
     *
     * @return void
     */
    public function storeAgent(Response $response): void
    {
        $location = $response->headers->get('Location');

        $queryString = parse_url((string) $location, PHP_URL_QUERY);
        parse_str($queryString, $params);

        OAuthSession::create([
            'ip_address' => request()->getClientIp(),
            'user_agent' => request()->userAgent(),
            'code' => $params['code'],
        ]);
    }

    /**
     * Store the access token against the authorization session.
     *
     * @param \Illuminate\Http\Response $response
     *
     * @throws \League\OAuth2\Server\Exception\OAuthServerException
     *
     * @return void
     */
    public function updateSession(Response $response): void
    {
        $body = json_decode($response->content(), true);

        if ( ! isset($body['access_token'])) {
            throw OAuthServerException::serverError('Access token not generated by the server');
        }

        OAuthSession::where('code', request()->code)->update(['token' => base64_encode((string) $body['access_token'])]);
    }
}
