<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Subscription extends Model
{
    use SoftDeletes;
    use HasFactory;

    public const PAYMENT_FREQUENCY_SELECT = [
        'monthly' => 'Monthly',
        'yearly'  => 'Yearly',
    ];

    public const STATUS_SELECT = [
        'pending'  => 'Pending',
        'active'   => 'Active',
        'expired'  => 'Expired',
        'canceled' => 'Canceled',
    ];

    public $table = 'subscriptions';

    protected $casts = [
        'start_at',
        'end_at',
        'canceled_at',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $fillable = [
        'user_id',
        'plan_id',
        'start_at',
        'end_at',
        'canceled_at',
        'status',
        'cancelled_reason',
        'payment_frequency',
        'multiprompt_company_usage',
        'image_bg_remover_usage',
        'template_run_usage',
        'pp_subscription',
        'image_bg_remover_usage',
        'stripe_subscription',
        'razorpay_subscription',
        'chat_usage',
        'image_prompt_usage',
        'stock_img_usage',
        'credits',
        'add_on_plan',
        'add_on_total_credit',
        'add_on_word_usage',
        'add_on_image_usage',
        'add_on_voice_usage',
        'plan_credits_id',
        'plan_total_credit',
        'plan_word_usage',
        'plan_image_usage',
        'plan_voice_usage',
        'usage',
        'long_blog_usage',
        'long_blog_image_usage',
        'image_usage',
        'created_at',
        'updated_at',
        'deleted_at',
        'is_react',
        'last_reset_date',
        'reset_expiry_date',
        'is_admin',
        'is_manual',
        'site_import_usage',
        'storyboard_usage',
        'storyboard_image_usage',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function getHasReachedLimitAttribute()
    {
        if (in_array($this->plan_id, [1, 2])) {
            return ($this->usage + $this->chat_usage) >= $this->plan->word_limit;
        }
        return $this->usage >= $this->plan->word_limit;

    }

    public function getHasReachedImageLimitAttribute()
    {
        return $this->image_usage >= $this->plan->image_limit;
    }

    public function getTableNameAttribute()
    {
        return $this->attributes['tableName'] = $this->getTable();
    }
}
