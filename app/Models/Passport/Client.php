<?php

namespace App\Models\Passport;

use <PERSON><PERSON>\Passport\Client as PassportClient;

class Client extends PassportClient
{
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];
    protected $fillable = [
        'user_id',
        'name',
        'secret',
        'provider',
        'domain',
        'redirect',
        'personal_access_client',
        'password_client',
        'revoked',
    ];

    /**
     * Determine if the client should skip the authorization prompt.
     *
     * @return bool
     */
    #[\Override]
    public function skipsAuthorization()
    {
        return true; // This will skip the authorization prompt for all clients
    }
}
