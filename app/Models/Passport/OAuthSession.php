<?php

namespace App\Models\Passport;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OAuthSession extends Model
{
    /** @use HasFactory<\Database\Factories\OAuthSessionFactory> */
    use HasFactory;

    protected $table = 'oauth_sessions';

    protected $fillable = [
        'user_id',
        'oauth_access_token_id',
        'ip_address',
        'user_agent',
        'code',
    ];
}
