<?php

namespace App\OAuth;

use League\OAuth2\Server\Entities\ClientEntityInterface;
use League\OAuth2\Server\Exception\OAuthServerException;
use League\OAuth2\Server\Grant\AuthCodeGrant;
use League\OAuth2\Server\RedirectUriValidators\RedirectUriValidatorInterface;
use League\OAuth2\Server\Repositories\AuthCodeRepositoryInterface;
use League\OAuth2\Server\Repositories\RefreshTokenRepositoryInterface;
use League\OAuth2\Server\RequestEvent;
use Psr\Http\Message\ServerRequestInterface;

/**
 * CustomAuthCodeGrant - Custom implementation of AuthCodeGrant that uses our WildcardRedirectUriValidator.
 *
 * This class extends the default OAuth2 server AuthCodeGrant to use our custom
 * WildcardRedirectUriValidator for validating redirect URIs during the OAuth authorization process.
 *
 * @package App\OAuth
 */
class CustomAuthCodeGrant extends AuthCodeGrant
{
    /**
     * @var RedirectUriValidatorInterface
     */
    protected $redirectUriValidator;

    /**
     * Create a new auth code grant instance.
     *
     * @param AuthCodeRepositoryInterface     $authCodeRepository
     * @param RefreshTokenRepositoryInterface $refreshTokenRepository
     * @param \DateInterval                    $authCodeTTL
     * @param RedirectUriValidatorInterface   $redirectUriValidator
     */
    public function __construct(
        AuthCodeRepositoryInterface $authCodeRepository,
        RefreshTokenRepositoryInterface $refreshTokenRepository,
        \DateInterval $authCodeTTL,
        RedirectUriValidatorInterface $redirectUriValidator,
    ) {
        parent::__construct($authCodeRepository, $refreshTokenRepository, $authCodeTTL);
        $this->redirectUriValidator = $redirectUriValidator;
    }

    /**
     * Validate the redirect URI.
     *
     * This method overrides the parent method to use our custom WildcardRedirectUriValidator.
     *
     * @param string                 $redirectUri
     * @param ClientEntityInterface  $client
     * @param ServerRequestInterface $request
     *
     * @throws OAuthServerException
     */
    #[\Override]
    protected function validateRedirectUri(
        string $redirectUri,
        ClientEntityInterface $client,
        ServerRequestInterface $request,
    ) {
        // Create a validator instance with the client's allowed redirect URIs
        $validator = app()->makeWith(RedirectUriValidatorInterface::class, [$client->getRedirectUri()]);

        // Validate the redirect URI using our custom validator
        if ( ! $validator->validateRedirectUri($redirectUri)) {
            $this->getEmitter()->emit(new RequestEvent(RequestEvent::CLIENT_AUTHENTICATION_FAILED, $request));
            throw OAuthServerException::invalidClient($request);
        }
    }
}
