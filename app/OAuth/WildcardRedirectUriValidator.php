<?php

namespace App\OAuth;

use League\OAuth2\Server\RedirectUriValidators\RedirectUriValidatorInterface;
use League\Uri\Exceptions\SyntaxError;
use League\Uri\Uri;

/**
 * WildcardRedirectUriValidator - Custom implementation of RedirectUriValidator that supports wildcard patterns.
 *
 * This validator implements the RedirectUriValidatorInterface to add support for wildcard patterns
 * in redirect URIs, allowing a single client to accept redirects from multiple dynamic subdomains
 * without having to register each one individually.
 *
 * Supported wildcard patterns:
 * - *.example.com - Matches any subdomain of example.com
 * - https://*.ngrok-free.app/callback - Matches any ngrok subdomain with the specified path
 *
 * @package App\OAuth
 */
class WildcardRedirectUriValidator implements RedirectUriValidatorInterface
{
    /**
     * @var array
     */
    private $allowedRedirectUris;

    /**
     * New validator instance for the given uri
     *
     * @param string|array $allowedRedirectUri
     */
    public function __construct($allowedRedirectUri)
    {
        if (\is_string($allowedRedirectUri)) {
            $this->allowedRedirectUris = [$allowedRedirectUri];
        } elseif (\is_array($allowedRedirectUri)) {
            $this->allowedRedirectUris = $allowedRedirectUri;
        } else {
            $this->allowedRedirectUris = [];
        }
    }

    /**
     * Validates the redirect uri.
     *
     * @param string $redirectUri
     *
     * @return bool Return true if valid, false otherwise
     */
    public function validateRedirectUri($redirectUri)
    {
        // First check for loopback URIs (127.0.0.1 or [::1])
        if ($this->isLoopbackUri($redirectUri)) {
            return $this->matchUriExcludingPort($redirectUri);
        }

        // Then check for exact match
        if ($this->matchExactUri($redirectUri)) {
            return true;
        }

        // Finally check for wildcard match
        return $this->matchWildcardUri($redirectUri);
    }

    /**
     * Check if the redirect URI is a loopback URI (127.0.0.1 or [::1])
     *
     * @param string $redirectUri
     * @return bool
     */
    private function isLoopbackUri($redirectUri)
    {
        try {
            $uri = Uri::new($redirectUri);
            return $uri->getScheme() === 'http'
                && (\in_array($uri->getHost(), ['127.0.0.1', '[::1]'], true));
        } catch (SyntaxError) {
            return false;
        }
    }

    /**
     * Find an exact match among allowed URIs
     *
     * @param string $redirectUri
     * @return bool
     */
    private function matchExactUri($redirectUri)
    {
        return \in_array($redirectUri, $this->allowedRedirectUris, true);
    }

    /**
     * Match a URI excluding the port number (for loopback URIs)
     *
     * @param string $redirectUri
     * @return bool
     */
    private function matchUriExcludingPort($redirectUri)
    {
        $parsedUrl = $this->parseUrlAndRemovePort($redirectUri);

        foreach ($this->allowedRedirectUris as $allowedRedirectUri) {
            if ($parsedUrl === $this->parseUrlAndRemovePort($allowedRedirectUri)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Parse a URL and remove the port
     *
     * @param string $url
     * @return string
     */
    private function parseUrlAndRemovePort($url)
    {
        try {
            $uri = Uri::new($url);
            return (string) $uri->withPort(null);
        } catch (SyntaxError) {
            return '';
        }
    }

    /**
     * Check if the redirect URI matches any of the allowed wildcard patterns
     *
     * @param string $redirectUri
     * @return bool
     */
    private function matchWildcardUri($redirectUri)
    {
        try {
            $uri = Uri::new($redirectUri);
            $redirectHost = $uri->getHost();
            $redirectPath = $uri->getPath();
            $redirectScheme = $uri->getScheme();
        } catch (SyntaxError) {
            return false;
        }

        foreach ($this->allowedRedirectUris as $allowedUri) {
            // Check if the allowed URI contains a wildcard
            if ( ! str_contains((string) $allowedUri, '*')) {
                continue;
            }

            try {
                // Handle wildcard in domain
                if (str_contains((string) $allowedUri, '://*.')) {
                    // Extract the pattern parts
                    $allowedUri = Uri::new(str_replace('*.', 'wildcard-placeholder.', $allowedUri));
                    $allowedHost = $allowedUri->getHost();
                    $allowedPath = $allowedUri->getPath();
                    $allowedScheme = $allowedUri->getScheme();

                    // Replace the wildcard placeholder
                    $domainPattern = str_replace('wildcard-placeholder.', '', $allowedHost);

                    // Check if the redirect URI matches the pattern
                    if ($redirectScheme === $allowedScheme &&
                        $this->matchesDomainPattern($redirectHost, $domainPattern) &&
                        $this->matchesPathPattern($redirectPath, $allowedPath)) {
                        return true;
                    }
                }
                // Handle just *.domain.com format without scheme
                elseif (str_starts_with((string) $allowedUri, '*.')) {
                    $domainPattern = substr((string) $allowedUri, 2); // Remove the *. prefix
                    if ($this->matchesDomainPattern($redirectHost, $domainPattern)) {
                        return true;
                    }
                }
            } catch (SyntaxError) {
                continue;
            }
        }

        return false;
    }

    /**
     * Check if a domain matches a wildcard pattern
     *
     * @param string $domain
     * @param string $pattern
     * @return bool
     */
    private function matchesDomainPattern($domain, $pattern)
    {
        // For wildcard subdomains, we need a subdomain, not just the base domain
        // So example.com should not match *.example.com
        if ($domain === $pattern) {
            return false;
        }

        // Check if the domain ends with .pattern (e.g., sub.example.com ends with .example.com)
        return substr($domain, -strlen($pattern) - 1) === '.' . $pattern;
    }

    /**
     * Check if a path matches the allowed path pattern
     *
     * @param string $path
     * @param string $allowedPath
     * @return bool
     */
    private function matchesPathPattern($path, $allowedPath)
    {
        // If allowed path is empty or /, any path is allowed
        if (empty($allowedPath) || $allowedPath === '/') {
            return true;
        }

        // Otherwise, the path must match exactly or be a subpath
        return $path === $allowedPath || str_starts_with($path, $allowedPath . '/');
    }
}
