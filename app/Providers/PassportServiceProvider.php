<?php

namespace App\Providers;

use App\Models\Passport\AuthCode;
use App\Models\Passport\Client;
use App\Models\Passport\PersonalAccessClient;
use App\Models\Passport\RefreshToken;
use App\Models\Passport\Token;
use App\Repositories\CustomClientRepository;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\ServiceProvider;
use Laravel\Passport\Bridge\ClientRepository as PassportClientRepository;
use Laravel\Passport\Passport;

/**
 * PassportServiceProvider - Custom implementation of Laravel Passport OAuth2 server.
 *
 * @package Passport
 *
 * @see https://laravel.com/docs/11.x/passport Laravel Passport Documentation
 * @see Laravel\Passport\PassportServiceProvider Passport Service Provider
 * @see League\OAuth2\Server\AuthorizationServer OAuth2 Server Implementation
 **/
class PassportServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    #[\Override]
    public function register(): void
    {
        // Bind the StatefulGuard interface to the web guard
        $this->app->bind(StatefulGuard::class, fn() => Auth::guard('web'));

        // Bind the ClientRepository interface to our custom implementation
        // This allows us to override the client validation logic
        $this->app->bind(PassportClientRepository::class, CustomClientRepository::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Disable default Passport routes
        Passport::ignoreRoutes();

        // Configure Passport
        Passport::tokensExpireIn(now()->addDays(15));
        Passport::refreshTokensExpireIn(now()->addDays(30));
        Passport::enableImplicitGrant();
        Passport::hashClientSecrets();

        // Use our custom models
        Passport::useTokenModel(Token::class);
        Passport::useRefreshTokenModel(RefreshToken::class);
        Passport::useAuthCodeModel(AuthCode::class);
        Passport::useClientModel(Client::class);
        Passport::usePersonalAccessClientModel(PersonalAccessClient::class);
    }
}
