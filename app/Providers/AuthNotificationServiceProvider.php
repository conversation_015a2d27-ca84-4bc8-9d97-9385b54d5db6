<?php

namespace App\Providers;

use App\Notifications\Auth\ResetPassword;
use Illuminate\Auth\Notifications\ResetPassword as BaseResetPassword;
use Illuminate\Support\ServiceProvider;

class AuthNotificationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    #[\Override]
    public function register(): void {}

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Override the default ResetPassword notification
        $this->app->bind(BaseResetPassword::class, ResetPassword::class);
    }
}
