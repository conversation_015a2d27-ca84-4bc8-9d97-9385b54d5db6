<?php

namespace App\Providers;

use App\OAuth\CustomAuthCodeGrant;
use App\OAuth\WildcardRedirectUriValidator;
use Illuminate\Support\ServiceProvider;
use Lara<PERSON>\Passport\Bridge\AuthCodeRepository;
use <PERSON><PERSON>\Passport\Bridge\RefreshTokenRepository;
use League\OAuth2\Server\AuthorizationServer;
use League\OAuth2\Server\Grant\AuthCodeGrant;
use League\OAuth2\Server\RedirectUriValidators\RedirectUriValidator;
use League\OAuth2\Server\RedirectUriValidators\RedirectUriValidatorInterface;

/**
 * OAuthServiceProvider - Registers custom OAuth components.
 *
 * This service provider registers our custom OAuth components, such as
 * the WildcardRedirectUriValidator, to override the default behavior
 * of Laravel Passport.
 *
 * @package App\Providers
 */
class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    #[\Override]
    public function register(): void
    {
        // Bind our custom WildcardRedirectUriValidator to the RedirectUriValidatorInterface
        // This will make the OAuth2 server use our validator instead of the default one
        $this->app->bind(RedirectUriValidatorInterface::class, function ($app, $parameters) {
            // If parameters are provided (the allowed redirect URIs), use them
            if (isset($parameters[0])) {
                return new WildcardRedirectUriValidator($parameters[0]);
            }

            // Otherwise, create a default instance
            return new WildcardRedirectUriValidator([]);
        });

        // Also bind to the concrete class in case it's directly instantiated
        $this->app->bind(RedirectUriValidator::class, function ($app, $parameters) {
            if (isset($parameters[0])) {
                return new WildcardRedirectUriValidator($parameters[0]);
            }

            return new WildcardRedirectUriValidator([]);
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Extend the AuthorizationServer to use our custom AuthCodeGrant
        $this->app->extend(AuthorizationServer::class, function (AuthorizationServer $server) {
            // Get the required repositories
            $authCodeRepository = $this->app->make(AuthCodeRepository::class);
            $refreshTokenRepository = $this->app->make(RefreshTokenRepository::class);

            // Create a redirect URI validator
            $redirectUriValidator = $this->app->make(RedirectUriValidatorInterface::class);

            // Create our custom auth code grant
            $grant = new CustomAuthCodeGrant(
                $authCodeRepository,
                $refreshTokenRepository,
                new \DateInterval('PT10M'), // Authorization codes will expire after 10 minutes
                $redirectUriValidator,
            );

            // Enable PKCE
            $grant->disableRequireCodeChallengeForPublicClients();

            // Add the grant to the server
            $server->enableGrantType($grant, new \DateInterval('PT1H')); // Access tokens will expire after 1 hour

            return $server;
        });
    }
}
