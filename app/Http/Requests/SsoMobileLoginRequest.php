<?php

namespace App\Http\Requests;

use App\Abstracts\JsonRequest;

class SsoMobileLoginRequest extends JsonRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'access_token' => 'required|string',
        ];
    }
}
