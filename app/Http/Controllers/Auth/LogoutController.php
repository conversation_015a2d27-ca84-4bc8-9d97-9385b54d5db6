<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Passport\Client;
use App\Traits\JsonResponseTrait;
use App\Traits\Passport\PassportRedirectTrait;
use App\Traits\UserLogoutTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Laravel\Passport\PersonalAccessTokenFactory;

class LogoutController extends Controller
{
    use JsonResponseTrait;
    use PassportRedirectTrait;
    use UserLogoutTrait;

    /**
     * Logout the user from all devices and revoke all tokens.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function logoutAll(Request $request): RedirectResponse
    {
        $request->validate([
            'access_token' => 'required',
            'client_id' => 'required',
            'redirect_uri' => 'required|url',
        ]);

        $client = Client::where('id', $request->client_id)->first();
        if ( ! $client) {
            abort(401, 'Client not found');
        }

        $accessToken = app(PersonalAccessTokenFactory::class)->findAccessToken(['access_token' => $request->access_token]);

        if (in_array($accessToken?->name, ['impersonate-token', 'wp_api_token'])) {
            $accessToken->revoke();

            return redirect($request->redirect_uri);
        }

        $userId = $this->revokeAllTokens($request->access_token);

        $this->revokeAllSessions($userId ?? auth()->id);

        return redirect($request->redirect_uri);
    }
}
