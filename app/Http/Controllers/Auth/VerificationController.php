<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Traits\LastLogInLocationTrait;
use App\Traits\MailerLiteRegisterTrait;
use App\Traits\Passport\PassportRedirectTrait;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\VerifiesEmails;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VerificationController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Email Verification Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling email verification for any
    | user that recently registered with the application. Emails may also
    | be re-sent if the user didn't receive the original email message.
    |
    */

    use VerifiesEmails;
    use PassportRedirectTrait;
    use MailerLiteRegisterTrait;
    use LastLogInLocationTrait;

    /**
     * Where to redirect users after verification.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // $this->middleware('auth');
        // $this->middleware('signed')->only('verify');
        // $this->middleware('throttle:6,1')->only('verify', 'resend');
    }

    /**
     * Show the email verification notice.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    public function show(Request $request)
    {
        $user = User::find(session()->get('verify_user_id'));
        if (auth()->check()) {
            $user = auth()->user();
        }

        return $user->hasVerifiedEmail()
            ? redirect($this->redirectPath())
            : view('auth.verify');
    }

    public function verify(Request $request, $id = null)
    {
        $user = User::find($id);

        Auth::login($user, true);

        $params = $request->except(['expires', 'signature', 'redirect_url', 'state']);
        $url = $request->redirect_url . "&" . http_build_query($params) . "&scope=&state=" . $request->state;

        if ( ! hash_equals((string) $request->route('id'), (string) $user->getKey())) {
            throw new AuthorizationException();
        }

        if ( ! hash_equals((string) $request->route('hash'), sha1((string) $user->getEmailForVerification()))) {
            throw new AuthorizationException();
        }

        if ($user->hasVerifiedEmail()) {
            return $request->wantsJson()
                ? new JsonResponse([], 204)
                : (
                    ($url)
                    ? redirect($url)
                    : redirect($this->redirectPath())
                );
        }

        if ($user->markEmailAsVerified()) {
            event(new Verified($user));
            $this->updateLastLogInLocation();
            $this->registerMailerLite($user);
        }

        $user->update(['verified' => 1, 'verified_at' => now()]);

        if ($url) {
            return redirect($url);
        }

        return $request->wantsJson()
            ? new JsonResponse([], 204)
            : redirect($this->redirectPath())->with('verified', true);
    }

    /**
     * Resend the email verification notification.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function resend(Request $request)
    {
        $user = User::find(session()->get('verify_user_id'));

        if ($user->hasVerifiedEmail()) {
            return $request->wantsJson()
                ? new JsonResponse([], 204)
                : redirect($this->redirectPath());
        }

        $user->sendEmailVerificationNotification();

        return $request->wantsJson()
            ? new JsonResponse([], 202)
            : back()->with('resent', true);
    }
}
