<?php

namespace App\Http\Controllers\Auth;

use App\Traits\Passport\PassportSessionTrait;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Passport\Http\Controllers\ApproveAuthorizationController as PassportApproveAuthorizationController;
use League\OAuth2\Server\AuthorizationServer;
use <PERSON>yholm\Psr7\Response as Psr7Response;

class ApproveAuthorizationController extends PassportApproveAuthorizationController
{
    use PassportSessionTrait;

    /**
     * Create a new controller instance.
     *
     * @param  \League\OAuth2\Server\AuthorizationServer  $server
     * @return void
     */
    public function __construct(AuthorizationServer $server)
    {
        $this->server = $server;
    }

    /**
     * Approve the authorization request.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return \Illuminate\Http\Response
     */
    #[\Override]
    public function approve(Request $request)
    {
        $this->assertValidAuthToken($request);

        $authRequest = $this->getAuthRequestFromSession($request);

        $authRequest->setAuthorizationApproved(true);

        return $this->withErrorHandling(function () use ($authRequest) {
            $response = $this->convertResponse(
                $this->server->completeAuthorizationRequest($authRequest, new Psr7Response()),
            );

            $this->storeAgent($response);

            return $response;
        });
    }
}
