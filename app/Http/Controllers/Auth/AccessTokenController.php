<?php

namespace App\Http\Controllers\Auth;

use App\Traits\Passport\PassportSessionTrait;
use <PERSON><PERSON>\Passport\Http\Controllers\AccessTokenController as PassportAccessTokenController;
use League\OAuth2\Server\Exception\OAuthServerException;
use <PERSON>yholm\Psr7\Response as Psr7Response;
use Psr\Http\Message\ServerRequestInterface;

class AccessTokenController extends PassportAccessTokenController
{
    use PassportSessionTrait;

    /**
     * @inheritdoc
     */
    #[\Override]
    public function issueToken(ServerRequestInterface $request)
    {
        return $this->withErrorHandling(function () use ($request) {
            $response = $this->convertResponse(
                $this->server->respondToAccessTokenRequest($request, new Psr7Response()),
            );

            $this->updateSession($response);

            return $response;
        });
    }

    /**
     * Refresh an access token.
     *
     * @param  \Psr\Http\Message\ServerRequestInterface  $request
     * @return \Illuminate\Http\Response
     */
    public function refresh(ServerRequestInterface $request)
    {
        $parsedBody = $request->getParsedBody();

        // Ensure refresh token is present
        if ( ! isset($parsedBody['refresh_token'])) {
            throw OAuthServerException::invalidRequest('refresh_token');
        }

        // Add grant type to request
        $parsedBody['grant_type'] = 'refresh_token';
        $request = $request->withParsedBody($parsedBody);

        return $this->withErrorHandling(function () use ($request) {
            $response = $this->convertResponse(
                $this->server->respondToAccessTokenRequest($request, new Psr7Response()),
            );

            $this->updateSession($response);

            return $response;
        });
    }
}
