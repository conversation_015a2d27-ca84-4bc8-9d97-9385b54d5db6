<?php

namespace App\Http\Controllers\Auth;

use App\Traits\Passport\PassportSessionTrait;
use <PERSON><PERSON>\Passport\Http\Controllers\DenyAuthorizationController as PassportDenyAuthorizationController;
use League\OAuth2\Server\AuthorizationServer;

class DenyAuthorizationController extends PassportDenyAuthorizationController
{
    use PassportSessionTrait;

    /**
     * Create a new controller instance.
     *
     * @param  \League\OAuth2\Server\AuthorizationServer  $server
     * @return void
     */
    public function __construct(AuthorizationServer $server)
    {
        $this->server = $server;
    }
}
