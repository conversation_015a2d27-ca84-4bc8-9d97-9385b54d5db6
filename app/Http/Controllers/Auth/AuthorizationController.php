<?php

namespace App\Http\Controllers\Auth;

use App\Traits\Passport\PassportRedirectTrait;
use App\Traits\Passport\PassportSessionTrait;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Passport\Bridge\User;
use Lara<PERSON>\Passport\ClientRepository;
use Lara<PERSON>\Passport\Contracts\AuthorizationViewResponse;
use <PERSON><PERSON>\Passport\Http\Controllers\AuthorizationController as PassportAuthorizationController;
use Lara<PERSON>\Passport\TokenRepository;
use League\OAuth2\Server\AuthorizationServer;
use Nyholm\Psr7\Response as Psr7Response;
use Psr\Http\Message\ServerRequestInterface;

class AuthorizationController extends PassportAuthorizationController
{
    use PassportSessionTrait;
    use PassportRedirectTrait;

    /**
     * Create a new controller instance.
     *
     * @param  \League\OAuth2\Server\AuthorizationServer  $server
     * @param  \Illuminate\Contracts\Auth\StatefulGuard  $guard
     * @param  \Laravel\Passport\Contracts\AuthorizationViewResponse  $response
     * @return void
     */
    public function __construct(
        AuthorizationServer $server,
        StatefulGuard $guard,
        AuthorizationViewResponse $response,
    ) {
        $this->server = $server;
        $this->guard = $guard;
        $this->response = $response;
    }


    /**
     * Authorize a client to access the user's account.
     *
     * @param  \Psr\Http\Message\ServerRequestInterface  $psrRequest
     * @param  \Illuminate\Http\Request  $request
     * @param  \Laravel\Passport\ClientRepository  $clients
     * @param  \Laravel\Passport\TokenRepository  $tokens
     * @return \Illuminate\Http\Response|\Laravel\Passport\Contracts\AuthorizationViewResponse
     */
    #[\Override]
    public function authorize(
        ServerRequestInterface $psrRequest,
        Request $request,
        ClientRepository $clients,
        TokenRepository $tokens,
    ) {
        // Persist OAuth parameters before session regeneration to prevent data loss
        $this->persistOAuthParams();

        // Instead of flush(), which destroys the session completely,
        // use forget() to clear specific keys or regenerate() to create a new session ID
        session()->regenerate(true); // true means delete the old session
        session()->put('is_first_time', true);

        return parent::authorize($psrRequest, $request, $clients, $tokens);
    }

    /**
     * Approve the authorization request.
     *
     * @param \League\OAuth2\Server\RequestTypes\AuthorizationRequest $authRequest
     * @param \Illuminate\Contracts\Auth\Authenticatable              $user
     *
     * @return \Illuminate\Http\Response
     */
    #[\Override]
    protected function approveRequest($authRequest, $user)
    {
        $authRequest->setUser(new User($user->getAuthIdentifier()));

        $authRequest->setAuthorizationApproved(true);

        return $this->withErrorHandling(function () use ($authRequest) {
            $response = $this->convertResponse(
                $this->server->completeAuthorizationRequest($authRequest, new Psr7Response()),
            );

            $this->storeAgent($response);

            return $response;
        });
    }
}
