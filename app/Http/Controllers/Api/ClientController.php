<?php

namespace App\Http\Controllers\Api;

use App\Models\Passport\Client;
use App\Traits\JsonResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Laravel\Passport\ClientRepository;
use <PERSON><PERSON>\Passport\Http\Controllers\ClientController as ControllersClientController;

class ClientController extends ControllersClientController
{
    use JsonResponseTrait;

    /**
     * Get all of the clients for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    #[\Override]
    public function forUser(Request $request)
    {
        $clients = Client::with('user')->get();

        return $this->successResponse($clients, 'Clients fetched successfully.');
    }

    /**
     * Store a new client.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    #[\Override]
    public function store(Request $request)
    {
        $this->validation->make($request->all(), [
            'name' => 'required|max:191',
            'domain' => 'required|string',
            'redirect' => ['required', $this->redirectRule],
        ])->validate();

        $client = (new ClientRepository())->createPersonalAccessClient(
            $request->user_id, // User id
            $request->name, // Name of the oauth client
            $request->redirect, // Redirect of the oauth client
        );

        $client->update(['domain' => $request->domain]);

        return $this->successResponse($client, 'Client created successfully.');
    }

    /**
     * Update the given client.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $clientId
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($clientId)
    {
        $client =  Client::find($clientId);

        return $this->successResponse($client, 'Client fetched successfully.');
    }

    /**
     * Update the given client.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $clientId
     *
     * @return \Illuminate\Http\JsonResponse
     */
    #[\Override]
    public function update(Request $request, $clientId)
    {
        $client =  Client::find($clientId);

        if ( ! $client) {
            return $this->errorResponse(null, 'Client not found.', Response::HTTP_NOT_FOUND);
        }

        $this->validation->make($request->all(), [
            'name' => 'required|max:191',
            'domain' => 'required|string',
            'redirect' => ['required', $this->redirectRule],
        ])->validate();

        $client->update([
            'name' => $request->name,
            'domain' => $request->domain,
            'redirect' => $request->redirect,
        ]);

        return $this->successResponse($client, 'Client updated successfully.');
    }

    /**
     * Delete the given client.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $clientId
     *
     * @return \Illuminate\Http\JsonResponse
     */
    #[\Override]
    public function destroy(Request $request, $clientId)
    {
        $client =  Client::find($clientId);

        if ( ! $client) {
            return $this->errorResponse(null, 'Client not found.', Response::HTTP_NOT_FOUND);
        }

        $client->delete($client);

        return $this->successResponse(null, 'Client deleted successfully.');
    }
}
