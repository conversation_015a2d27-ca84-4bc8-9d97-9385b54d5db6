name: Deploy GravityWrite SSO Application

on:
  push:
    branches: [ staging ]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          # extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql, bcmath, redis

      - name: Install Composer dependencies
        run: composer install --no-dev --optimize-autoloader

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22'

      - name: Install NPM dependencies and build assets
        run: |
          npm ci
          npm run build

      - name: Deploy to staging server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /var/www/gw_sso/
            git pull origin staging
            composer install --no-dev --optimize-autoloader
            php artisan migrate --force
            php artisan optimize
            npm ci
            npm run build
            sudo supervisorctl restart laravel-worker:*
