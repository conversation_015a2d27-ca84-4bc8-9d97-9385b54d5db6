APP_NAME=Laravel
APP_ENV=testing
APP_KEY=base64:5Z/b7bTjWrHU0neR4ylWDB48yOWdzVxD3TAWD7Har2Y=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
PHP_CLI_SERVER_WORKERS=4
BCRYPT_ROUNDS=4

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Test Database Configuration - SQLite In-Memory
DB_CONNECTION=sqlite
DB_DATABASE=:memory:
DB_FOREIGN_KEYS=true

# Session Configuration for Testing
SESSION_DRIVER=array
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# Broadcasting and Filesystem for Testing
BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local

# Queue Configuration for Testing
QUEUE_CONNECTION=sync

# Cache Configuration for Testing
CACHE_STORE=array
CACHE_PREFIX=

# Redis Configuration (not used in testing)
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration for Testing
MAIL_MAILER=array
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS Configuration (not used in testing)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Testing-specific configurations
TELESCOPE_ENABLED=false
PULSE_ENABLED=false

# OAuth/Passport Configuration for Testing
PASSPORT_PERSONAL_ACCESS_CLIENT_ID=test-client-id
PASSPORT_PERSONAL_ACCESS_CLIENT_SECRET=test-client-secret

# Social Authentication (disabled for testing)
GOOGLE_CLIENT_ID=test-google-client-id
GOOGLE_CLIENT_SECRET=test-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost/oauth/google/callback

# Email Verification
EMAIL_VERIFIER_KEY=test-email-verifier-key
MAILER_LITE_API_KEY=test-mailer-lite-key
