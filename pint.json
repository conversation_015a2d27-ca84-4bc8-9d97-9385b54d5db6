{"preset": "per", "rules": {"align_multiline_comment": true, "array_indentation": true, "array_syntax": true, "blank_line_after_namespace": true, "blank_line_after_opening_tag": true, "combine_consecutive_issets": true, "combine_consecutive_unsets": true, "concat_space": {"spacing": "one"}, "declare_parentheses": true, "declare_strict_types": false, "explicit_string_variable": true, "final_class": false, "fully_qualified_strict_types": false, "global_namespace_import": {"import_classes": false, "import_constants": false, "import_functions": false}, "is_null": false, "lambda_not_used_import": false, "logical_operators": false, "mb_str_functions": false, "method_chaining_indentation": true, "modernize_strpos": false, "new_with_braces": false, "no_empty_comment": true, "not_operator_with_space": true, "ordered_traits": false, "protected_to_private": true, "simplified_if_return": true, "strict_comparison": false, "ternary_to_null_coalescing": true, "trim_array_spaces": true, "use_arrow_functions": false, "void_return": false, "yoda_style": false, "array_push": false, "assign_null_coalescing_to_coalesce_equal": true, "explicit_indirect_variable": false, "method_argument_space": {"on_multiline": "ensure_fully_multiline"}, "modernize_types_casting": false, "no_superfluous_elseif": false, "no_useless_else": true, "nullable_type_declaration_for_default_null_value": true, "ordered_imports": {"sort_algorithm": "alpha"}, "ordered_class_elements": {"order": ["use_trait", "case", "constant", "constant_public", "constant_protected", "constant_private", "property_public", "property_protected", "property_private", "construct", "destruct", "magic", "phpunit", "method_abstract", "method_public_static", "method_public", "method_protected_static", "method_protected", "method_private_static", "method_private"], "sort_algorithm": "none"}}}