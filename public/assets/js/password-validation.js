/**
 * Password Validation Script
 * Provides real-time feedback on password strength and requirements
 */
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password-input');
    const togglePasswordButton = document.getElementById('toggle-password');
    const requirementItems = document.querySelectorAll('.requirement-item');
    const strengthMeter = document.querySelector('.password-strength-meter-bar');
    const strengthText = document.querySelector('.password-strength-text');
    const passwordRequirements = document.querySelector('.password-requirements');

    // Password validation patterns
    const validations = {
        uppercase: {
            pattern: /[A-Z]/,
            element: document.querySelector('[data-requirement="uppercase"]')
        },
        lowercase: {
            pattern: /[a-z]/,
            element: document.querySelector('[data-requirement="lowercase"]')
        },
        number: {
            pattern: /[0-9]/,
            element: document.querySelector('[data-requirement="number"]')
        },
        special: {
            pattern: /[!@#$%^&()'[\]"?+\-/*={}.,;:_]/,
            element: document.querySelector('[data-requirement="special"]')
        },
        length: {
            pattern: /.{8,}/,
            element: document.querySelector('[data-requirement="length"]')
        }
    };

    // Toggle password visibility
    if (togglePasswordButton) {
        togglePasswordButton.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle icon
            if (type === 'password') {
                togglePasswordButton.innerHTML = '<i class="bi bi-eye"></i>';
            } else {
                togglePasswordButton.innerHTML = '<i class="bi bi-eye-slash"></i>';
            }
        });
    }

    // Show/hide password requirements on focus/blur
    if (passwordInput && passwordRequirements) {
        // Show requirements when password field is focused
        passwordInput.addEventListener('focus', function() {
            passwordRequirements.classList.add('show');
        });

        // We'll keep requirements visible if there's content in the field
        // and hide them only when the field is empty and loses focus

        // Always show requirements if there's text in the password field
        passwordInput.addEventListener('blur', function() {
            if (this.value.length > 0) {
                passwordRequirements.classList.add('show');
            } else {
                passwordRequirements.classList.remove('show');
            }
        });
    }

    // Check password strength and update UI
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            let validCount = 0;
            let isValid = true;

            // Always show requirements when typing
            if (passwordRequirements) {
                passwordRequirements.classList.add('show');
            }

            // Check each validation requirement
            Object.keys(validations).forEach(key => {
                const validation = validations[key];
                const isRequirementMet = validation.pattern.test(password);

                if (validation.element) {
                    const iconElement = validation.element.querySelector('.requirement-icon');
                    const textElement = validation.element.querySelector('.requirement-text');

                    if (isRequirementMet) {
                        validCount++;
                        iconElement.innerHTML = '<i class="bi bi-check-circle-fill"></i>';
                        iconElement.classList.remove('invalid');
                        iconElement.classList.add('valid');
                        textElement.classList.remove('invalid');
                        textElement.classList.add('valid');
                    } else {
                        isValid = false;
                        iconElement.innerHTML = '<i class="bi bi-x-circle"></i>';
                        iconElement.classList.remove('valid');
                        iconElement.classList.add('invalid');
                        textElement.classList.remove('valid');
                        textElement.classList.add('invalid');
                    }
                }
            });

            // Update strength meter
            updateStrengthMeter(validCount);

            // Update form validation state
            passwordInput.setCustomValidity(isValid ? '' : 'Please meet all password requirements');

            // Hide requirements if field is empty
            if (password.length === 0 && !document.activeElement.isSameNode(passwordInput)) {
                passwordRequirements.classList.remove('show');
            }
        });
    }

    // Update the strength meter based on valid requirements count
    function updateStrengthMeter(validCount) {
        // Remove all existing classes
        strengthMeter.classList.remove('strength-weak', 'strength-fair', 'strength-good', 'strength-strong');

        // Set appropriate strength class and text
        if (validCount === 0) {
            strengthMeter.classList.add('strength-weak');
            strengthText.textContent = 'Weak';
            strengthText.style.color = '#dc3545';
        } else if (validCount <= 2) {
            strengthMeter.classList.add('strength-fair');
            strengthText.textContent = 'Fair';
            strengthText.style.color = '#ffc107';
        } else if (validCount <= 4) {
            strengthMeter.classList.add('strength-good');
            strengthText.textContent = 'Good';
            strengthText.style.color = '#17a2b8';
        } else {
            strengthMeter.classList.add('strength-strong');
            strengthText.textContent = 'Strong';
            strengthText.style.color = '#28a745';
        }
    }
});
