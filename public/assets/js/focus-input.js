function focusOnInput(input = $('form input:first'))
{   
    // Ensure name field gets focus on page load and maintain it
    const focusNameField = function() {
        input.focus();
    };

    // Set up focus event handler to log when focus happens
    input.focus();

    // Focus immediately
    focusNameField();

    // Create a flag to track if user has interacted with the form
    let userInteracted = false;

    // Track if user has manually changed focus
    let userChangedFocus = false;

    // Listen for any click or keypress to indicate user interaction
    $(document).on('click keydown', function() {
        userInteracted = true;
    });

    [50, 100, 200, 500, 1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000].forEach(function(delay) {
        setTimeout(function() {
            if (!userChangedFocus && !input.is(':focus')) {
                input.focus();
            }
        }, delay);
    });

    // Monitor focus changes for the first 5 seconds of page load
    const focusInterval = setInterval(function() {
        // Only force focus if user hasn't interacted with the form yet
        // and hasn't manually changed focus to another element
        if (!userInteracted && !userChangedFocus && !input.is(':focus')) {
            input.focus();
        }
    }, 100);

    // Stop monitoring after 5 seconds to allow normal user interaction
    setTimeout(function() {
        clearInterval(focusInterval);
    }, 5000);

}