/* Password Validation Styles */
.password-container {
  position: relative;
  margin-bottom: 1.5rem;
}

.password-input-wrapper {
  position: relative;
}

.password-input {
  padding-right: 40px;
  border: 2px solid #e0e0e0;
  transition: border-color 0.3s;
}

.password-input:focus {
  border-color: #2E42FF;
  box-shadow: 0 0 0 0.25rem rgba(46, 66, 255, 0.25);
}

.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #6c757d;
}

.password-toggle:hover {
  color: #2E42FF;
}

.password-requirements {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
  border: 1px solid #e9ecef;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: max-height 0.4s ease, opacity 0.3s ease, padding 0.3s ease, margin 0.3s ease;
  padding-top: 0;
  padding-bottom: 0;
  margin-bottom: 0;
}

.password-requirements.show {
  max-height: 300px; /* Adjust based on your content */
  opacity: 1;
  padding: 15px;
  margin-bottom: 10px;
}

.password-strength-meter {
  height: 5px;
  background-color: #e9ecef;
  margin: 10px 0;
  border-radius: 5px;
  overflow: hidden;
}

.password-strength-meter-bar {
  height: 100%;
  border-radius: 5px;
  transition: width 0.3s ease, background-color 0.3s ease;
}

.strength-weak {
  width: 25%;
  background-color: #dc3545;
}

.strength-fair {
  width: 50%;
  background-color: #ffc107;
}

.strength-good {
  width: 75%;
  background-color: #17a2b8;
}

.strength-strong {
  width: 100%;
  background-color: #28a745;
}

.password-strength-text {
  font-size: 0.85rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.requirement-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: #6c757d;
  transition: color 0.3s ease;
}

.requirement-icon {
  margin-right: 8px;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.requirement-icon.invalid {
  color: #dc3545;
}

.requirement-icon.valid {
  color: #28a745;
}

.requirement-text {
  transition: color 0.3s ease;
}

.requirement-text.valid {
  color: #28a745;
}

.requirement-text.invalid {
  color: #6c757d;
}

/* Animation for validation icons */
@keyframes validationPop {
  0% { transform: scale(0.8); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.requirement-icon.valid,
.requirement-icon.invalid {
  animation: validationPop 0.3s ease;
}
