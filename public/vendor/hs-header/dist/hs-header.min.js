!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.HSHeader=t():e.HSHeader=t()}(window,function(){return d={"./src/js/hs-header.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return HSHeader; });\n/* harmony import */ var _observers_sticky__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./observers/sticky */ \"./src/js/observers/sticky.js\");\n/* harmony import */ var _observers_moment_show_hide__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./observers/moment-show-hide */ \"./src/js/observers/moment-show-hide.js\");\n/* harmony import */ var _observers_show_hide__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./observers/show-hide */ \"./src/js/observers/show-hide.js\");\n/* harmony import */ var _observers_hide_section__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./observers/hide-section */ \"./src/js/observers/hide-section.js\");\n/* harmony import */ var _observers_has_hidden_element__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./observers/has-hidden-element */ \"./src/js/observers/has-hidden-element.js\");\n/* harmony import */ var _observers_floating__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./observers/floating */ \"./src/js/observers/floating.js\");\n/* harmony import */ var _observers_without_behavior__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./observers/without-behavior */ \"./src/js/observers/without-behavior.js\");\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n/*\n* HSHeader Plugin\n* @version: 3.0.0 (Mon, 25 Mar 2021)\n* @author: HtmlStream\n* @event-namespace: .HSHeader\n* @license: Htmlstream Libraries (https://htmlstream.com/)\n* Copyright 2019 Htmlstream\n*/\n// Sticky\n // Moment Show / Hide\n\n // Show / Hide\n\n // Hide Section\n\n // Has Hidden Element\n\n // Floating\n\n // Without Behavior\n\n\n\nvar HSHeader = /*#__PURE__*/function () {\n  function HSHeader(el, config, observers) {\n    _classCallCheck(this, HSHeader);\n\n    this.element = typeof el === \"string\" ? document.querySelector(el) : el;\n    this.config = config;\n    this.observers = observers && Object.prototype.toString.call(observers) === '[object Object]' ? observers : {};\n    this.viewport = 'xs';\n    this.defaults = {\n      fixMoment: 0,\n      fixMomentClasses: null,\n      fixMomentExclude: null,\n      fixEffect: 'slide',\n      breakpoint: 'lg',\n      breakpointsMap: {\n        'md': 768,\n        'sm': 576,\n        'lg': 992,\n        'xl': 1200\n      },\n      effectCompensation: false,\n      effectCompensationStartClass: false,\n      effectCompensationEndClass: false\n    };\n  }\n\n  _createClass(HSHeader, [{\n    key: \"init\",\n    value: function init() {\n      var self = this,\n          element = this.element;\n      var dataSettings = element.hasAttribute('data-hs-header-options') ? JSON.parse(element.getAttribute('data-hs-header-options')) : {};\n      if (!element || element.hasAttribute('HSHeader')) return;\n      this.config = Object.assign({}, this.defaults, dataSettings, this.config);\n\n      this._detectObservers();\n\n      this.fixMediaDifference(this.element);\n      this.checkViewport();\n      onScroll();\n      document.addEventListener('scroll', onScroll);\n      onResize();\n      window.addEventListener('resize', onResize);\n\n      function onScroll() {\n        window.HSHeader = null;\n\n        if (window.pageYOffset < self.config.fixMoment - 100 && self.config.effectCompensation === true) {\n          element.style.top = -window.pageYOffset;\n          element.classList.add(self.config.effectCompensationStartClass);\n          element.classList.remove(self.config.effectCompensationEndClass);\n        } else if (self.config.effectCompensation === true) {\n          element.style.top = 0;\n          element.classList.add(self.config.effectCompensationEndClass);\n          element.classList.remove(self.config.effectCompensationStartClass);\n        }\n\n        if (element.hasAttribute('HSHeader')) {\n          self.notify();\n        }\n\n        element.setAttribute('HSHeader', true);\n      }\n\n      function onResize() {\n        if (self.resizeTimeOutId) clearTimeout(self.resizeTimeOutId);\n        self.resizeTimeOutId = setTimeout(function () {\n          // self.checkViewport()\n          self.update();\n        }, 100);\n      }\n\n      return this.element;\n    }\n  }, {\n    key: \"header\",\n    value: function header(element, config, observers) {\n      if (!element || !element.length) return;\n      this.element = element;\n      this.config = config;\n      this.observers = observers && $.isPlainObject(observers) ? observers : {};\n      this.viewport = 'xs';\n      this.checkViewport();\n    }\n  }, {\n    key: \"_detectObservers\",\n    value: function _detectObservers() {\n      if (!this.element) return;\n      var observers = this.observers = {\n        'xs': [],\n        'sm': [],\n        'md': [],\n        'lg': [],\n        'xl': []\n      };\n      /* ------------------------ xs -------------------------*/\n      // Has Hidden Element\n\n      if (this.element.classList.contains('navbar-has-hidden-element')) {\n        observers['xs'].push(new _observers_has_hidden_element__WEBPACK_IMPORTED_MODULE_4__[\"default\"](this.element).init());\n      } // Sticky top\n\n\n      if (this.element.classList.contains('navbar-sticky-top')) {\n        if (this.element.classList.contains('navbar-show-hide')) {\n          observers['xs'].push(new _observers_moment_show_hide__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this.element).init());\n        } else if (this.element.classList.contains('navbar-toggle')) {\n          observers['xs'].push(new _observers_hide_section__WEBPACK_IMPORTED_MODULE_3__[\"default\"](this.element).init());\n        }\n\n        observers['xs'].push(new _observers_sticky__WEBPACK_IMPORTED_MODULE_0__[\"default\"](this.element).init());\n      } // Floating\n\n\n      if (this.element.classList.contains('navbar-floating')) {\n        observers['xs'].push(new _observers_floating__WEBPACK_IMPORTED_MODULE_5__[\"default\"](this.element).init());\n      }\n\n      if (this.element.classList.contains('navbar-invulnerable')) {\n        observers['xs'].push(new _observers_without_behavior__WEBPACK_IMPORTED_MODULE_6__[\"default\"](this.element).init());\n      } // Abs top & Static\n\n\n      if (this.element.classList.contains('navbar-absolute-top') || this.element.classList.contains('navbar-static')) {\n        if (this.element.classList.contains('navbar-show-hide')) {\n          observers['xs'].push(new _observers_show_hide__WEBPACK_IMPORTED_MODULE_2__[\"default\"](this.element, this.config).init());\n        }\n      }\n\n      return observers;\n    }\n  }, {\n    key: \"fixMediaDifference\",\n    value: function fixMediaDifference(element) {\n      if (!element || !element.length || !element.filter(function (el) {\n        return el.closest('[class*=\"navbar-side\"]');\n      }).length) return;\n      var toggleable;\n\n      if (element.classList.contains('navbar-side-left-xl') || element.classList.contains('navbar-side-right-xl')) {\n        toggleable = element.querySelector('.navbar-expand-xl');\n\n        if (toggleable) {\n          toggleable.classList.remove('navbar-expand-xl');\n          toggleable.classList.add('navbar-expand-lg');\n        }\n      } else if (element.classList.contains('navbar-side-left-lg') || element.classList.contains('navbar-side-right-lg')) {\n        toggleable = element.querySelector('.navbar-expand-lg');\n\n        if (toggleable) {\n          toggleable.classList.remove('navbar-expand-lg');\n          toggleable.classList.add('navbar-expand-md');\n        }\n      } else if (element.classList.contains('navbar-side-left-md') || element.classList.contains('navbar-side-right-md')) {\n        toggleable = element.querySelector('.navbar-expand-md');\n\n        if (toggleable) {\n          toggleable.classList.remove('navbar-expand-md');\n          toggleable.classList.add('navbar-expand-sm');\n        }\n      } else if (element.classList.contains('navbar-side-left-sm') || element.classList.contains('navbar-side-right-sm')) {\n        toggleable = element.querySelector('.navbar-expand-sm');\n\n        if (toggleable) {\n          toggleable.classList.remove('navbar-expand-sm');\n          toggleable.classList.add('navbar-expand');\n        }\n      }\n    }\n  }, {\n    key: \"checkViewport\",\n    value: function checkViewport() {\n      if (window.innerWidth > this.config.breakpointsMap['sm'] && this.observers['sm'].length) {\n        this.prevViewport = this.viewport;\n        this.viewport = 'sm';\n\n        if (this.config.fixMoment && window.pageYOffset > this.config.fixMoment) {\n          if (typeof this.config.breakpointsMap['sm'] === 'undefined') {\n            this.element.classList.remove('navbar-scrolled');\n          } else {\n            this.element.classList.add('navbar-scrolled');\n          }\n        }\n\n        return this;\n      }\n\n      if (window.innerWidth > this.config.breakpointsMap['md'] && this.observers['md'].length) {\n        this.prevViewport = this.viewport;\n        this.viewport = 'md';\n\n        if (this.config.fixMoment && window.pageYOffset > this.config.fixMoment) {\n          if (typeof this.config.breakpointsMap['md'] === 'undefined') {\n            this.element.classList.remove('navbar-scrolled');\n          } else {\n            this.element.classList.add('navbar-scrolled');\n          }\n        }\n\n        return this;\n      }\n\n      if (window.innerWidth > this.config.breakpointsMap['lg'] && this.observers['lg'].length) {\n        this.prevViewport = this.viewport;\n        this.viewport = 'lg';\n\n        if (this.config.fixMoment && window.pageYOffset > this.config.fixMoment) {\n          if (typeof this.config.breakpointsMap['lg'] === 'undefined') {\n            this.element.classList.remove('navbar-scrolled');\n          } else {\n            this.element.classList.add('navbar-scrolled');\n          }\n        }\n\n        return this;\n      }\n\n      if (window.innerWidth > this.config.breakpointsMap['xl'] && this.observers['xl'].length) {\n        this.prevViewport = this.viewport;\n        this.viewport = 'xl';\n\n        if (this.config.fixMoment && window.pageYOffset > this.config.fixMoment) {\n          if (typeof this.config.breakpointsMap['xl'] === 'undefined') {\n            this.element.classList.remove('navbar-scrolled');\n          } else {\n            this.element.classList.add('navbar-scrolled');\n          }\n        }\n\n        return this;\n      }\n\n      if (this.prevViewport) this.prevViewport = this.viewport;\n\n      if (this.config.fixMoment && window.pageYOffset > this.config.fixMoment) {\n        if (typeof this.config.breakpointsMap['xs'] === 'undefined') {\n          this.element.classList.remove('navbar-scrolled');\n        } else {\n          this.element.classList.add('navbar-scrolled');\n        }\n      }\n\n      this.viewport = 'xs';\n      return this;\n    }\n  }, {\n    key: \"notify\",\n    value: function notify() {\n      if (this.prevViewport) {\n        this.observers[this.prevViewport].forEach(function (observer) {\n          observer.destroy();\n        });\n        this.prevViewport = null;\n      }\n\n      this.observers[this.viewport].forEach(function (observer) {\n        observer.check();\n      });\n      return this;\n    }\n  }, {\n    key: \"update\",\n    value: function update() {\n      for (var viewport in this.observers) {\n        this.observers[viewport].forEach(function (observer) {\n          observer.destroy();\n        });\n      }\n\n      this.prevViewport = null;\n      this.observers[this.viewport].forEach(function (observer) {\n        observer.reinit();\n      });\n      return this;\n    }\n  }]);\n\n  return HSHeader;\n}();\n\n\n\n//# sourceURL=webpack://HSHeader/./src/js/hs-header.js?")},"./src/js/observers/abstract.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return HSAbstractObserver; });\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nvar HSAbstractObserver = /*#__PURE__*/function () {\n  function HSAbstractObserver(element) {\n    _classCallCheck(this, HSAbstractObserver);\n\n    this.element = element;\n    this.defaultState = true;\n  }\n\n  _createClass(HSAbstractObserver, [{\n    key: "reinit",\n    value: function reinit() {\n      this.destroy().init().check();\n    }\n  }]);\n\n  return HSAbstractObserver;\n}();\n\n\n\n//# sourceURL=webpack://HSHeader/./src/js/observers/abstract.js?')},"./src/js/observers/floating.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return HSHeaderFloatingObserver; });\n/* harmony import */ var _abstract__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract */ "./src/js/observers/abstract.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn\'t been initialised - super() hasn\'t been called"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\nvar HSHeaderFloatingObserver = /*#__PURE__*/function (_HSAbstractObserver) {\n  _inherits(HSHeaderFloatingObserver, _HSAbstractObserver);\n\n  var _super = _createSuper(HSHeaderFloatingObserver);\n\n  function HSHeaderFloatingObserver(element) {\n    var _this;\n\n    _classCallCheck(this, HSHeaderFloatingObserver);\n\n    _this = _super.call(this, element);\n    _this.dataSettings = _this.element.hasAttribute(\'data-hs-header-options\') ? JSON.parse(_this.element.getAttribute(\'data-hs-header-options\')) : {};\n    return _this;\n  }\n\n  _createClass(HSHeaderFloatingObserver, [{\n    key: "init",\n    value: function init() {\n      this.offset = this.element.offsetTop;\n      this.sections = this.element.querySelectorAll(\'.navbar-section\');\n      this.defaultState = true;\n      return this;\n    }\n  }, {\n    key: "destroy",\n    value: function destroy() {\n      this.toDefaultState();\n      return this;\n    }\n  }, {\n    key: "check",\n    value: function check() {\n      var docScrolled = window.pageYOffset;\n\n      if (docScrolled > this.offset && this.defaultState) {\n        this.changeState();\n      } else if (docScrolled <= this.offset && !this.defaultState) {\n        this.toDefaultState();\n      }\n\n      return this;\n    }\n  }, {\n    key: "changeState",\n    value: function changeState() {\n      this.element.classList.add(\'navbar-scrolled\');\n      this.element.classList.add(this.dataSettings.fixMomentClasses);\n      this.element.classList.remove(this.dataSettings.fixMomentExclude);\n\n      if (this.sections.length) {\n        this.sections.forEach(function ($section) {\n          var dataSettings = $section.hasAttribute(\'data-hs-navbar-item-options\') ? JSON.parse($section.getAttribute(\'data-hs-navbar-item-options\')) : {};\n          $section.classList.add(dataSettings.fixMomentClasses);\n          $section.classList.remove(dataSettings.fixMomentExclude);\n        });\n      }\n\n      this.defaultState = !this.defaultState;\n      return this;\n    }\n  }, {\n    key: "toDefaultState",\n    value: function toDefaultState() {\n      this.element.classList.remove(\'navbar-scrolled\');\n      this.element.classList.remove(this.dataSettings.fixMomentClasses);\n      this.element.classList.add(this.dataSettings.fixMomentExclude);\n\n      if (this.sections.length) {\n        this.sections.forEach(function ($section) {\n          var dataSettings = $section.hasAttribute(\'data-hs-navbar-item-options\') ? JSON.parse($section.getAttribute(\'data-hs-navbar-item-options\')) : {};\n          $section.classList.add(dataSettings.fixMomentClasses);\n          $section.classList.remove(dataSettings.fixMomentExclude);\n        });\n      }\n\n      this.defaultState = !this.defaultState;\n      return this;\n    }\n  }]);\n\n  return HSHeaderFloatingObserver;\n}(_abstract__WEBPACK_IMPORTED_MODULE_0__["default"]);\n\n\n\n//# sourceURL=webpack://HSHeader/./src/js/observers/floating.js?')},"./src/js/observers/has-hidden-element.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return HSHeaderHasHiddenElement; });\n/* harmony import */ var _abstract__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract */ "./src/js/observers/abstract.js");\n/* harmony import */ var _utils_slideUp__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/slideUp */ "./src/js/utils/slideUp.js");\n/* harmony import */ var _utils_slideDown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/slideDown */ "./src/js/utils/slideDown.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn\'t been initialised - super() hasn\'t been called"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\n\n\nvar HSHeaderHasHiddenElement = /*#__PURE__*/function (_HSAbstractObserver) {\n  _inherits(HSHeaderHasHiddenElement, _HSAbstractObserver);\n\n  var _super = _createSuper(HSHeaderHasHiddenElement);\n\n  function HSHeaderHasHiddenElement(element) {\n    var _this;\n\n    _classCallCheck(this, HSHeaderHasHiddenElement);\n\n    _this = _super.call(this, element);\n    _this.config = {\n      animated: true\n    };\n    _this.dataSettings = _this.element.hasAttribute(\'data-hs-header-options\') ? JSON.parse(_this.element.getAttribute(\'data-hs-header-options\')) : {};\n    return _this;\n  }\n\n  _createClass(HSHeaderHasHiddenElement, [{\n    key: "init",\n    value: function init() {\n      this.offset = isFinite(this.dataSettings.fixMoment) ? this.dataSettings.fixMoment : 5;\n      this.elements = this.element.querySelectorAll(\'.navbar-hidden-element\');\n      this.defaultState = true;\n      return this;\n    }\n  }, {\n    key: "destroy",\n    value: function destroy() {\n      this.toDefaultState();\n      return this;\n    }\n  }, {\n    key: "check",\n    value: function check() {\n      if (!this.elements.length) return this;\n      var docScrolled = window.pageYOffset;\n\n      if (docScrolled > this.offset && this.defaultState) {\n        this.changeState();\n      } else if (docScrolled <= this.offset && !this.defaultState) {\n        this.toDefaultState();\n      }\n\n      return this;\n    }\n  }, {\n    key: "changeState",\n    value: function changeState() {\n      if (this.config.animated) {\n        this.elements.forEach(function (item) {\n          Object(_utils_slideUp__WEBPACK_IMPORTED_MODULE_1__["default"])(item);\n        });\n      } else {\n        this.elements.forEach(function (item) {\n          item.style.display = \'none\';\n        });\n      }\n\n      this.defaultState = !this.defaultState;\n      return this;\n    }\n  }, {\n    key: "toDefaultState",\n    value: function toDefaultState() {\n      if (this.config.animated) {\n        this.elements.forEach(function (item) {\n          Object(_utils_slideDown__WEBPACK_IMPORTED_MODULE_2__["default"])(item);\n        });\n      } else {\n        this.elements.forEach(function (item) {\n          item.style.display = \'block\';\n        });\n      }\n\n      this.defaultState = !this.defaultState;\n      return this;\n    }\n  }]);\n\n  return HSHeaderHasHiddenElement;\n}(_abstract__WEBPACK_IMPORTED_MODULE_0__["default"]);\n\n\n\n//# sourceURL=webpack://HSHeader/./src/js/observers/has-hidden-element.js?')},"./src/js/observers/hide-section.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return HSHeaderHideSectionObserver; });\n/* harmony import */ var _abstract__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract */ "./src/js/observers/abstract.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn\'t been initialised - super() hasn\'t been called"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\nvar HSHeaderHideSectionObserver = /*#__PURE__*/function (_HSAbstractObserver) {\n  _inherits(HSHeaderHideSectionObserver, _HSAbstractObserver);\n\n  var _super = _createSuper(HSHeaderHideSectionObserver);\n\n  function HSHeaderHideSectionObserver(element) {\n    var _this;\n\n    _classCallCheck(this, HSHeaderHideSectionObserver);\n\n    _this = _super.call(this, element);\n    _this.dataSettings = _this.element.hasAttribute(\'data-hs-header-options\') ? JSON.parse(_this.element.getAttribute(\'data-hs-header-options\')) : {};\n    return _this;\n  }\n\n  _createClass(HSHeaderHideSectionObserver, [{\n    key: "init",\n    value: function init() {\n      this.offset = isFinite(this.dataSettings.fixMoment) ? this.dataSettings.fixMoment : 5;\n      this.section = this.element.querySelector(\'.navbar-section-hidden\');\n      this.defaultState = true;\n      this.sectionHeight = this.section ? this.section.offsetHeight : 0;\n      return this;\n    }\n  }, {\n    key: "destroy",\n    value: function destroy() {\n      if (this.section) {\n        this.element.transition = \'margin-top .5s\';\n        this.element.style.marginTop = 0;\n      }\n\n      return this;\n    }\n  }, {\n    key: "check",\n    value: function check() {\n      if (!this.section) return this;\n      var docScrolled = window.pageYOffset;\n\n      if (docScrolled > this.offset && this.defaultState) {\n        this.changeState();\n      } else if (docScrolled <= this.offset && !this.defaultState) {\n        this.toDefaultState();\n      }\n\n      return this;\n    }\n  }, {\n    key: "changeState",\n    value: function changeState() {\n      var self = this;\n      this.element.transition = \'margin-top .5s\';\n      this.element.style.marginTop = self.sectionHeight * -1 - 1 + \'px\';\n      this.defaultState = !this.defaultState;\n      return this;\n    }\n  }, {\n    key: "toDefaultState",\n    value: function toDefaultState() {\n      this.element.transition = \'margin-top .5s\';\n      this.element.style.marginTop = 0;\n      this.defaultState = !this.defaultState;\n      return this;\n    }\n  }]);\n\n  return HSHeaderHideSectionObserver;\n}(_abstract__WEBPACK_IMPORTED_MODULE_0__["default"]);\n\n\n\n//# sourceURL=webpack://HSHeader/./src/js/observers/hide-section.js?')},"./src/js/observers/moment-show-hide.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return HSHeaderMomentShowHideObserver; });\n/* harmony import */ var _abstract__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract */ "./src/js/observers/abstract.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn\'t been initialised - super() hasn\'t been called"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\nvar HSHeaderMomentShowHideObserver = /*#__PURE__*/function (_HSAbstractObserver) {\n  _inherits(HSHeaderMomentShowHideObserver, _HSAbstractObserver);\n\n  var _super = _createSuper(HSHeaderMomentShowHideObserver);\n\n  function HSHeaderMomentShowHideObserver(element) {\n    var _this;\n\n    _classCallCheck(this, HSHeaderMomentShowHideObserver);\n\n    _this = _super.call(this, element);\n    _this.dataSettings = _this.element.hasAttribute(\'data-hs-header-options\') ? JSON.parse(_this.element.getAttribute(\'data-hs-header-options\')) : {};\n    return _this;\n  }\n\n  _createClass(HSHeaderMomentShowHideObserver, [{\n    key: "init",\n    value: function init() {\n      this.direction = \'down\';\n      this.delta = 0;\n      this.defaultState = true;\n      this.offset = isFinite(this.dataSettings.fixMoment) && this.dataSettings.fixMoment !== 0 ? this.dataSettings.fixMoment : 5;\n      this.effect = this.dataSettings.fixEffect ? this.dataSettings.fixEffect : \'show-hide\';\n      return this;\n    }\n  }, {\n    key: "destroy",\n    value: function destroy() {\n      this.toDefaultState();\n      return this;\n    }\n  }, {\n    key: "checkDirection",\n    value: function checkDirection() {\n      if (window.pageYOffset > this.delta) {\n        this.direction = \'down\';\n      } else {\n        this.direction = \'up\';\n      }\n\n      this.delta = window.pageYOffset;\n      return this;\n    }\n  }, {\n    key: "toDefaultState",\n    value: function toDefaultState() {\n      switch (this.effect) {\n        case \'slide\':\n          this.element.classList.remove(\'navbar-moved-up\');\n          break;\n\n        case \'fade\':\n          this.element.classList.remove(\'navbar-faded\');\n          break;\n\n        default:\n          this.element.classList.remove(\'navbar-invisible\');\n      }\n\n      this.defaultState = !this.defaultState;\n      return this;\n    }\n  }, {\n    key: "changeState",\n    value: function changeState() {\n      switch (this.effect) {\n        case \'slide\':\n          this.element.classList.add(\'navbar-moved-up\');\n          break;\n\n        case \'fade\':\n          this.element.classList.add(\'navbar-faded\');\n          break;\n\n        default:\n          this.element.classList.add(\'navbar-invisible\');\n      }\n\n      this.defaultState = !this.defaultState;\n      return this;\n    }\n  }, {\n    key: "check",\n    value: function check() {\n      var docScrolled = window.pageYOffset;\n      this.checkDirection();\n\n      if (docScrolled >= this.offset && this.defaultState && this.direction === \'down\') {\n        this.changeState();\n      } else if (!this.defaultState && this.direction === \'up\') {\n        this.toDefaultState();\n      }\n\n      return this;\n    }\n  }]);\n\n  return HSHeaderMomentShowHideObserver;\n}(_abstract__WEBPACK_IMPORTED_MODULE_0__["default"]);\n\n\n\n//# sourceURL=webpack://HSHeader/./src/js/observers/moment-show-hide.js?')},"./src/js/observers/show-hide.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return HSHeaderShowHideObserver; });\n/* harmony import */ var _abstract__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract */ \"./src/js/observers/abstract.js\");\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\nvar HSHeaderShowHideObserver = /*#__PURE__*/function (_HSAbstractObserver) {\n  _inherits(HSHeaderShowHideObserver, _HSAbstractObserver);\n\n  var _super = _createSuper(HSHeaderShowHideObserver);\n\n  function HSHeaderShowHideObserver(element, config) {\n    var _this;\n\n    _classCallCheck(this, HSHeaderShowHideObserver);\n\n    _this = _super.call(this, element, config);\n    _this.dataSettings = _this.element.hasAttribute('data-hs-header-options') ? JSON.parse(_this.element.getAttribute('data-hs-header-options')) : {};\n    _this.config = config;\n    return _this;\n  }\n\n  _createClass(HSHeaderShowHideObserver, [{\n    key: \"init\",\n    value: function init() {\n      if (!this.defaultState && window.pageYOffset > this.offset) return this;\n      this.defaultState = true;\n      this.transitionDuration = parseFloat(getComputedStyle(this.element)['transition-duration'], 10) * 1000;\n      this.offset = isFinite(this.dataSettings.fixMoment) && this.dataSettings.fixMoment > this.element.offsetHeight ? this.dataSettings.fixMoment : this.element.offsetHeight + 100;\n      this.effect = this.dataSettings.fixEffect ? this.dataSettings.fixEffect : 'show-hide';\n      return this;\n    }\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      if (!this.defaultState && window.pageYOffset > this.offset) return this;\n      this.element.classList.remove('navbar-untransitioned');\n\n      this._removeCap();\n\n      return this;\n    }\n  }, {\n    key: \"check\",\n    value: function check() {\n      if (window.pageYOffset > this.element.offsetHeight && !this.capInserted) {\n        this._insertCap();\n      } else if (window.pageYOffset <= this.element.offsetHeight && this.capInserted) {\n        this._removeCap();\n      }\n\n      if (window.pageYOffset > this.offset && this.defaultState) {\n        this.changeState();\n      } else if (window.pageYOffset <= this.offset && !this.defaultState) {\n        this.toDefaultState();\n      }\n    }\n  }, {\n    key: \"changeState\",\n    value: function changeState() {\n      if (this.config.fixMomentClasses) {\n        var _this$element$classLi;\n\n        (_this$element$classLi = this.element.classList).add.apply(_this$element$classLi, _toConsumableArray(this.config.fixMomentClasses));\n      }\n\n      this.element.classList.remove('navbar-untransitioned');\n      if (this.animationTimeoutId) clearTimeout(this.animationTimeoutId);\n\n      switch (this.effect) {\n        case 'fade':\n          this.element.classList.remove('navbar-faded');\n          break;\n\n        case 'slide':\n          this.element.classList.remove('navbar-moved-up');\n          break;\n\n        default:\n          this.element.classList.remove('navbar-invisible');\n      }\n\n      this.defaultState = !this.defaultState;\n    }\n  }, {\n    key: \"toDefaultState\",\n    value: function toDefaultState() {\n      var self = this;\n\n      if (this.config.fixMomentClasses) {\n        var _this$element$classLi2;\n\n        (_this$element$classLi2 = this.element.classList).remove.apply(_this$element$classLi2, _toConsumableArray(this.config.fixMomentClasses));\n      }\n\n      this.animationTimeoutId = setTimeout(function () {\n        self.element.classList.add('navbar-untransitioned');\n      }, this.transitionDuration);\n\n      switch (this.effect) {\n        case 'fade':\n          this.element.classList.add('navbar-faded');\n          break;\n\n        case 'slide':\n          this.element.classList.add('navbar-moved-up');\n          break;\n\n        default:\n          this.element.classList.add('navbar-invisible');\n      }\n\n      this.defaultState = !this.defaultState;\n    }\n  }, {\n    key: \"_insertCap\",\n    value: function _insertCap() {\n      this.element.classList.add('navbar-scrolled', 'navbar-untransitioned');\n\n      if (this.element.classList.contains('navbar-static')) {\n        document.documentElement.style.paddingTop = this.element.offsetHeight;\n      }\n\n      switch (this.effect) {\n        case 'fade':\n          this.element.classList.add('navbar-faded');\n          break;\n\n        case 'slide':\n          this.element.classList.add('navbar-moved-up');\n          break;\n\n        default:\n          this.element.classList.add('navbar-invisible');\n      }\n\n      this.capInserted = true;\n    }\n  }, {\n    key: \"_removeCap\",\n    value: function _removeCap() {\n      var self = this;\n      this.element.classList.remove('navbar-scrolled');\n\n      if (this.element.classList.contains('navbar-static')) {\n        document.documentElement.style.paddingTop = 0;\n      }\n\n      if (this.removeCapTimeOutId) clearTimeout(this.removeCapTimeOutId);\n      this.removeCapTimeOutId = setTimeout(function () {\n        self.element.classList.remove('navbar-moved-up', 'navbar-faded', 'navbar-invisible');\n      }, 10);\n      this.capInserted = false;\n    }\n  }]);\n\n  return HSHeaderShowHideObserver;\n}(_abstract__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n\n//# sourceURL=webpack://HSHeader/./src/js/observers/show-hide.js?")},"./src/js/observers/sticky.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return HSHeaderStickObserver; });\n/* harmony import */ var _abstract__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract */ "./src/js/observers/abstract.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn\'t been initialised - super() hasn\'t been called"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\nvar HSHeaderStickObserver = /*#__PURE__*/function (_HSAbstractObserver) {\n  _inherits(HSHeaderStickObserver, _HSAbstractObserver);\n\n  var _super = _createSuper(HSHeaderStickObserver);\n\n  function HSHeaderStickObserver(element) {\n    var _this;\n\n    _classCallCheck(this, HSHeaderStickObserver);\n\n    _this = _super.call(this, element);\n    _this.dataSettings = _this.element.hasAttribute(\'data-hs-header-options\') ? JSON.parse(_this.element.getAttribute(\'data-hs-header-options\')) : {};\n    return _this;\n  }\n\n  _createClass(HSHeaderStickObserver, [{\n    key: "init",\n    value: function init() {\n      this.defaultState = true;\n      this.offset = this.element.offsetTop;\n      return this;\n    }\n  }, {\n    key: "destroy",\n    value: function destroy() {\n      this.toDefaultState();\n      return this;\n    }\n  }, {\n    key: "check",\n    value: function check() {\n      var docScrolled = window.pageYOffset;\n\n      if (docScrolled > this.offset && this.defaultState) {\n        this.changeState();\n      } else if (docScrolled <= this.offset && !this.defaultState) {\n        this.toDefaultState();\n      }\n\n      return this;\n    }\n  }, {\n    key: "changeState",\n    value: function changeState() {\n      this.element.classList.add(\'navbar-scrolled\');\n      this.defaultState = !this.defaultState;\n      this.element.classList.add(this.dataSettings.fixMomentClasses);\n      this.element.classList.remove(this.dataSettings.fixMomentExclude);\n      return this;\n    }\n  }, {\n    key: "toDefaultState",\n    value: function toDefaultState() {\n      this.element.classList.remove(\'navbar-scrolled\');\n      this.defaultState = !this.defaultState;\n      this.element.classList.remove(this.dataSettings.fixMomentClasses);\n      this.element.classList.add(this.dataSettings.fixMomentExclude);\n      return this;\n    }\n  }]);\n\n  return HSHeaderStickObserver;\n}(_abstract__WEBPACK_IMPORTED_MODULE_0__["default"]);\n\n\n\n//# sourceURL=webpack://HSHeader/./src/js/observers/sticky.js?')},"./src/js/observers/without-behavior.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return HSHeaderWithoutBehaviorObserver; });\n/* harmony import */ var _abstract__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract */ "./src/js/observers/abstract.js");\nfunction _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn\'t been initialised - super() hasn\'t been called"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\nvar HSHeaderWithoutBehaviorObserver = /*#__PURE__*/function (_HSAbstractObserver) {\n  _inherits(HSHeaderWithoutBehaviorObserver, _HSAbstractObserver);\n\n  var _super = _createSuper(HSHeaderWithoutBehaviorObserver);\n\n  function HSHeaderWithoutBehaviorObserver(element) {\n    _classCallCheck(this, HSHeaderWithoutBehaviorObserver);\n\n    return _super.call(this, element);\n  }\n\n  _createClass(HSHeaderWithoutBehaviorObserver, [{\n    key: "init",\n    value: function init() {\n      return this;\n    }\n  }, {\n    key: "check",\n    value: function check() {\n      return this;\n    }\n  }, {\n    key: "destroy",\n    value: function destroy() {\n      return this;\n    }\n  }, {\n    key: "changeState",\n    value: function changeState() {\n      return this;\n    }\n  }, {\n    key: "toDefaultState",\n    value: function toDefaultState() {\n      return this;\n    }\n  }]);\n\n  return HSHeaderWithoutBehaviorObserver;\n}(_abstract__WEBPACK_IMPORTED_MODULE_0__["default"]);\n\n\n\n//# sourceURL=webpack://HSHeader/./src/js/observers/without-behavior.js?')},"./src/js/utils/slideDown.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval("__webpack_require__.r(__webpack_exports__);\nvar slideDown = function slideDown(target) {\n  var duration = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 500;\n  target.style.removeProperty('display');\n  var display = window.getComputedStyle(target).display;\n  if (display === 'none') display = 'block';\n  target.style.display = display;\n  var height = target.offsetHeight;\n  target.style.overflow = 'hidden';\n  target.style.height = 0;\n  target.style.paddingTop = 0;\n  target.style.paddingBottom = 0;\n  target.style.marginTop = 0;\n  target.style.marginBottom = 0;\n  target.offsetHeight;\n  target.style.boxSizing = 'border-box';\n  target.style.transitionProperty = \"height, margin, padding\";\n  target.style.transitionDuration = duration + 'ms';\n  target.style.height = height + 'px';\n  target.style.removeProperty('padding-top');\n  target.style.removeProperty('padding-bottom');\n  target.style.removeProperty('margin-top');\n  target.style.removeProperty('margin-bottom');\n  window.setTimeout(function () {\n    target.style.removeProperty('height');\n    target.style.removeProperty('overflow');\n    target.style.removeProperty('transition-duration');\n    target.style.removeProperty('transition-property');\n  }, duration);\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (slideDown);\n\n//# sourceURL=webpack://HSHeader/./src/js/utils/slideDown.js?")},"./src/js/utils/slideUp.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval("__webpack_require__.r(__webpack_exports__);\nvar slideUp = function slideUp(target) {\n  var duration = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 500;\n  target.style.transitionProperty = 'height, margin, padding';\n  target.style.transitionDuration = duration + 'ms';\n  target.style.boxSizing = 'border-box';\n  target.style.height = target.offsetHeight + 'px';\n  target.offsetHeight;\n  target.style.overflow = 'hidden';\n  target.style.height = 0;\n  target.style.paddingTop = 0;\n  target.style.paddingBottom = 0;\n  target.style.marginTop = 0;\n  target.style.marginBottom = 0;\n  window.setTimeout(function () {\n    target.style.display = 'none';\n    target.style.removeProperty('height');\n    target.style.removeProperty('padding-top');\n    target.style.removeProperty('padding-bottom');\n    target.style.removeProperty('margin-top');\n    target.style.removeProperty('margin-bottom');\n    target.style.removeProperty('overflow');\n    target.style.removeProperty('transition-duration');\n    target.style.removeProperty('transition-property'); //alert(\"!\");\n  }, duration);\n  return target;\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (slideUp);\n\n//# sourceURL=webpack://HSHeader/./src/js/utils/slideUp.js?")}},e={},f.m=d,f.c=e,f.d=function(e,t,n){f.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},f.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},f.t=function(t,e){if(1&e&&(t=f(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(f.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)f.d(n,r,function(e){return t[e]}.bind(null,r));return n},f.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return f.d(t,"a",t),t},f.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},f.p="",f(f.s="./src/js/hs-header.js").default;function f(t){if(e[t])return e[t].exports;var n=e[t]={i:t,l:!1,exports:{}};return d[t].call(n.exports,n,n.exports,f),n.l=!0,n.exports}var d,e});