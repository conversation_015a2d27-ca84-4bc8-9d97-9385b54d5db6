!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.HsNavScroller=e():t.HsNavScroller=e()}(window,function(){return d={"./node_modules/velocity-animate/velocity.js":function(module,exports,__webpack_require__){eval('var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_RESULT__;/*! VelocityJS.org (1.5.2). (C) 2014 <PERSON> Shapiro. MIT @license: en.wikipedia.org/wiki/MIT_License */\r\n\r\n/*************************\r\n Velocity jQuery Shim\r\n *************************/\r\n\r\n/*! VelocityJS.org jQuery Shim (1.0.1). (C) 2014 The jQuery Foundation. MIT @license: en.wikipedia.org/wiki/MIT_License. */\r\n\r\n/* This file contains the jQuery functions that Velocity relies on, thereby removing Velocity\'s dependency on a full copy of jQuery, and allowing it to work in any environment. */\r\n/* These shimmed functions are only used if jQuery isn\'t present. If both this shim and jQuery are loaded, Velocity defaults to jQuery proper. */\r\n/* Browser support: Using this shim instead of jQuery proper removes support for IE8. */\r\n\r\n(function(window) {\r\n\t"use strict";\r\n\t/***************\r\n\t Setup\r\n\t ***************/\r\n\r\n\t/* If jQuery is already loaded, there\'s no point in loading this shim. */\r\n\tif (window.jQuery) {\r\n\t\treturn;\r\n\t}\r\n\r\n\t/* jQuery base. */\r\n\tvar $ = function(selector, context) {\r\n\t\treturn new $.fn.init(selector, context);\r\n\t};\r\n\r\n\t/********************\r\n\t Private Methods\r\n\t ********************/\r\n\r\n\t/* jQuery */\r\n\t$.isWindow = function(obj) {\r\n\t\t/* jshint eqeqeq: false */\r\n\t\treturn obj && obj === obj.window;\r\n\t};\r\n\r\n\t/* jQuery */\r\n\t$.type = function(obj) {\r\n\t\tif (!obj) {\r\n\t\t\treturn obj + "";\r\n\t\t}\r\n\r\n\t\treturn typeof obj === "object" || typeof obj === "function" ?\r\n\t\t\t\tclass2type[toString.call(obj)] || "object" :\r\n\t\t\t\ttypeof obj;\r\n\t};\r\n\r\n\t/* jQuery */\r\n\t$.isArray = Array.isArray || function(obj) {\r\n\t\treturn $.type(obj) === "array";\r\n\t};\r\n\r\n\t/* jQuery */\r\n\tfunction isArraylike(obj) {\r\n\t\tvar length = obj.length,\r\n\t\t\t\ttype = $.type(obj);\r\n\r\n\t\tif (type === "function" || $.isWindow(obj)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tif (obj.nodeType === 1 && length) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\treturn type === "array" || length === 0 || typeof length === "number" && length > 0 && (length - 1) in obj;\r\n\t}\r\n\r\n\t/***************\r\n\t $ Methods\r\n\t ***************/\r\n\r\n\t/* jQuery: Support removed for IE<9. */\r\n\t$.isPlainObject = function(obj) {\r\n\t\tvar key;\r\n\r\n\t\tif (!obj || $.type(obj) !== "object" || obj.nodeType || $.isWindow(obj)) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\ttry {\r\n\t\t\tif (obj.constructor &&\r\n\t\t\t\t\t!hasOwn.call(obj, "constructor") &&\r\n\t\t\t\t\t!hasOwn.call(obj.constructor.prototype, "isPrototypeOf")) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t} catch (e) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\r\n\t\tfor (key in obj) {\r\n\t\t}\r\n\r\n\t\treturn key === undefined || hasOwn.call(obj, key);\r\n\t};\r\n\r\n\t/* jQuery */\r\n\t$.each = function(obj, callback, args) {\r\n\t\tvar value,\r\n\t\t\t\ti = 0,\r\n\t\t\t\tlength = obj.length,\r\n\t\t\t\tisArray = isArraylike(obj);\r\n\r\n\t\tif (args) {\r\n\t\t\tif (isArray) {\r\n\t\t\t\tfor (; i < length; i++) {\r\n\t\t\t\t\tvalue = callback.apply(obj[i], args);\r\n\r\n\t\t\t\t\tif (value === false) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tfor (i in obj) {\r\n\t\t\t\t\tif (!obj.hasOwnProperty(i)) {\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvalue = callback.apply(obj[i], args);\r\n\r\n\t\t\t\t\tif (value === false) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t} else {\r\n\t\t\tif (isArray) {\r\n\t\t\t\tfor (; i < length; i++) {\r\n\t\t\t\t\tvalue = callback.call(obj[i], i, obj[i]);\r\n\r\n\t\t\t\t\tif (value === false) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tfor (i in obj) {\r\n\t\t\t\t\tif (!obj.hasOwnProperty(i)) {\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvalue = callback.call(obj[i], i, obj[i]);\r\n\r\n\t\t\t\t\tif (value === false) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn obj;\r\n\t};\r\n\r\n\t/* Custom */\r\n\t$.data = function(node, key, value) {\r\n\t\t/* $.getData() */\r\n\t\tif (value === undefined) {\r\n\t\t\tvar getId = node[$.expando],\r\n\t\t\t\t\tstore = getId && cache[getId];\r\n\r\n\t\t\tif (key === undefined) {\r\n\t\t\t\treturn store;\r\n\t\t\t} else if (store) {\r\n\t\t\t\tif (key in store) {\r\n\t\t\t\t\treturn store[key];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t/* $.setData() */\r\n\t\t} else if (key !== undefined) {\r\n\t\t\tvar setId = node[$.expando] || (node[$.expando] = ++$.uuid);\r\n\r\n\t\t\tcache[setId] = cache[setId] || {};\r\n\t\t\tcache[setId][key] = value;\r\n\r\n\t\t\treturn value;\r\n\t\t}\r\n\t};\r\n\r\n\t/* Custom */\r\n\t$.removeData = function(node, keys) {\r\n\t\tvar id = node[$.expando],\r\n\t\t\t\tstore = id && cache[id];\r\n\r\n\t\tif (store) {\r\n\t\t\t// Cleanup the entire store if no keys are provided.\r\n\t\t\tif (!keys) {\r\n\t\t\t\tdelete cache[id];\r\n\t\t\t} else {\r\n\t\t\t\t$.each(keys, function(_, key) {\r\n\t\t\t\t\tdelete store[key];\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\t/* jQuery */\r\n\t$.extend = function() {\r\n\t\tvar src, copyIsArray, copy, name, options, clone,\r\n\t\t\t\ttarget = arguments[0] || {},\r\n\t\t\t\ti = 1,\r\n\t\t\t\tlength = arguments.length,\r\n\t\t\t\tdeep = false;\r\n\r\n\t\tif (typeof target === "boolean") {\r\n\t\t\tdeep = target;\r\n\r\n\t\t\ttarget = arguments[i] || {};\r\n\t\t\ti++;\r\n\t\t}\r\n\r\n\t\tif (typeof target !== "object" && $.type(target) !== "function") {\r\n\t\t\ttarget = {};\r\n\t\t}\r\n\r\n\t\tif (i === length) {\r\n\t\t\ttarget = this;\r\n\t\t\ti--;\r\n\t\t}\r\n\r\n\t\tfor (; i < length; i++) {\r\n\t\t\tif ((options = arguments[i])) {\r\n\t\t\t\tfor (name in options) {\r\n\t\t\t\t\tif (!options.hasOwnProperty(name)) {\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tsrc = target[name];\r\n\t\t\t\t\tcopy = options[name];\r\n\r\n\t\t\t\t\tif (target === copy) {\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (deep && copy && ($.isPlainObject(copy) || (copyIsArray = $.isArray(copy)))) {\r\n\t\t\t\t\t\tif (copyIsArray) {\r\n\t\t\t\t\t\t\tcopyIsArray = false;\r\n\t\t\t\t\t\t\tclone = src && $.isArray(src) ? src : [];\r\n\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tclone = src && $.isPlainObject(src) ? src : {};\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\ttarget[name] = $.extend(deep, clone, copy);\r\n\r\n\t\t\t\t\t} else if (copy !== undefined) {\r\n\t\t\t\t\t\ttarget[name] = copy;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn target;\r\n\t};\r\n\r\n\t/* jQuery 1.4.3 */\r\n\t$.queue = function(elem, type, data) {\r\n\t\tfunction $makeArray(arr, results) {\r\n\t\t\tvar ret = results || [];\r\n\r\n\t\t\tif (arr) {\r\n\t\t\t\tif (isArraylike(Object(arr))) {\r\n\t\t\t\t\t/* $.merge */\r\n\t\t\t\t\t(function(first, second) {\r\n\t\t\t\t\t\tvar len = +second.length,\r\n\t\t\t\t\t\t\t\tj = 0,\r\n\t\t\t\t\t\t\t\ti = first.length;\r\n\r\n\t\t\t\t\t\twhile (j < len) {\r\n\t\t\t\t\t\t\tfirst[i++] = second[j++];\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (len !== len) {\r\n\t\t\t\t\t\t\twhile (second[j] !== undefined) {\r\n\t\t\t\t\t\t\t\tfirst[i++] = second[j++];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tfirst.length = i;\r\n\r\n\t\t\t\t\t\treturn first;\r\n\t\t\t\t\t})(ret, typeof arr === "string" ? [arr] : arr);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t[].push.call(ret, arr);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn ret;\r\n\t\t}\r\n\r\n\t\tif (!elem) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\ttype = (type || "fx") + "queue";\r\n\r\n\t\tvar q = $.data(elem, type);\r\n\r\n\t\tif (!data) {\r\n\t\t\treturn q || [];\r\n\t\t}\r\n\r\n\t\tif (!q || $.isArray(data)) {\r\n\t\t\tq = $.data(elem, type, $makeArray(data));\r\n\t\t} else {\r\n\t\t\tq.push(data);\r\n\t\t}\r\n\r\n\t\treturn q;\r\n\t};\r\n\r\n\t/* jQuery 1.4.3 */\r\n\t$.dequeue = function(elems, type) {\r\n\t\t/* Custom: Embed element iteration. */\r\n\t\t$.each(elems.nodeType ? [elems] : elems, function(i, elem) {\r\n\t\t\ttype = type || "fx";\r\n\r\n\t\t\tvar queue = $.queue(elem, type),\r\n\t\t\t\t\tfn = queue.shift();\r\n\r\n\t\t\tif (fn === "inprogress") {\r\n\t\t\t\tfn = queue.shift();\r\n\t\t\t}\r\n\r\n\t\t\tif (fn) {\r\n\t\t\t\tif (type === "fx") {\r\n\t\t\t\t\tqueue.unshift("inprogress");\r\n\t\t\t\t}\r\n\r\n\t\t\t\tfn.call(elem, function() {\r\n\t\t\t\t\t$.dequeue(elem, type);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t});\r\n\t};\r\n\r\n\t/******************\r\n\t $.fn Methods\r\n\t ******************/\r\n\r\n\t/* jQuery */\r\n\t$.fn = $.prototype = {\r\n\t\tinit: function(selector) {\r\n\t\t\t/* Just return the element wrapped inside an array; don\'t proceed with the actual jQuery node wrapping process. */\r\n\t\t\tif (selector.nodeType) {\r\n\t\t\t\tthis[0] = selector;\r\n\r\n\t\t\t\treturn this;\r\n\t\t\t} else {\r\n\t\t\t\tthrow new Error("Not a DOM node.");\r\n\t\t\t}\r\n\t\t},\r\n\t\toffset: function() {\r\n\t\t\t/* jQuery altered code: Dropped disconnected DOM node checking. */\r\n\t\t\tvar box = this[0].getBoundingClientRect ? this[0].getBoundingClientRect() : {top: 0, left: 0};\r\n\r\n\t\t\treturn {\r\n\t\t\t\ttop: box.top + (window.pageYOffset || document.scrollTop || 0) - (document.clientTop || 0),\r\n\t\t\t\tleft: box.left + (window.pageXOffset || document.scrollLeft || 0) - (document.clientLeft || 0)\r\n\t\t\t};\r\n\t\t},\r\n\t\tposition: function() {\r\n\t\t\t/* jQuery */\r\n\t\t\tfunction offsetParentFn(elem) {\r\n\t\t\t\tvar offsetParent = elem.offsetParent;\r\n\r\n\t\t\t\twhile (offsetParent && (offsetParent.nodeName.toLowerCase() !== "html" && offsetParent.style && offsetParent.style.position.toLowerCase() === "static")) {\r\n\t\t\t\t\toffsetParent = offsetParent.offsetParent;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn offsetParent || document;\r\n\t\t\t}\r\n\r\n\t\t\t/* Zepto */\r\n\t\t\tvar elem = this[0],\r\n\t\t\t\t\toffsetParent = offsetParentFn(elem),\r\n\t\t\t\t\toffset = this.offset(),\r\n\t\t\t\t\tparentOffset = /^(?:body|html)$/i.test(offsetParent.nodeName) ? {top: 0, left: 0} : $(offsetParent).offset();\r\n\r\n\t\t\toffset.top -= parseFloat(elem.style.marginTop) || 0;\r\n\t\t\toffset.left -= parseFloat(elem.style.marginLeft) || 0;\r\n\r\n\t\t\tif (offsetParent.style) {\r\n\t\t\t\tparentOffset.top += parseFloat(offsetParent.style.borderTopWidth) || 0;\r\n\t\t\t\tparentOffset.left += parseFloat(offsetParent.style.borderLeftWidth) || 0;\r\n\t\t\t}\r\n\r\n\t\t\treturn {\r\n\t\t\t\ttop: offset.top - parentOffset.top,\r\n\t\t\t\tleft: offset.left - parentOffset.left\r\n\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\t/**********************\r\n\t Private Variables\r\n\t **********************/\r\n\r\n\t/* For $.data() */\r\n\tvar cache = {};\r\n\t$.expando = "velocity" + (new Date().getTime());\r\n\t$.uuid = 0;\r\n\r\n\t/* For $.queue() */\r\n\tvar class2type = {},\r\n\t\t\thasOwn = class2type.hasOwnProperty,\r\n\t\t\ttoString = class2type.toString;\r\n\r\n\tvar types = "Boolean Number String Function Array Date RegExp Object Error".split(" ");\r\n\tfor (var i = 0; i < types.length; i++) {\r\n\t\tclass2type["[object " + types[i] + "]"] = types[i].toLowerCase();\r\n\t}\r\n\r\n\t/* Makes $(node) possible, without having to call init. */\r\n\t$.fn.init.prototype = $.fn;\r\n\r\n\t/* Globalize Velocity onto the window, and assign its Utilities property. */\r\n\twindow.Velocity = {Utilities: $};\r\n})(window);\r\n\r\n/******************\r\n Velocity.js\r\n ******************/\r\n\r\n(function(factory) {\r\n\t"use strict";\r\n\t/* CommonJS module. */\r\n\tif ( true && typeof module.exports === "object") {\r\n\t\tmodule.exports = factory();\r\n\t\t/* AMD module. */\r\n\t} else if (true) {\r\n\t\t!(__WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === \'function\' ?\n\t\t\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.call(exports, __webpack_require__, exports, module)) :\n\t\t\t\t__WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\r\n\t\t/* Browser globals. */\r\n\t} else {}\r\n}(function() {\r\n\t"use strict";\r\n\treturn function(global, window, document, undefined) {\r\n\r\n\t\t/***************\r\n\t\t Summary\r\n\t\t ***************/\r\n\r\n\t\t/*\r\n\t\t - CSS: CSS stack that works independently from the rest of Velocity.\r\n\t\t - animate(): Core animation method that iterates over the targeted elements and queues the incoming call onto each element individually.\r\n\t\t - Pre-Queueing: Prepare the element for animation by instantiating its data cache and processing the call\'s options.\r\n\t\t - Queueing: The logic that runs once the call has reached its point of execution in the element\'s $.queue() stack.\r\n\t\t Most logic is placed here to avoid risking it becoming stale (if the element\'s properties have changed).\r\n\t\t - Pushing: Consolidation of the tween data followed by its push onto the global in-progress calls container.\r\n\t\t - tick(): The single requestAnimationFrame loop responsible for tweening all in-progress calls.\r\n\t\t - completeCall(): Handles the cleanup process for each Velocity call.\r\n\t\t */\r\n\r\n\t\t/*********************\r\n\t\t Helper Functions\r\n\t\t *********************/\r\n\r\n\t\t/* IE detection. Gist: https://gist.github.com/julianshapiro/9098609 */\r\n\t\tvar IE = (function() {\r\n\t\t\tif (document.documentMode) {\r\n\t\t\t\treturn document.documentMode;\r\n\t\t\t} else {\r\n\t\t\t\tfor (var i = 7; i > 4; i--) {\r\n\t\t\t\t\tvar div = document.createElement("div");\r\n\r\n\t\t\t\t\tdiv.innerHTML = "\x3c!--[if IE " + i + "]><span></span><![endif]--\x3e";\r\n\r\n\t\t\t\t\tif (div.getElementsByTagName("span").length) {\r\n\t\t\t\t\t\tdiv = null;\r\n\r\n\t\t\t\t\t\treturn i;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn undefined;\r\n\t\t})();\r\n\r\n\t\t/* rAF shim. Gist: https://gist.github.com/julianshapiro/9497513 */\r\n\t\tvar rAFShim = (function() {\r\n\t\t\tvar timeLast = 0;\r\n\r\n\t\t\treturn window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || function(callback) {\r\n\t\t\t\tvar timeCurrent = (new Date()).getTime(),\r\n\t\t\t\t\t\ttimeDelta;\r\n\r\n\t\t\t\t/* Dynamically set delay on a per-tick basis to match 60fps. */\r\n\t\t\t\t/* Technique by Erik Moller. MIT license: https://gist.github.com/paulirish/1579671 */\r\n\t\t\t\ttimeDelta = Math.max(0, 16 - (timeCurrent - timeLast));\r\n\t\t\t\ttimeLast = timeCurrent + timeDelta;\r\n\r\n\t\t\t\treturn setTimeout(function() {\r\n\t\t\t\t\tcallback(timeCurrent + timeDelta);\r\n\t\t\t\t}, timeDelta);\r\n\t\t\t};\r\n\t\t})();\r\n\r\n\t\tvar performance = (function() {\r\n\t\t\tvar perf = window.performance || {};\r\n\r\n\t\t\tif (typeof perf.now !== "function") {\r\n\t\t\t\tvar nowOffset = perf.timing && perf.timing.navigationStart ? perf.timing.navigationStart : (new Date()).getTime();\r\n\r\n\t\t\t\tperf.now = function() {\r\n\t\t\t\t\treturn (new Date()).getTime() - nowOffset;\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\treturn perf;\r\n\t\t})();\r\n\r\n\t\t/* Array compacting. Copyright Lo-Dash. MIT License: https://github.com/lodash/lodash/blob/master/LICENSE.txt */\r\n\t\tfunction compactSparseArray(array) {\r\n\t\t\tvar index = -1,\r\n\t\t\t\t\tlength = array ? array.length : 0,\r\n\t\t\t\t\tresult = [];\r\n\r\n\t\t\twhile (++index < length) {\r\n\t\t\t\tvar value = array[index];\r\n\r\n\t\t\t\tif (value) {\r\n\t\t\t\t\tresult.push(value);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn result;\r\n\t\t}\r\n\r\n\t\t/**\r\n\t\t * Shim for "fixing" IE\'s lack of support (IE < 9) for applying slice\r\n\t\t * on host objects like NamedNodeMap, NodeList, and HTMLCollection\r\n\t\t * (technically, since host objects have been implementation-dependent,\r\n\t\t * at least before ES2015, IE hasn\'t needed to work this way).\r\n\t\t * Also works on strings, fixes IE < 9 to allow an explicit undefined\r\n\t\t * for the 2nd argument (as in Firefox), and prevents errors when\r\n\t\t * called on other DOM objects.\r\n\t\t */\r\n\t\tvar _slice = (function() {\r\n\t\t\tvar slice = Array.prototype.slice;\r\n\r\n\t\t\ttry {\r\n\t\t\t\t// Can\'t be used with DOM elements in IE < 9\r\n\t\t\t\tslice.call(document.documentElement);\r\n\t\t\t\treturn slice;\r\n\t\t\t} catch (e) { // Fails in IE < 9\r\n\r\n\t\t\t\t// This will work for genuine arrays, array-like objects, \r\n\t\t\t\t// NamedNodeMap (attributes, entities, notations),\r\n\t\t\t\t// NodeList (e.g., getElementsByTagName), HTMLCollection (e.g., childNodes),\r\n\t\t\t\t// and will not fail on other DOM objects (as do DOM elements in IE < 9)\r\n\t\t\t\treturn function(begin, end) {\r\n\t\t\t\t\tvar len = this.length;\r\n\r\n\t\t\t\t\tif (typeof begin !== "number") {\r\n\t\t\t\t\t\tbegin = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// IE < 9 gets unhappy with an undefined end argument\r\n\t\t\t\t\tif (typeof end !== "number") {\r\n\t\t\t\t\t\tend = len;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// For native Array objects, we use the native slice function\r\n\t\t\t\t\tif (this.slice) {\r\n\t\t\t\t\t\treturn slice.call(this, begin, end);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// For array like object we handle it ourselves.\r\n\t\t\t\t\tvar i,\r\n\t\t\t\t\t\t\tcloned = [],\r\n\t\t\t\t\t\t\t// Handle negative value for "begin"\r\n\t\t\t\t\t\t\tstart = (begin >= 0) ? begin : Math.max(0, len + begin),\r\n\t\t\t\t\t\t\t// Handle negative value for "end"\r\n\t\t\t\t\t\t\tupTo = end < 0 ? len + end : Math.min(end, len),\r\n\t\t\t\t\t\t\t// Actual expected size of the slice\r\n\t\t\t\t\t\t\tsize = upTo - start;\r\n\r\n\t\t\t\t\tif (size > 0) {\r\n\t\t\t\t\t\tcloned = new Array(size);\r\n\t\t\t\t\t\tif (this.charAt) {\r\n\t\t\t\t\t\t\tfor (i = 0; i < size; i++) {\r\n\t\t\t\t\t\t\t\tcloned[i] = this.charAt(start + i);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tfor (i = 0; i < size; i++) {\r\n\t\t\t\t\t\t\t\tcloned[i] = this[start + i];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn cloned;\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t})();\r\n\r\n\t\t/* .indexOf doesn\'t exist in IE<9 */\r\n\t\tvar _inArray = (function() {\r\n\t\t\tif (Array.prototype.includes) {\r\n\t\t\t\treturn function(arr, val) {\r\n\t\t\t\t\treturn arr.includes(val);\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\tif (Array.prototype.indexOf) {\r\n\t\t\t\treturn function(arr, val) {\r\n\t\t\t\t\treturn arr.indexOf(val) >= 0;\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\treturn function(arr, val) {\r\n\t\t\t\tfor (var i = 0; i < arr.length; i++) {\r\n\t\t\t\t\tif (arr[i] === val) {\r\n\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn false;\r\n\t\t\t};\r\n\t\t});\r\n\r\n\t\tfunction sanitizeElements(elements) {\r\n\t\t\t/* Unwrap jQuery/Zepto objects. */\r\n\t\t\tif (Type.isWrapped(elements)) {\r\n\t\t\t\telements = _slice.call(elements);\r\n\t\t\t\t/* Wrap a single element in an array so that $.each() can iterate with the element instead of its node\'s children. */\r\n\t\t\t} else if (Type.isNode(elements)) {\r\n\t\t\t\telements = [elements];\r\n\t\t\t}\r\n\r\n\t\t\treturn elements;\r\n\t\t}\r\n\r\n\t\tvar Type = {\r\n\t\t\tisNumber: function(variable) {\r\n\t\t\t\treturn (typeof variable === "number");\r\n\t\t\t},\r\n\t\t\tisString: function(variable) {\r\n\t\t\t\treturn (typeof variable === "string");\r\n\t\t\t},\r\n\t\t\tisArray: Array.isArray || function(variable) {\r\n\t\t\t\treturn Object.prototype.toString.call(variable) === "[object Array]";\r\n\t\t\t},\r\n\t\t\tisFunction: function(variable) {\r\n\t\t\t\treturn Object.prototype.toString.call(variable) === "[object Function]";\r\n\t\t\t},\r\n\t\t\tisNode: function(variable) {\r\n\t\t\t\treturn variable && variable.nodeType;\r\n\t\t\t},\r\n\t\t\t/* Determine if variable is an array-like wrapped jQuery, Zepto or similar element, or even a NodeList etc. */\r\n\t\t\t/* NOTE: HTMLFormElements also have a length. */\r\n\t\t\tisWrapped: function(variable) {\r\n\t\t\t\treturn variable\r\n\t\t\t\t\t\t&& variable !== window\r\n\t\t\t\t\t\t&& Type.isNumber(variable.length)\r\n\t\t\t\t\t\t&& !Type.isString(variable)\r\n\t\t\t\t\t\t&& !Type.isFunction(variable)\r\n\t\t\t\t\t\t&& !Type.isNode(variable)\r\n\t\t\t\t\t\t&& (variable.length === 0 || Type.isNode(variable[0]));\r\n\t\t\t},\r\n\t\t\tisSVG: function(variable) {\r\n\t\t\t\treturn window.SVGElement && (variable instanceof window.SVGElement);\r\n\t\t\t},\r\n\t\t\tisEmptyObject: function(variable) {\r\n\t\t\t\tfor (var name in variable) {\r\n\t\t\t\t\tif (variable.hasOwnProperty(name)) {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t/*****************\r\n\t\t Dependencies\r\n\t\t *****************/\r\n\r\n\t\tvar $,\r\n\t\t\t\tisJQuery = false;\r\n\r\n\t\tif (global.fn && global.fn.jquery) {\r\n\t\t\t$ = global;\r\n\t\t\tisJQuery = true;\r\n\t\t} else {\r\n\t\t\t$ = window.Velocity.Utilities;\r\n\t\t}\r\n\r\n\t\tif (IE <= 8 && !isJQuery) {\r\n\t\t\tthrow new Error("Velocity: IE8 and below require jQuery to be loaded before Velocity.");\r\n\t\t} else if (IE <= 7) {\r\n\t\t\t/* Revert to jQuery\'s $.animate(), and lose Velocity\'s extra features. */\r\n\t\t\tjQuery.fn.velocity = jQuery.fn.animate;\r\n\r\n\t\t\t/* Now that $.fn.velocity is aliased, abort this Velocity declaration. */\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t/*****************\r\n\t\t Constants\r\n\t\t *****************/\r\n\r\n\t\tvar DURATION_DEFAULT = 400,\r\n\t\t\t\tEASING_DEFAULT = "swing";\r\n\r\n\t\t/*************\r\n\t\t State\r\n\t\t *************/\r\n\r\n\t\tvar Velocity = {\r\n\t\t\t/* Container for page-wide Velocity state data. */\r\n\t\t\tState: {\r\n\t\t\t\t/* Detect mobile devices to determine if mobileHA should be turned on. */\r\n\t\t\t\tisMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(window.navigator.userAgent),\r\n\t\t\t\t/* The mobileHA option\'s behavior changes on older Android devices (Gingerbread, versions 2.3.3-2.3.7). */\r\n\t\t\t\tisAndroid: /Android/i.test(window.navigator.userAgent),\r\n\t\t\t\tisGingerbread: /Android 2\\.3\\.[3-7]/i.test(window.navigator.userAgent),\r\n\t\t\t\tisChrome: window.chrome,\r\n\t\t\t\tisFirefox: /Firefox/i.test(window.navigator.userAgent),\r\n\t\t\t\t/* Create a cached element for re-use when checking for CSS property prefixes. */\r\n\t\t\t\tprefixElement: document.createElement("div"),\r\n\t\t\t\t/* Cache every prefix match to avoid repeating lookups. */\r\n\t\t\t\tprefixMatches: {},\r\n\t\t\t\t/* Cache the anchor used for animating window scrolling. */\r\n\t\t\t\tscrollAnchor: null,\r\n\t\t\t\t/* Cache the browser-specific property names associated with the scroll anchor. */\r\n\t\t\t\tscrollPropertyLeft: null,\r\n\t\t\t\tscrollPropertyTop: null,\r\n\t\t\t\t/* Keep track of whether our RAF tick is running. */\r\n\t\t\t\tisTicking: false,\r\n\t\t\t\t/* Container for every in-progress call to Velocity. */\r\n\t\t\t\tcalls: [],\r\n\t\t\t\tdelayedElements: {\r\n\t\t\t\t\tcount: 0\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/* Velocity\'s custom CSS stack. Made global for unit testing. */\r\n\t\t\tCSS: {/* Defined below. */},\r\n\t\t\t/* A shim of the jQuery utility functions used by Velocity -- provided by Velocity\'s optional jQuery shim. */\r\n\t\t\tUtilities: $,\r\n\t\t\t/* Container for the user\'s custom animation redirects that are referenced by name in place of the properties map argument. */\r\n\t\t\tRedirects: {/* Manually registered by the user. */},\r\n\t\t\tEasings: {/* Defined below. */},\r\n\t\t\t/* Attempt to use ES6 Promises by default. Users can override this with a third-party promises library. */\r\n\t\t\tPromise: window.Promise,\r\n\t\t\t/* Velocity option defaults, which can be overriden by the user. */\r\n\t\t\tdefaults: {\r\n\t\t\t\tqueue: "",\r\n\t\t\t\tduration: DURATION_DEFAULT,\r\n\t\t\t\teasing: EASING_DEFAULT,\r\n\t\t\t\tbegin: undefined,\r\n\t\t\t\tcomplete: undefined,\r\n\t\t\t\tprogress: undefined,\r\n\t\t\t\tdisplay: undefined,\r\n\t\t\t\tvisibility: undefined,\r\n\t\t\t\tloop: false,\r\n\t\t\t\tdelay: false,\r\n\t\t\t\tmobileHA: true,\r\n\t\t\t\t/* Advanced: Set to false to prevent property values from being cached between consecutive Velocity-initiated chain calls. */\r\n\t\t\t\t_cacheValues: true,\r\n\t\t\t\t/* Advanced: Set to false if the promise should always resolve on empty element lists. */\r\n\t\t\t\tpromiseRejectEmpty: true\r\n\t\t\t},\r\n\t\t\t/* A design goal of Velocity is to cache data wherever possible in order to avoid DOM requerying. Accordingly, each element has a data cache. */\r\n\t\t\tinit: function(element) {\r\n\t\t\t\t$.data(element, "velocity", {\r\n\t\t\t\t\t/* Store whether this is an SVG element, since its properties are retrieved and updated differently than standard HTML elements. */\r\n\t\t\t\t\tisSVG: Type.isSVG(element),\r\n\t\t\t\t\t/* Keep track of whether the element is currently being animated by Velocity.\r\n\t\t\t\t\t This is used to ensure that property values are not transferred between non-consecutive (stale) calls. */\r\n\t\t\t\t\tisAnimating: false,\r\n\t\t\t\t\t/* A reference to the element\'s live computedStyle object. Learn more here: https://developer.mozilla.org/en/docs/Web/API/window.getComputedStyle */\r\n\t\t\t\t\tcomputedStyle: null,\r\n\t\t\t\t\t/* Tween data is cached for each animation on the element so that data can be passed across calls --\r\n\t\t\t\t\t in particular, end values are used as subsequent start values in consecutive Velocity calls. */\r\n\t\t\t\t\ttweensContainer: null,\r\n\t\t\t\t\t/* The full root property values of each CSS hook being animated on this element are cached so that:\r\n\t\t\t\t\t 1) Concurrently-animating hooks sharing the same root can have their root values\' merged into one while tweening.\r\n\t\t\t\t\t 2) Post-hook-injection root values can be transferred over to consecutively chained Velocity calls as starting root values. */\r\n\t\t\t\t\trootPropertyValueCache: {},\r\n\t\t\t\t\t/* A cache for transform updates, which must be manually flushed via CSS.flushTransformCache(). */\r\n\t\t\t\t\ttransformCache: {}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/* A parallel to jQuery\'s $.css(), used for getting/setting Velocity\'s hooked CSS properties. */\r\n\t\t\thook: null, /* Defined below. */\r\n\t\t\t/* Velocity-wide animation time remapping for testing purposes. */\r\n\t\t\tmock: false,\r\n\t\t\tversion: {major: 1, minor: 5, patch: 2},\r\n\t\t\t/* Set to 1 or 2 (most verbose) to output debug info to console. */\r\n\t\t\tdebug: false,\r\n\t\t\t/* Use rAF high resolution timestamp when available */\r\n\t\t\ttimestamp: true,\r\n\t\t\t/* Pause all animations */\r\n\t\t\tpauseAll: function(queueName) {\r\n\t\t\t\tvar currentTime = (new Date()).getTime();\r\n\r\n\t\t\t\t$.each(Velocity.State.calls, function(i, activeCall) {\r\n\r\n\t\t\t\t\tif (activeCall) {\r\n\r\n\t\t\t\t\t\t/* If we have a queueName and this call is not on that queue, skip */\r\n\t\t\t\t\t\tif (queueName !== undefined && ((activeCall[2].queue !== queueName) || (activeCall[2].queue === false))) {\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Set call to paused */\r\n\t\t\t\t\t\tactiveCall[5] = {\r\n\t\t\t\t\t\t\tresume: false\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\t/* Pause timers on any currently delayed calls */\r\n\t\t\t\t$.each(Velocity.State.delayedElements, function(k, element) {\r\n\t\t\t\t\tif (!element) {\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tpauseDelayOnElement(element, currentTime);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/* Resume all animations */\r\n\t\t\tresumeAll: function(queueName) {\r\n\t\t\t\tvar currentTime = (new Date()).getTime();\r\n\r\n\t\t\t\t$.each(Velocity.State.calls, function(i, activeCall) {\r\n\r\n\t\t\t\t\tif (activeCall) {\r\n\r\n\t\t\t\t\t\t/* If we have a queueName and this call is not on that queue, skip */\r\n\t\t\t\t\t\tif (queueName !== undefined && ((activeCall[2].queue !== queueName) || (activeCall[2].queue === false))) {\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Set call to resumed if it was paused */\r\n\t\t\t\t\t\tif (activeCall[5]) {\r\n\t\t\t\t\t\t\tactiveCall[5].resume = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t/* Resume timers on any currently delayed calls */\r\n\t\t\t\t$.each(Velocity.State.delayedElements, function(k, element) {\r\n\t\t\t\t\tif (!element) {\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tresumeDelayOnElement(element, currentTime);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t/* Retrieve the appropriate scroll anchor and property name for the browser: https://developer.mozilla.org/en-US/docs/Web/API/Window.scrollY */\r\n\t\tif (window.pageYOffset !== undefined) {\r\n\t\t\tVelocity.State.scrollAnchor = window;\r\n\t\t\tVelocity.State.scrollPropertyLeft = "pageXOffset";\r\n\t\t\tVelocity.State.scrollPropertyTop = "pageYOffset";\r\n\t\t} else {\r\n\t\t\tVelocity.State.scrollAnchor = document.documentElement || document.body.parentNode || document.body;\r\n\t\t\tVelocity.State.scrollPropertyLeft = "scrollLeft";\r\n\t\t\tVelocity.State.scrollPropertyTop = "scrollTop";\r\n\t\t}\r\n\r\n\t\t/* Shorthand alias for jQuery\'s $.data() utility. */\r\n\t\tfunction Data(element) {\r\n\t\t\t/* Hardcode a reference to the plugin name. */\r\n\t\t\tvar response = $.data(element, "velocity");\r\n\r\n\t\t\t/* jQuery <=1.4.2 returns null instead of undefined when no match is found. We normalize this behavior. */\r\n\t\t\treturn response === null ? undefined : response;\r\n\t\t}\r\n\r\n\t\t/**************\r\n\t\t Delay Timer\r\n\t\t **************/\r\n\r\n\t\tfunction pauseDelayOnElement(element, currentTime) {\r\n\t\t\t/* Check for any delay timers, and pause the set timeouts (while preserving time data)\r\n\t\t\t to be resumed when the "resume" command is issued */\r\n\t\t\tvar data = Data(element);\r\n\t\t\tif (data && data.delayTimer && !data.delayPaused) {\r\n\t\t\t\tdata.delayRemaining = data.delay - currentTime + data.delayBegin;\r\n\t\t\t\tdata.delayPaused = true;\r\n\t\t\t\tclearTimeout(data.delayTimer.setTimeout);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tfunction resumeDelayOnElement(element, currentTime) {\r\n\t\t\t/* Check for any paused timers and resume */\r\n\t\t\tvar data = Data(element);\r\n\t\t\tif (data && data.delayTimer && data.delayPaused) {\r\n\t\t\t\t/* If the element was mid-delay, re initiate the timeout with the remaining delay */\r\n\t\t\t\tdata.delayPaused = false;\r\n\t\t\t\tdata.delayTimer.setTimeout = setTimeout(data.delayTimer.next, data.delayRemaining);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\r\n\t\t/**************\r\n\t\t Easing\r\n\t\t **************/\r\n\r\n\t\t/* Step easing generator. */\r\n\t\tfunction generateStep(steps) {\r\n\t\t\treturn function(p) {\r\n\t\t\t\treturn Math.round(p * steps) * (1 / steps);\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t/* Bezier curve function generator. Copyright Gaetan Renaudeau. MIT License: http://en.wikipedia.org/wiki/MIT_License */\r\n\t\tfunction generateBezier(mX1, mY1, mX2, mY2) {\r\n\t\t\tvar NEWTON_ITERATIONS = 4,\r\n\t\t\t\t\tNEWTON_MIN_SLOPE = 0.001,\r\n\t\t\t\t\tSUBDIVISION_PRECISION = 0.0000001,\r\n\t\t\t\t\tSUBDIVISION_MAX_ITERATIONS = 10,\r\n\t\t\t\t\tkSplineTableSize = 11,\r\n\t\t\t\t\tkSampleStepSize = 1.0 / (kSplineTableSize - 1.0),\r\n\t\t\t\t\tfloat32ArraySupported = "Float32Array" in window;\r\n\r\n\t\t\t/* Must contain four arguments. */\r\n\t\t\tif (arguments.length !== 4) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\t/* Arguments must be numbers. */\r\n\t\t\tfor (var i = 0; i < 4; ++i) {\r\n\t\t\t\tif (typeof arguments[i] !== "number" || isNaN(arguments[i]) || !isFinite(arguments[i])) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/* X values must be in the [0, 1] range. */\r\n\t\t\tmX1 = Math.min(mX1, 1);\r\n\t\t\tmX2 = Math.min(mX2, 1);\r\n\t\t\tmX1 = Math.max(mX1, 0);\r\n\t\t\tmX2 = Math.max(mX2, 0);\r\n\r\n\t\t\tvar mSampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);\r\n\r\n\t\t\tfunction A(aA1, aA2) {\r\n\t\t\t\treturn 1.0 - 3.0 * aA2 + 3.0 * aA1;\r\n\t\t\t}\r\n\t\t\tfunction B(aA1, aA2) {\r\n\t\t\t\treturn 3.0 * aA2 - 6.0 * aA1;\r\n\t\t\t}\r\n\t\t\tfunction C(aA1) {\r\n\t\t\t\treturn 3.0 * aA1;\r\n\t\t\t}\r\n\r\n\t\t\tfunction calcBezier(aT, aA1, aA2) {\r\n\t\t\t\treturn ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;\r\n\t\t\t}\r\n\r\n\t\t\tfunction getSlope(aT, aA1, aA2) {\r\n\t\t\t\treturn 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);\r\n\t\t\t}\r\n\r\n\t\t\tfunction newtonRaphsonIterate(aX, aGuessT) {\r\n\t\t\t\tfor (var i = 0; i < NEWTON_ITERATIONS; ++i) {\r\n\t\t\t\t\tvar currentSlope = getSlope(aGuessT, mX1, mX2);\r\n\r\n\t\t\t\t\tif (currentSlope === 0.0) {\r\n\t\t\t\t\t\treturn aGuessT;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tvar currentX = calcBezier(aGuessT, mX1, mX2) - aX;\r\n\t\t\t\t\taGuessT -= currentX / currentSlope;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn aGuessT;\r\n\t\t\t}\r\n\r\n\t\t\tfunction calcSampleValues() {\r\n\t\t\t\tfor (var i = 0; i < kSplineTableSize; ++i) {\r\n\t\t\t\t\tmSampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tfunction binarySubdivide(aX, aA, aB) {\r\n\t\t\t\tvar currentX, currentT, i = 0;\r\n\r\n\t\t\t\tdo {\r\n\t\t\t\t\tcurrentT = aA + (aB - aA) / 2.0;\r\n\t\t\t\t\tcurrentX = calcBezier(currentT, mX1, mX2) - aX;\r\n\t\t\t\t\tif (currentX > 0.0) {\r\n\t\t\t\t\t\taB = currentT;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\taA = currentT;\r\n\t\t\t\t\t}\r\n\t\t\t\t} while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);\r\n\r\n\t\t\t\treturn currentT;\r\n\t\t\t}\r\n\r\n\t\t\tfunction getTForX(aX) {\r\n\t\t\t\tvar intervalStart = 0.0,\r\n\t\t\t\t\t\tcurrentSample = 1,\r\n\t\t\t\t\t\tlastSample = kSplineTableSize - 1;\r\n\r\n\t\t\t\tfor (; currentSample !== lastSample && mSampleValues[currentSample] <= aX; ++currentSample) {\r\n\t\t\t\t\tintervalStart += kSampleStepSize;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t--currentSample;\r\n\r\n\t\t\t\tvar dist = (aX - mSampleValues[currentSample]) / (mSampleValues[currentSample + 1] - mSampleValues[currentSample]),\r\n\t\t\t\t\t\tguessForT = intervalStart + dist * kSampleStepSize,\r\n\t\t\t\t\t\tinitialSlope = getSlope(guessForT, mX1, mX2);\r\n\r\n\t\t\t\tif (initialSlope >= NEWTON_MIN_SLOPE) {\r\n\t\t\t\t\treturn newtonRaphsonIterate(aX, guessForT);\r\n\t\t\t\t} else if (initialSlope === 0.0) {\r\n\t\t\t\t\treturn guessForT;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tvar _precomputed = false;\r\n\r\n\t\t\tfunction precompute() {\r\n\t\t\t\t_precomputed = true;\r\n\t\t\t\tif (mX1 !== mY1 || mX2 !== mY2) {\r\n\t\t\t\t\tcalcSampleValues();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tvar f = function(aX) {\r\n\t\t\t\tif (!_precomputed) {\r\n\t\t\t\t\tprecompute();\r\n\t\t\t\t}\r\n\t\t\t\tif (mX1 === mY1 && mX2 === mY2) {\r\n\t\t\t\t\treturn aX;\r\n\t\t\t\t}\r\n\t\t\t\tif (aX === 0) {\r\n\t\t\t\t\treturn 0;\r\n\t\t\t\t}\r\n\t\t\t\tif (aX === 1) {\r\n\t\t\t\t\treturn 1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn calcBezier(getTForX(aX), mY1, mY2);\r\n\t\t\t};\r\n\r\n\t\t\tf.getControlPoints = function() {\r\n\t\t\t\treturn [{x: mX1, y: mY1}, {x: mX2, y: mY2}];\r\n\t\t\t};\r\n\r\n\t\t\tvar str = "generateBezier(" + [mX1, mY1, mX2, mY2] + ")";\r\n\t\t\tf.toString = function() {\r\n\t\t\t\treturn str;\r\n\t\t\t};\r\n\r\n\t\t\treturn f;\r\n\t\t}\r\n\r\n\t\t/* Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen Bok. MIT License: http://en.wikipedia.org/wiki/MIT_License */\r\n\t\t/* Given a tension, friction, and duration, a simulation at 60FPS will first run without a defined duration in order to calculate the full path. A second pass\r\n\t\t then adjusts the time delta -- using the relation between actual time and duration -- to calculate the path for the duration-constrained animation. */\r\n\t\tvar generateSpringRK4 = (function() {\r\n\t\t\tfunction springAccelerationForState(state) {\r\n\t\t\t\treturn (-state.tension * state.x) - (state.friction * state.v);\r\n\t\t\t}\r\n\r\n\t\t\tfunction springEvaluateStateWithDerivative(initialState, dt, derivative) {\r\n\t\t\t\tvar state = {\r\n\t\t\t\t\tx: initialState.x + derivative.dx * dt,\r\n\t\t\t\t\tv: initialState.v + derivative.dv * dt,\r\n\t\t\t\t\ttension: initialState.tension,\r\n\t\t\t\t\tfriction: initialState.friction\r\n\t\t\t\t};\r\n\r\n\t\t\t\treturn {dx: state.v, dv: springAccelerationForState(state)};\r\n\t\t\t}\r\n\r\n\t\t\tfunction springIntegrateState(state, dt) {\r\n\t\t\t\tvar a = {\r\n\t\t\t\t\tdx: state.v,\r\n\t\t\t\t\tdv: springAccelerationForState(state)\r\n\t\t\t\t},\r\n\t\t\t\t\t\tb = springEvaluateStateWithDerivative(state, dt * 0.5, a),\r\n\t\t\t\t\t\tc = springEvaluateStateWithDerivative(state, dt * 0.5, b),\r\n\t\t\t\t\t\td = springEvaluateStateWithDerivative(state, dt, c),\r\n\t\t\t\t\t\tdxdt = 1.0 / 6.0 * (a.dx + 2.0 * (b.dx + c.dx) + d.dx),\r\n\t\t\t\t\t\tdvdt = 1.0 / 6.0 * (a.dv + 2.0 * (b.dv + c.dv) + d.dv);\r\n\r\n\t\t\t\tstate.x = state.x + dxdt * dt;\r\n\t\t\t\tstate.v = state.v + dvdt * dt;\r\n\r\n\t\t\t\treturn state;\r\n\t\t\t}\r\n\r\n\t\t\treturn function springRK4Factory(tension, friction, duration) {\r\n\r\n\t\t\t\tvar initState = {\r\n\t\t\t\t\tx: -1,\r\n\t\t\t\t\tv: 0,\r\n\t\t\t\t\ttension: null,\r\n\t\t\t\t\tfriction: null\r\n\t\t\t\t},\r\n\t\t\t\t\t\tpath = [0],\r\n\t\t\t\t\t\ttime_lapsed = 0,\r\n\t\t\t\t\t\ttolerance = 1 / 10000,\r\n\t\t\t\t\t\tDT = 16 / 1000,\r\n\t\t\t\t\t\thave_duration, dt, last_state;\r\n\r\n\t\t\t\ttension = parseFloat(tension) || 500;\r\n\t\t\t\tfriction = parseFloat(friction) || 20;\r\n\t\t\t\tduration = duration || null;\r\n\r\n\t\t\t\tinitState.tension = tension;\r\n\t\t\t\tinitState.friction = friction;\r\n\r\n\t\t\t\thave_duration = duration !== null;\r\n\r\n\t\t\t\t/* Calculate the actual time it takes for this animation to complete with the provided conditions. */\r\n\t\t\t\tif (have_duration) {\r\n\t\t\t\t\t/* Run the simulation without a duration. */\r\n\t\t\t\t\ttime_lapsed = springRK4Factory(tension, friction);\r\n\t\t\t\t\t/* Compute the adjusted time delta. */\r\n\t\t\t\t\tdt = time_lapsed / duration * DT;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdt = DT;\r\n\t\t\t\t}\r\n\r\n\t\t\t\twhile (true) {\r\n\t\t\t\t\t/* Next/step function .*/\r\n\t\t\t\t\tlast_state = springIntegrateState(last_state || initState, dt);\r\n\t\t\t\t\t/* Store the position. */\r\n\t\t\t\t\tpath.push(1 + last_state.x);\r\n\t\t\t\t\ttime_lapsed += 16;\r\n\t\t\t\t\t/* If the change threshold is reached, break. */\r\n\t\t\t\t\tif (!(Math.abs(last_state.x) > tolerance && Math.abs(last_state.v) > tolerance)) {\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* If duration is not defined, return the actual time required for completing this animation. Otherwise, return a closure that holds the\r\n\t\t\t\t computed path and returns a snapshot of the position according to a given percentComplete. */\r\n\t\t\t\treturn !have_duration ? time_lapsed : function(percentComplete) {\r\n\t\t\t\t\treturn path[ (percentComplete * (path.length - 1)) | 0 ];\r\n\t\t\t\t};\r\n\t\t\t};\r\n\t\t}());\r\n\r\n\t\t/* jQuery easings. */\r\n\t\tVelocity.Easings = {\r\n\t\t\tlinear: function(p) {\r\n\t\t\t\treturn p;\r\n\t\t\t},\r\n\t\t\tswing: function(p) {\r\n\t\t\t\treturn 0.5 - Math.cos(p * Math.PI) / 2;\r\n\t\t\t},\r\n\t\t\t/* Bonus "spring" easing, which is a less exaggerated version of easeInOutElastic. */\r\n\t\t\tspring: function(p) {\r\n\t\t\t\treturn 1 - (Math.cos(p * 4.5 * Math.PI) * Math.exp(-p * 6));\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t/* CSS3 and Robert Penner easings. */\r\n\t\t$.each(\r\n\t\t\t\t[\r\n\t\t\t\t\t["ease", [0.25, 0.1, 0.25, 1.0]],\r\n\t\t\t\t\t["ease-in", [0.42, 0.0, 1.00, 1.0]],\r\n\t\t\t\t\t["ease-out", [0.00, 0.0, 0.58, 1.0]],\r\n\t\t\t\t\t["ease-in-out", [0.42, 0.0, 0.58, 1.0]],\r\n\t\t\t\t\t["easeInSine", [0.47, 0, 0.745, 0.715]],\r\n\t\t\t\t\t["easeOutSine", [0.39, 0.575, 0.565, 1]],\r\n\t\t\t\t\t["easeInOutSine", [0.445, 0.05, 0.55, 0.95]],\r\n\t\t\t\t\t["easeInQuad", [0.55, 0.085, 0.68, 0.53]],\r\n\t\t\t\t\t["easeOutQuad", [0.25, 0.46, 0.45, 0.94]],\r\n\t\t\t\t\t["easeInOutQuad", [0.455, 0.03, 0.515, 0.955]],\r\n\t\t\t\t\t["easeInCubic", [0.55, 0.055, 0.675, 0.19]],\r\n\t\t\t\t\t["easeOutCubic", [0.215, 0.61, 0.355, 1]],\r\n\t\t\t\t\t["easeInOutCubic", [0.645, 0.045, 0.355, 1]],\r\n\t\t\t\t\t["easeInQuart", [0.895, 0.03, 0.685, 0.22]],\r\n\t\t\t\t\t["easeOutQuart", [0.165, 0.84, 0.44, 1]],\r\n\t\t\t\t\t["easeInOutQuart", [0.77, 0, 0.175, 1]],\r\n\t\t\t\t\t["easeInQuint", [0.755, 0.05, 0.855, 0.06]],\r\n\t\t\t\t\t["easeOutQuint", [0.23, 1, 0.32, 1]],\r\n\t\t\t\t\t["easeInOutQuint", [0.86, 0, 0.07, 1]],\r\n\t\t\t\t\t["easeInExpo", [0.95, 0.05, 0.795, 0.035]],\r\n\t\t\t\t\t["easeOutExpo", [0.19, 1, 0.22, 1]],\r\n\t\t\t\t\t["easeInOutExpo", [1, 0, 0, 1]],\r\n\t\t\t\t\t["easeInCirc", [0.6, 0.04, 0.98, 0.335]],\r\n\t\t\t\t\t["easeOutCirc", [0.075, 0.82, 0.165, 1]],\r\n\t\t\t\t\t["easeInOutCirc", [0.785, 0.135, 0.15, 0.86]]\r\n\t\t\t\t], function(i, easingArray) {\r\n\t\t\tVelocity.Easings[easingArray[0]] = generateBezier.apply(null, easingArray[1]);\r\n\t\t});\r\n\r\n\t\t/* Determine the appropriate easing type given an easing input. */\r\n\t\tfunction getEasing(value, duration) {\r\n\t\t\tvar easing = value;\r\n\r\n\t\t\t/* The easing option can either be a string that references a pre-registered easing,\r\n\t\t\t or it can be a two-/four-item array of integers to be converted into a bezier/spring function. */\r\n\t\t\tif (Type.isString(value)) {\r\n\t\t\t\t/* Ensure that the easing has been assigned to jQuery\'s Velocity.Easings object. */\r\n\t\t\t\tif (!Velocity.Easings[value]) {\r\n\t\t\t\t\teasing = false;\r\n\t\t\t\t}\r\n\t\t\t} else if (Type.isArray(value) && value.length === 1) {\r\n\t\t\t\teasing = generateStep.apply(null, value);\r\n\t\t\t} else if (Type.isArray(value) && value.length === 2) {\r\n\t\t\t\t/* springRK4 must be passed the animation\'s duration. */\r\n\t\t\t\t/* Note: If the springRK4 array contains non-numbers, generateSpringRK4() returns an easing\r\n\t\t\t\t function generated with default tension and friction values. */\r\n\t\t\t\teasing = generateSpringRK4.apply(null, value.concat([duration]));\r\n\t\t\t} else if (Type.isArray(value) && value.length === 4) {\r\n\t\t\t\t/* Note: If the bezier array contains non-numbers, generateBezier() returns false. */\r\n\t\t\t\teasing = generateBezier.apply(null, value);\r\n\t\t\t} else {\r\n\t\t\t\teasing = false;\r\n\t\t\t}\r\n\r\n\t\t\t/* Revert to the Velocity-wide default easing type, or fall back to "swing" (which is also jQuery\'s default)\r\n\t\t\t if the Velocity-wide default has been incorrectly modified. */\r\n\t\t\tif (easing === false) {\r\n\t\t\t\tif (Velocity.Easings[Velocity.defaults.easing]) {\r\n\t\t\t\t\teasing = Velocity.defaults.easing;\r\n\t\t\t\t} else {\r\n\t\t\t\t\teasing = EASING_DEFAULT;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn easing;\r\n\t\t}\r\n\r\n\t\t/*****************\r\n\t\t CSS Stack\r\n\t\t *****************/\r\n\r\n\t\t/* The CSS object is a highly condensed and performant CSS stack that fully replaces jQuery\'s.\r\n\t\t It handles the validation, getting, and setting of both standard CSS properties and CSS property hooks. */\r\n\t\t/* Note: A "CSS" shorthand is aliased so that our code is easier to read. */\r\n\t\tvar CSS = Velocity.CSS = {\r\n\t\t\t/*************\r\n\t\t\t RegEx\r\n\t\t\t *************/\r\n\r\n\t\t\tRegEx: {\r\n\t\t\t\tisHex: /^#([A-f\\d]{3}){1,2}$/i,\r\n\t\t\t\t/* Unwrap a property value\'s surrounding text, e.g. "rgba(4, 3, 2, 1)" ==> "4, 3, 2, 1" and "rect(4px 3px 2px 1px)" ==> "4px 3px 2px 1px". */\r\n\t\t\t\tvalueUnwrap: /^[A-z]+\\((.*)\\)$/i,\r\n\t\t\t\twrappedValueAlreadyExtracted: /[0-9.]+ [0-9.]+ [0-9.]+( [0-9.]+)?/,\r\n\t\t\t\t/* Split a multi-value property into an array of subvalues, e.g. "rgba(4, 3, 2, 1) 4px 3px 2px 1px" ==> [ "rgba(4, 3, 2, 1)", "4px", "3px", "2px", "1px" ]. */\r\n\t\t\t\tvalueSplit: /([A-z]+\\(.+\\))|(([A-z0-9#-.]+?)(?=\\s|$))/ig\r\n\t\t\t},\r\n\t\t\t/************\r\n\t\t\t Lists\r\n\t\t\t ************/\r\n\r\n\t\t\tLists: {\r\n\t\t\t\tcolors: ["fill", "stroke", "stopColor", "color", "backgroundColor", "borderColor", "borderTopColor", "borderRightColor", "borderBottomColor", "borderLeftColor", "outlineColor"],\r\n\t\t\t\ttransformsBase: ["translateX", "translateY", "scale", "scaleX", "scaleY", "skewX", "skewY", "rotateZ"],\r\n\t\t\t\ttransforms3D: ["transformPerspective", "translateZ", "scaleZ", "rotateX", "rotateY"],\r\n\t\t\t\tunits: [\r\n\t\t\t\t\t"%", // relative\r\n\t\t\t\t\t"em", "ex", "ch", "rem", // font relative\r\n\t\t\t\t\t"vw", "vh", "vmin", "vmax", // viewport relative\r\n\t\t\t\t\t"cm", "mm", "Q", "in", "pc", "pt", "px", // absolute lengths\r\n\t\t\t\t\t"deg", "grad", "rad", "turn", // angles\r\n\t\t\t\t\t"s", "ms" // time\r\n\t\t\t\t],\r\n\t\t\t\tcolorNames: {\r\n\t\t\t\t\t"aliceblue": "240,248,255",\r\n\t\t\t\t\t"antiquewhite": "250,235,215",\r\n\t\t\t\t\t"aquamarine": "127,255,212",\r\n\t\t\t\t\t"aqua": "0,255,255",\r\n\t\t\t\t\t"azure": "240,255,255",\r\n\t\t\t\t\t"beige": "245,245,220",\r\n\t\t\t\t\t"bisque": "255,228,196",\r\n\t\t\t\t\t"black": "0,0,0",\r\n\t\t\t\t\t"blanchedalmond": "255,235,205",\r\n\t\t\t\t\t"blueviolet": "138,43,226",\r\n\t\t\t\t\t"blue": "0,0,255",\r\n\t\t\t\t\t"brown": "165,42,42",\r\n\t\t\t\t\t"burlywood": "222,184,135",\r\n\t\t\t\t\t"cadetblue": "95,158,160",\r\n\t\t\t\t\t"chartreuse": "127,255,0",\r\n\t\t\t\t\t"chocolate": "210,105,30",\r\n\t\t\t\t\t"coral": "255,127,80",\r\n\t\t\t\t\t"cornflowerblue": "100,149,237",\r\n\t\t\t\t\t"cornsilk": "255,248,220",\r\n\t\t\t\t\t"crimson": "220,20,60",\r\n\t\t\t\t\t"cyan": "0,255,255",\r\n\t\t\t\t\t"darkblue": "0,0,139",\r\n\t\t\t\t\t"darkcyan": "0,139,139",\r\n\t\t\t\t\t"darkgoldenrod": "184,134,11",\r\n\t\t\t\t\t"darkgray": "169,169,169",\r\n\t\t\t\t\t"darkgrey": "169,169,169",\r\n\t\t\t\t\t"darkgreen": "0,100,0",\r\n\t\t\t\t\t"darkkhaki": "189,183,107",\r\n\t\t\t\t\t"darkmagenta": "139,0,139",\r\n\t\t\t\t\t"darkolivegreen": "85,107,47",\r\n\t\t\t\t\t"darkorange": "255,140,0",\r\n\t\t\t\t\t"darkorchid": "153,50,204",\r\n\t\t\t\t\t"darkred": "139,0,0",\r\n\t\t\t\t\t"darksalmon": "233,150,122",\r\n\t\t\t\t\t"darkseagreen": "143,188,143",\r\n\t\t\t\t\t"darkslateblue": "72,61,139",\r\n\t\t\t\t\t"darkslategray": "47,79,79",\r\n\t\t\t\t\t"darkturquoise": "0,206,209",\r\n\t\t\t\t\t"darkviolet": "148,0,211",\r\n\t\t\t\t\t"deeppink": "255,20,147",\r\n\t\t\t\t\t"deepskyblue": "0,191,255",\r\n\t\t\t\t\t"dimgray": "105,105,105",\r\n\t\t\t\t\t"dimgrey": "105,105,105",\r\n\t\t\t\t\t"dodgerblue": "30,144,255",\r\n\t\t\t\t\t"firebrick": "178,34,34",\r\n\t\t\t\t\t"floralwhite": "255,250,240",\r\n\t\t\t\t\t"forestgreen": "34,139,34",\r\n\t\t\t\t\t"fuchsia": "255,0,255",\r\n\t\t\t\t\t"gainsboro": "220,220,220",\r\n\t\t\t\t\t"ghostwhite": "248,248,255",\r\n\t\t\t\t\t"gold": "255,215,0",\r\n\t\t\t\t\t"goldenrod": "218,165,32",\r\n\t\t\t\t\t"gray": "128,128,128",\r\n\t\t\t\t\t"grey": "128,128,128",\r\n\t\t\t\t\t"greenyellow": "173,255,47",\r\n\t\t\t\t\t"green": "0,128,0",\r\n\t\t\t\t\t"honeydew": "240,255,240",\r\n\t\t\t\t\t"hotpink": "255,105,180",\r\n\t\t\t\t\t"indianred": "205,92,92",\r\n\t\t\t\t\t"indigo": "75,0,130",\r\n\t\t\t\t\t"ivory": "255,255,240",\r\n\t\t\t\t\t"khaki": "240,230,140",\r\n\t\t\t\t\t"lavenderblush": "255,240,245",\r\n\t\t\t\t\t"lavender": "230,230,250",\r\n\t\t\t\t\t"lawngreen": "124,252,0",\r\n\t\t\t\t\t"lemonchiffon": "255,250,205",\r\n\t\t\t\t\t"lightblue": "173,216,230",\r\n\t\t\t\t\t"lightcoral": "240,128,128",\r\n\t\t\t\t\t"lightcyan": "224,255,255",\r\n\t\t\t\t\t"lightgoldenrodyellow": "250,250,210",\r\n\t\t\t\t\t"lightgray": "211,211,211",\r\n\t\t\t\t\t"lightgrey": "211,211,211",\r\n\t\t\t\t\t"lightgreen": "144,238,144",\r\n\t\t\t\t\t"lightpink": "255,182,193",\r\n\t\t\t\t\t"lightsalmon": "255,160,122",\r\n\t\t\t\t\t"lightseagreen": "32,178,170",\r\n\t\t\t\t\t"lightskyblue": "135,206,250",\r\n\t\t\t\t\t"lightslategray": "119,136,153",\r\n\t\t\t\t\t"lightsteelblue": "176,196,222",\r\n\t\t\t\t\t"lightyellow": "255,255,224",\r\n\t\t\t\t\t"limegreen": "50,205,50",\r\n\t\t\t\t\t"lime": "0,255,0",\r\n\t\t\t\t\t"linen": "250,240,230",\r\n\t\t\t\t\t"magenta": "255,0,255",\r\n\t\t\t\t\t"maroon": "128,0,0",\r\n\t\t\t\t\t"mediumaquamarine": "102,205,170",\r\n\t\t\t\t\t"mediumblue": "0,0,205",\r\n\t\t\t\t\t"mediumorchid": "186,85,211",\r\n\t\t\t\t\t"mediumpurple": "147,112,219",\r\n\t\t\t\t\t"mediumseagreen": "60,179,113",\r\n\t\t\t\t\t"mediumslateblue": "123,104,238",\r\n\t\t\t\t\t"mediumspringgreen": "0,250,154",\r\n\t\t\t\t\t"mediumturquoise": "72,209,204",\r\n\t\t\t\t\t"mediumvioletred": "199,21,133",\r\n\t\t\t\t\t"midnightblue": "25,25,112",\r\n\t\t\t\t\t"mintcream": "245,255,250",\r\n\t\t\t\t\t"mistyrose": "255,228,225",\r\n\t\t\t\t\t"moccasin": "255,228,181",\r\n\t\t\t\t\t"navajowhite": "255,222,173",\r\n\t\t\t\t\t"navy": "0,0,128",\r\n\t\t\t\t\t"oldlace": "253,245,230",\r\n\t\t\t\t\t"olivedrab": "107,142,35",\r\n\t\t\t\t\t"olive": "128,128,0",\r\n\t\t\t\t\t"orangered": "255,69,0",\r\n\t\t\t\t\t"orange": "255,165,0",\r\n\t\t\t\t\t"orchid": "218,112,214",\r\n\t\t\t\t\t"palegoldenrod": "238,232,170",\r\n\t\t\t\t\t"palegreen": "152,251,152",\r\n\t\t\t\t\t"paleturquoise": "175,238,238",\r\n\t\t\t\t\t"palevioletred": "219,112,147",\r\n\t\t\t\t\t"papayawhip": "255,239,213",\r\n\t\t\t\t\t"peachpuff": "255,218,185",\r\n\t\t\t\t\t"peru": "205,133,63",\r\n\t\t\t\t\t"pink": "255,192,203",\r\n\t\t\t\t\t"plum": "221,160,221",\r\n\t\t\t\t\t"powderblue": "176,224,230",\r\n\t\t\t\t\t"purple": "128,0,128",\r\n\t\t\t\t\t"red": "255,0,0",\r\n\t\t\t\t\t"rosybrown": "188,143,143",\r\n\t\t\t\t\t"royalblue": "65,105,225",\r\n\t\t\t\t\t"saddlebrown": "139,69,19",\r\n\t\t\t\t\t"salmon": "250,128,114",\r\n\t\t\t\t\t"sandybrown": "244,164,96",\r\n\t\t\t\t\t"seagreen": "46,139,87",\r\n\t\t\t\t\t"seashell": "255,245,238",\r\n\t\t\t\t\t"sienna": "160,82,45",\r\n\t\t\t\t\t"silver": "192,192,192",\r\n\t\t\t\t\t"skyblue": "135,206,235",\r\n\t\t\t\t\t"slateblue": "106,90,205",\r\n\t\t\t\t\t"slategray": "112,128,144",\r\n\t\t\t\t\t"snow": "255,250,250",\r\n\t\t\t\t\t"springgreen": "0,255,127",\r\n\t\t\t\t\t"steelblue": "70,130,180",\r\n\t\t\t\t\t"tan": "210,180,140",\r\n\t\t\t\t\t"teal": "0,128,128",\r\n\t\t\t\t\t"thistle": "216,191,216",\r\n\t\t\t\t\t"tomato": "255,99,71",\r\n\t\t\t\t\t"turquoise": "64,224,208",\r\n\t\t\t\t\t"violet": "238,130,238",\r\n\t\t\t\t\t"wheat": "245,222,179",\r\n\t\t\t\t\t"whitesmoke": "245,245,245",\r\n\t\t\t\t\t"white": "255,255,255",\r\n\t\t\t\t\t"yellowgreen": "154,205,50",\r\n\t\t\t\t\t"yellow": "255,255,0"\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/************\r\n\t\t\t Hooks\r\n\t\t\t ************/\r\n\r\n\t\t\t/* Hooks allow a subproperty (e.g. "boxShadowBlur") of a compound-value CSS property\r\n\t\t\t (e.g. "boxShadow: X Y Blur Spread Color") to be animated as if it were a discrete property. */\r\n\t\t\t/* Note: Beyond enabling fine-grained property animation, hooking is necessary since Velocity only\r\n\t\t\t tweens properties with single numeric values; unlike CSS transitions, Velocity does not interpolate compound-values. */\r\n\t\t\tHooks: {\r\n\t\t\t\t/********************\r\n\t\t\t\t Registration\r\n\t\t\t\t ********************/\r\n\r\n\t\t\t\t/* Templates are a concise way of indicating which subproperties must be individually registered for each compound-value CSS property. */\r\n\t\t\t\t/* Each template consists of the compound-value\'s base name, its constituent subproperty names, and those subproperties\' default values. */\r\n\t\t\t\ttemplates: {\r\n\t\t\t\t\t"textShadow": ["Color X Y Blur", "black 0px 0px 0px"],\r\n\t\t\t\t\t"boxShadow": ["Color X Y Blur Spread", "black 0px 0px 0px 0px"],\r\n\t\t\t\t\t"clip": ["Top Right Bottom Left", "0px 0px 0px 0px"],\r\n\t\t\t\t\t"backgroundPosition": ["X Y", "0% 0%"],\r\n\t\t\t\t\t"transformOrigin": ["X Y Z", "50% 50% 0px"],\r\n\t\t\t\t\t"perspectiveOrigin": ["X Y", "50% 50%"]\r\n\t\t\t\t},\r\n\t\t\t\t/* A "registered" hook is one that has been converted from its template form into a live,\r\n\t\t\t\t tweenable property. It contains data to associate it with its root property. */\r\n\t\t\t\tregistered: {\r\n\t\t\t\t\t/* Note: A registered hook looks like this ==> textShadowBlur: [ "textShadow", 3 ],\r\n\t\t\t\t\t which consists of the subproperty\'s name, the associated root property\'s name,\r\n\t\t\t\t\t and the subproperty\'s position in the root\'s value. */\r\n\t\t\t\t},\r\n\t\t\t\t/* Convert the templates into individual hooks then append them to the registered object above. */\r\n\t\t\t\tregister: function() {\r\n\t\t\t\t\t/* Color hooks registration: Colors are defaulted to white -- as opposed to black -- since colors that are\r\n\t\t\t\t\t currently set to "transparent" default to their respective template below when color-animated,\r\n\t\t\t\t\t and white is typically a closer match to transparent than black is. An exception is made for text ("color"),\r\n\t\t\t\t\t which is almost always set closer to black than white. */\r\n\t\t\t\t\tfor (var i = 0; i < CSS.Lists.colors.length; i++) {\r\n\t\t\t\t\t\tvar rgbComponents = (CSS.Lists.colors[i] === "color") ? "0 0 0 1" : "255 255 255 1";\r\n\t\t\t\t\t\tCSS.Hooks.templates[CSS.Lists.colors[i]] = ["Red Green Blue Alpha", rgbComponents];\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tvar rootProperty,\r\n\t\t\t\t\t\t\thookTemplate,\r\n\t\t\t\t\t\t\thookNames;\r\n\r\n\t\t\t\t\t/* In IE, color values inside compound-value properties are positioned at the end the value instead of at the beginning.\r\n\t\t\t\t\t Thus, we re-arrange the templates accordingly. */\r\n\t\t\t\t\tif (IE) {\r\n\t\t\t\t\t\tfor (rootProperty in CSS.Hooks.templates) {\r\n\t\t\t\t\t\t\tif (!CSS.Hooks.templates.hasOwnProperty(rootProperty)) {\r\n\t\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\thookTemplate = CSS.Hooks.templates[rootProperty];\r\n\t\t\t\t\t\t\thookNames = hookTemplate[0].split(" ");\r\n\r\n\t\t\t\t\t\t\tvar defaultValues = hookTemplate[1].match(CSS.RegEx.valueSplit);\r\n\r\n\t\t\t\t\t\t\tif (hookNames[0] === "Color") {\r\n\t\t\t\t\t\t\t\t/* Reposition both the hook\'s name and its default value to the end of their respective strings. */\r\n\t\t\t\t\t\t\t\thookNames.push(hookNames.shift());\r\n\t\t\t\t\t\t\t\tdefaultValues.push(defaultValues.shift());\r\n\r\n\t\t\t\t\t\t\t\t/* Replace the existing template for the hook\'s root property. */\r\n\t\t\t\t\t\t\t\tCSS.Hooks.templates[rootProperty] = [hookNames.join(" "), defaultValues.join(" ")];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* Hook registration. */\r\n\t\t\t\t\tfor (rootProperty in CSS.Hooks.templates) {\r\n\t\t\t\t\t\tif (!CSS.Hooks.templates.hasOwnProperty(rootProperty)) {\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\thookTemplate = CSS.Hooks.templates[rootProperty];\r\n\t\t\t\t\t\thookNames = hookTemplate[0].split(" ");\r\n\r\n\t\t\t\t\t\tfor (var j in hookNames) {\r\n\t\t\t\t\t\t\tif (!hookNames.hasOwnProperty(j)) {\r\n\t\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tvar fullHookName = rootProperty + hookNames[j],\r\n\t\t\t\t\t\t\t\t\thookPosition = j;\r\n\r\n\t\t\t\t\t\t\t/* For each hook, register its full name (e.g. textShadowBlur) with its root property (e.g. textShadow)\r\n\t\t\t\t\t\t\t and the hook\'s position in its template\'s default value string. */\r\n\t\t\t\t\t\t\tCSS.Hooks.registered[fullHookName] = [rootProperty, hookPosition];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t/*****************************\r\n\t\t\t\t Injection and Extraction\r\n\t\t\t\t *****************************/\r\n\r\n\t\t\t\t/* Look up the root property associated with the hook (e.g. return "textShadow" for "textShadowBlur"). */\r\n\t\t\t\t/* Since a hook cannot be set directly (the browser won\'t recognize it), style updating for hooks is routed through the hook\'s root property. */\r\n\t\t\t\tgetRoot: function(property) {\r\n\t\t\t\t\tvar hookData = CSS.Hooks.registered[property];\r\n\r\n\t\t\t\t\tif (hookData) {\r\n\t\t\t\t\t\treturn hookData[0];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t/* If there was no hook match, return the property name untouched. */\r\n\t\t\t\t\t\treturn property;\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tgetUnit: function(str, start) {\r\n\t\t\t\t\tvar unit = (str.substr(start || 0, 5).match(/^[a-z%]+/) || [])[0] || "";\r\n\r\n\t\t\t\t\tif (unit && _inArray(CSS.Lists.units, unit)) {\r\n\t\t\t\t\t\treturn unit;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn "";\r\n\t\t\t\t},\r\n\t\t\t\tfixColors: function(str) {\r\n\t\t\t\t\treturn str.replace(/(rgba?\\(\\s*)?(\\b[a-z]+\\b)/g, function($0, $1, $2) {\r\n\t\t\t\t\t\tif (CSS.Lists.colorNames.hasOwnProperty($2)) {\r\n\t\t\t\t\t\t\treturn ($1 ? $1 : "rgba(") + CSS.Lists.colorNames[$2] + ($1 ? "" : ",1)");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn $1 + $2;\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\t/* Convert any rootPropertyValue, null or otherwise, into a space-delimited list of hook values so that\r\n\t\t\t\t the targeted hook can be injected or extracted at its standard position. */\r\n\t\t\t\tcleanRootPropertyValue: function(rootProperty, rootPropertyValue) {\r\n\t\t\t\t\t/* If the rootPropertyValue is wrapped with "rgb()", "clip()", etc., remove the wrapping to normalize the value before manipulation. */\r\n\t\t\t\t\tif (CSS.RegEx.valueUnwrap.test(rootPropertyValue)) {\r\n\t\t\t\t\t\trootPropertyValue = rootPropertyValue.match(CSS.RegEx.valueUnwrap)[1];\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* If rootPropertyValue is a CSS null-value (from which there\'s inherently no hook value to extract),\r\n\t\t\t\t\t default to the root\'s default value as defined in CSS.Hooks.templates. */\r\n\t\t\t\t\t/* Note: CSS null-values include "none", "auto", and "transparent". They must be converted into their\r\n\t\t\t\t\t zero-values (e.g. textShadow: "none" ==> textShadow: "0px 0px 0px black") for hook manipulation to proceed. */\r\n\t\t\t\t\tif (CSS.Values.isCSSNullValue(rootPropertyValue)) {\r\n\t\t\t\t\t\trootPropertyValue = CSS.Hooks.templates[rootProperty][1];\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\treturn rootPropertyValue;\r\n\t\t\t\t},\r\n\t\t\t\t/* Extracted the hook\'s value from its root property\'s value. This is used to get the starting value of an animating hook. */\r\n\t\t\t\textractValue: function(fullHookName, rootPropertyValue) {\r\n\t\t\t\t\tvar hookData = CSS.Hooks.registered[fullHookName];\r\n\r\n\t\t\t\t\tif (hookData) {\r\n\t\t\t\t\t\tvar hookRoot = hookData[0],\r\n\t\t\t\t\t\t\t\thookPosition = hookData[1];\r\n\r\n\t\t\t\t\t\trootPropertyValue = CSS.Hooks.cleanRootPropertyValue(hookRoot, rootPropertyValue);\r\n\r\n\t\t\t\t\t\t/* Split rootPropertyValue into its constituent hook values then grab the desired hook at its standard position. */\r\n\t\t\t\t\t\treturn rootPropertyValue.toString().match(CSS.RegEx.valueSplit)[hookPosition];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t/* If the provided fullHookName isn\'t a registered hook, return the rootPropertyValue that was passed in. */\r\n\t\t\t\t\t\treturn rootPropertyValue;\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t/* Inject the hook\'s value into its root property\'s value. This is used to piece back together the root property\r\n\t\t\t\t once Velocity has updated one of its individually hooked values through tweening. */\r\n\t\t\t\tinjectValue: function(fullHookName, hookValue, rootPropertyValue) {\r\n\t\t\t\t\tvar hookData = CSS.Hooks.registered[fullHookName];\r\n\r\n\t\t\t\t\tif (hookData) {\r\n\t\t\t\t\t\tvar hookRoot = hookData[0],\r\n\t\t\t\t\t\t\t\thookPosition = hookData[1],\r\n\t\t\t\t\t\t\t\trootPropertyValueParts,\r\n\t\t\t\t\t\t\t\trootPropertyValueUpdated;\r\n\r\n\t\t\t\t\t\trootPropertyValue = CSS.Hooks.cleanRootPropertyValue(hookRoot, rootPropertyValue);\r\n\r\n\t\t\t\t\t\t/* Split rootPropertyValue into its individual hook values, replace the targeted value with hookValue,\r\n\t\t\t\t\t\t then reconstruct the rootPropertyValue string. */\r\n\t\t\t\t\t\trootPropertyValueParts = rootPropertyValue.toString().match(CSS.RegEx.valueSplit);\r\n\t\t\t\t\t\trootPropertyValueParts[hookPosition] = hookValue;\r\n\t\t\t\t\t\trootPropertyValueUpdated = rootPropertyValueParts.join(" ");\r\n\r\n\t\t\t\t\t\treturn rootPropertyValueUpdated;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t/* If the provided fullHookName isn\'t a registered hook, return the rootPropertyValue that was passed in. */\r\n\t\t\t\t\t\treturn rootPropertyValue;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/*******************\r\n\t\t\t Normalizations\r\n\t\t\t *******************/\r\n\r\n\t\t\t/* Normalizations standardize CSS property manipulation by pollyfilling browser-specific implementations (e.g. opacity)\r\n\t\t\t and reformatting special properties (e.g. clip, rgba) to look like standard ones. */\r\n\t\t\tNormalizations: {\r\n\t\t\t\t/* Normalizations are passed a normalization target (either the property\'s name, its extracted value, or its injected value),\r\n\t\t\t\t the targeted element (which may need to be queried), and the targeted property value. */\r\n\t\t\t\tregistered: {\r\n\t\t\t\t\tclip: function(type, element, propertyValue) {\r\n\t\t\t\t\t\tswitch (type) {\r\n\t\t\t\t\t\t\tcase "name":\r\n\t\t\t\t\t\t\t\treturn "clip";\r\n\t\t\t\t\t\t\t\t/* Clip needs to be unwrapped and stripped of its commas during extraction. */\r\n\t\t\t\t\t\t\tcase "extract":\r\n\t\t\t\t\t\t\t\tvar extracted;\r\n\r\n\t\t\t\t\t\t\t\t/* If Velocity also extracted this value, skip extraction. */\r\n\t\t\t\t\t\t\t\tif (CSS.RegEx.wrappedValueAlreadyExtracted.test(propertyValue)) {\r\n\t\t\t\t\t\t\t\t\textracted = propertyValue;\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t/* Remove the "rect()" wrapper. */\r\n\t\t\t\t\t\t\t\t\textracted = propertyValue.toString().match(CSS.RegEx.valueUnwrap);\r\n\r\n\t\t\t\t\t\t\t\t\t/* Strip off commas. */\r\n\t\t\t\t\t\t\t\t\textracted = extracted ? extracted[1].replace(/,(\\s+)?/g, " ") : propertyValue;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\treturn extracted;\r\n\t\t\t\t\t\t\t\t/* Clip needs to be re-wrapped during injection. */\r\n\t\t\t\t\t\t\tcase "inject":\r\n\t\t\t\t\t\t\t\treturn "rect(" + propertyValue + ")";\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tblur: function(type, element, propertyValue) {\r\n\t\t\t\t\t\tswitch (type) {\r\n\t\t\t\t\t\t\tcase "name":\r\n\t\t\t\t\t\t\t\treturn Velocity.State.isFirefox ? "filter" : "-webkit-filter";\r\n\t\t\t\t\t\t\tcase "extract":\r\n\t\t\t\t\t\t\t\tvar extracted = parseFloat(propertyValue);\r\n\r\n\t\t\t\t\t\t\t\t/* If extracted is NaN, meaning the value isn\'t already extracted. */\r\n\t\t\t\t\t\t\t\tif (!(extracted || extracted === 0)) {\r\n\t\t\t\t\t\t\t\t\tvar blurComponent = propertyValue.toString().match(/blur\\(([0-9]+[A-z]+)\\)/i);\r\n\r\n\t\t\t\t\t\t\t\t\t/* If the filter string had a blur component, return just the blur value and unit type. */\r\n\t\t\t\t\t\t\t\t\tif (blurComponent) {\r\n\t\t\t\t\t\t\t\t\t\textracted = blurComponent[1];\r\n\t\t\t\t\t\t\t\t\t\t/* If the component doesn\'t exist, default blur to 0. */\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\textracted = 0;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\treturn extracted;\r\n\t\t\t\t\t\t\t\t/* Blur needs to be re-wrapped during injection. */\r\n\t\t\t\t\t\t\tcase "inject":\r\n\t\t\t\t\t\t\t\t/* For the blur effect to be fully de-applied, it needs to be set to "none" instead of 0. */\r\n\t\t\t\t\t\t\t\tif (!parseFloat(propertyValue)) {\r\n\t\t\t\t\t\t\t\t\treturn "none";\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\treturn "blur(" + propertyValue + ")";\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t/* <=IE8 do not support the standard opacity property. They use filter:alpha(opacity=INT) instead. */\r\n\t\t\t\t\topacity: function(type, element, propertyValue) {\r\n\t\t\t\t\t\tif (IE <= 8) {\r\n\t\t\t\t\t\t\tswitch (type) {\r\n\t\t\t\t\t\t\t\tcase "name":\r\n\t\t\t\t\t\t\t\t\treturn "filter";\r\n\t\t\t\t\t\t\t\tcase "extract":\r\n\t\t\t\t\t\t\t\t\t/* <=IE8 return a "filter" value of "alpha(opacity=\\d{1,3})".\r\n\t\t\t\t\t\t\t\t\t Extract the value and convert it to a decimal value to match the standard CSS opacity property\'s formatting. */\r\n\t\t\t\t\t\t\t\t\tvar extracted = propertyValue.toString().match(/alpha\\(opacity=(.*)\\)/i);\r\n\r\n\t\t\t\t\t\t\t\t\tif (extracted) {\r\n\t\t\t\t\t\t\t\t\t\t/* Convert to decimal value. */\r\n\t\t\t\t\t\t\t\t\t\tpropertyValue = extracted[1] / 100;\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t/* When extracting opacity, default to 1 since a null value means opacity hasn\'t been set. */\r\n\t\t\t\t\t\t\t\t\t\tpropertyValue = 1;\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\treturn propertyValue;\r\n\t\t\t\t\t\t\t\tcase "inject":\r\n\t\t\t\t\t\t\t\t\t/* Opacified elements are required to have their zoom property set to a non-zero value. */\r\n\t\t\t\t\t\t\t\t\telement.style.zoom = 1;\r\n\r\n\t\t\t\t\t\t\t\t\t/* Setting the filter property on elements with certain font property combinations can result in a\r\n\t\t\t\t\t\t\t\t\t highly unappealing ultra-bolding effect. There\'s no way to remedy this throughout a tween, but dropping the\r\n\t\t\t\t\t\t\t\t\t value altogether (when opacity hits 1) at leasts ensures that the glitch is gone post-tweening. */\r\n\t\t\t\t\t\t\t\t\tif (parseFloat(propertyValue) >= 1) {\r\n\t\t\t\t\t\t\t\t\t\treturn "";\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t/* As per the filter property\'s spec, convert the decimal value to a whole number and wrap the value. */\r\n\t\t\t\t\t\t\t\t\t\treturn "alpha(opacity=" + parseInt(parseFloat(propertyValue) * 100, 10) + ")";\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t/* With all other browsers, normalization is not required; return the same values that were passed in. */\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tswitch (type) {\r\n\t\t\t\t\t\t\t\tcase "name":\r\n\t\t\t\t\t\t\t\t\treturn "opacity";\r\n\t\t\t\t\t\t\t\tcase "extract":\r\n\t\t\t\t\t\t\t\t\treturn propertyValue;\r\n\t\t\t\t\t\t\t\tcase "inject":\r\n\t\t\t\t\t\t\t\t\treturn propertyValue;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t/*****************************\r\n\t\t\t\t Batched Registrations\r\n\t\t\t\t *****************************/\r\n\r\n\t\t\t\t/* Note: Batched normalizations extend the CSS.Normalizations.registered object. */\r\n\t\t\t\tregister: function() {\r\n\r\n\t\t\t\t\t/*****************\r\n\t\t\t\t\t Transforms\r\n\t\t\t\t\t *****************/\r\n\r\n\t\t\t\t\t/* Transforms are the subproperties contained by the CSS "transform" property. Transforms must undergo normalization\r\n\t\t\t\t\t so that they can be referenced in a properties map by their individual names. */\r\n\t\t\t\t\t/* Note: When transforms are "set", they are actually assigned to a per-element transformCache. When all transform\r\n\t\t\t\t\t setting is complete complete, CSS.flushTransformCache() must be manually called to flush the values to the DOM.\r\n\t\t\t\t\t Transform setting is batched in this way to improve performance: the transform style only needs to be updated\r\n\t\t\t\t\t once when multiple transform subproperties are being animated simultaneously. */\r\n\t\t\t\t\t/* Note: IE9 and Android Gingerbread have support for 2D -- but not 3D -- transforms. Since animating unsupported\r\n\t\t\t\t\t transform properties results in the browser ignoring the *entire* transform string, we prevent these 3D values\r\n\t\t\t\t\t from being normalized for these browsers so that tweening skips these properties altogether\r\n\t\t\t\t\t (since it will ignore them as being unsupported by the browser.) */\r\n\t\t\t\t\tif ((!IE || IE > 9) && !Velocity.State.isGingerbread) {\r\n\t\t\t\t\t\t/* Note: Since the standalone CSS "perspective" property and the CSS transform "perspective" subproperty\r\n\t\t\t\t\t\t share the same name, the latter is given a unique token within Velocity: "transformPerspective". */\r\n\t\t\t\t\t\tCSS.Lists.transformsBase = CSS.Lists.transformsBase.concat(CSS.Lists.transforms3D);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tfor (var i = 0; i < CSS.Lists.transformsBase.length; i++) {\r\n\t\t\t\t\t\t/* Wrap the dynamically generated normalization function in a new scope so that transformName\'s value is\r\n\t\t\t\t\t\t paired with its respective function. (Otherwise, all functions would take the final for loop\'s transformName.) */\r\n\t\t\t\t\t\t(function() {\r\n\t\t\t\t\t\t\tvar transformName = CSS.Lists.transformsBase[i];\r\n\r\n\t\t\t\t\t\t\tCSS.Normalizations.registered[transformName] = function(type, element, propertyValue) {\r\n\t\t\t\t\t\t\t\tswitch (type) {\r\n\t\t\t\t\t\t\t\t\t/* The normalized property name is the parent "transform" property -- the property that is actually set in CSS. */\r\n\t\t\t\t\t\t\t\t\tcase "name":\r\n\t\t\t\t\t\t\t\t\t\treturn "transform";\r\n\t\t\t\t\t\t\t\t\t\t/* Transform values are cached onto a per-element transformCache object. */\r\n\t\t\t\t\t\t\t\t\tcase "extract":\r\n\t\t\t\t\t\t\t\t\t\t/* If this transform has yet to be assigned a value, return its null value. */\r\n\t\t\t\t\t\t\t\t\t\tif (Data(element) === undefined || Data(element).transformCache[transformName] === undefined) {\r\n\t\t\t\t\t\t\t\t\t\t\t/* Scale CSS.Lists.transformsBase default to 1 whereas all other transform properties default to 0. */\r\n\t\t\t\t\t\t\t\t\t\t\treturn /^scale/i.test(transformName) ? 1 : 0;\r\n\t\t\t\t\t\t\t\t\t\t\t/* When transform values are set, they are wrapped in parentheses as per the CSS spec.\r\n\t\t\t\t\t\t\t\t\t\t\t Thus, when extracting their values (for tween calculations), we strip off the parentheses. */\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\treturn Data(element).transformCache[transformName].replace(/[()]/g, "");\r\n\t\t\t\t\t\t\t\t\tcase "inject":\r\n\t\t\t\t\t\t\t\t\t\tvar invalid = false;\r\n\r\n\t\t\t\t\t\t\t\t\t\t/* If an individual transform property contains an unsupported unit type, the browser ignores the *entire* transform property.\r\n\t\t\t\t\t\t\t\t\t\t Thus, protect users from themselves by skipping setting for transform values supplied with invalid unit types. */\r\n\t\t\t\t\t\t\t\t\t\t/* Switch on the base transform type; ignore the axis by removing the last letter from the transform\'s name. */\r\n\t\t\t\t\t\t\t\t\t\tswitch (transformName.substr(0, transformName.length - 1)) {\r\n\t\t\t\t\t\t\t\t\t\t\t/* Whitelist unit types for each transform. */\r\n\t\t\t\t\t\t\t\t\t\t\tcase "translate":\r\n\t\t\t\t\t\t\t\t\t\t\t\tinvalid = !/(%|px|em|rem|vw|vh|\\d)$/i.test(propertyValue);\r\n\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\t\t/* Since an axis-free "scale" property is supported as well, a little hack is used here to detect it by chopping off its last letter. */\r\n\t\t\t\t\t\t\t\t\t\t\tcase "scal":\r\n\t\t\t\t\t\t\t\t\t\t\tcase "scale":\r\n\t\t\t\t\t\t\t\t\t\t\t\t/* Chrome on Android has a bug in which scaled elements blur if their initial scale\r\n\t\t\t\t\t\t\t\t\t\t\t\t value is below 1 (which can happen with forcefeeding). Thus, we detect a yet-unset scale property\r\n\t\t\t\t\t\t\t\t\t\t\t\t and ensure that its first value is always 1. More info: http://stackoverflow.com/questions/10417890/css3-animations-with-transform-causes-blurred-elements-on-webkit/10417962#10417962 */\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (Velocity.State.isAndroid && Data(element).transformCache[transformName] === undefined && propertyValue < 1) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpropertyValue = 1;\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\tinvalid = !/(\\d)$/i.test(propertyValue);\r\n\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\tcase "skew":\r\n\t\t\t\t\t\t\t\t\t\t\t\tinvalid = !/(deg|\\d)$/i.test(propertyValue);\r\n\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\tcase "rotate":\r\n\t\t\t\t\t\t\t\t\t\t\t\tinvalid = !/(deg|\\d)$/i.test(propertyValue);\r\n\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\tif (!invalid) {\r\n\t\t\t\t\t\t\t\t\t\t\t/* As per the CSS spec, wrap the value in parentheses. */\r\n\t\t\t\t\t\t\t\t\t\t\tData(element).transformCache[transformName] = "(" + propertyValue + ")";\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t/* Although the value is set on the transformCache object, return the newly-updated value for the calling code to process as normal. */\r\n\t\t\t\t\t\t\t\t\t\treturn Data(element).transformCache[transformName];\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t})();\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/*************\r\n\t\t\t\t\t Colors\r\n\t\t\t\t\t *************/\r\n\r\n\t\t\t\t\t/* Since Velocity only animates a single numeric value per property, color animation is achieved by hooking the individual RGBA components of CSS color properties.\r\n\t\t\t\t\t Accordingly, color values must be normalized (e.g. "#ff0000", "red", and "rgb(255, 0, 0)" ==> "255 0 0 1") so that their components can be injected/extracted by CSS.Hooks logic. */\r\n\t\t\t\t\tfor (var j = 0; j < CSS.Lists.colors.length; j++) {\r\n\t\t\t\t\t\t/* Wrap the dynamically generated normalization function in a new scope so that colorName\'s value is paired with its respective function.\r\n\t\t\t\t\t\t (Otherwise, all functions would take the final for loop\'s colorName.) */\r\n\t\t\t\t\t\t(function() {\r\n\t\t\t\t\t\t\tvar colorName = CSS.Lists.colors[j];\r\n\r\n\t\t\t\t\t\t\t/* Note: In IE<=8, which support rgb but not rgba, color properties are reverted to rgb by stripping off the alpha component. */\r\n\t\t\t\t\t\t\tCSS.Normalizations.registered[colorName] = function(type, element, propertyValue) {\r\n\t\t\t\t\t\t\t\tswitch (type) {\r\n\t\t\t\t\t\t\t\t\tcase "name":\r\n\t\t\t\t\t\t\t\t\t\treturn colorName;\r\n\t\t\t\t\t\t\t\t\t\t/* Convert all color values into the rgb format. (Old IE can return hex values and color names instead of rgb/rgba.) */\r\n\t\t\t\t\t\t\t\t\tcase "extract":\r\n\t\t\t\t\t\t\t\t\t\tvar extracted;\r\n\r\n\t\t\t\t\t\t\t\t\t\t/* If the color is already in its hookable form (e.g. "255 255 255 1") due to having been previously extracted, skip extraction. */\r\n\t\t\t\t\t\t\t\t\t\tif (CSS.RegEx.wrappedValueAlreadyExtracted.test(propertyValue)) {\r\n\t\t\t\t\t\t\t\t\t\t\textracted = propertyValue;\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tvar converted,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolorNames = {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tblack: "rgb(0, 0, 0)",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tblue: "rgb(0, 0, 255)",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgray: "rgb(128, 128, 128)",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgreen: "rgb(0, 128, 0)",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tred: "rgb(255, 0, 0)",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twhite: "rgb(255, 255, 255)"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t/* Convert color names to rgb. */\r\n\t\t\t\t\t\t\t\t\t\t\tif (/^[A-z]+$/i.test(propertyValue)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (colorNames[propertyValue] !== undefined) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tconverted = colorNames[propertyValue];\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/* If an unmatched color name is provided, default to black. */\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tconverted = colorNames.black;\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/* Convert hex values to rgb. */\r\n\t\t\t\t\t\t\t\t\t\t\t} else if (CSS.RegEx.isHex.test(propertyValue)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconverted = "rgb(" + CSS.Values.hexToRgb(propertyValue).join(" ") + ")";\r\n\t\t\t\t\t\t\t\t\t\t\t\t/* If the provided color doesn\'t match any of the accepted color formats, default to black. */\r\n\t\t\t\t\t\t\t\t\t\t\t} else if (!(/^rgba?\\(/i.test(propertyValue))) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconverted = colorNames.black;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t/* Remove the surrounding "rgb/rgba()" string then replace commas with spaces and strip\r\n\t\t\t\t\t\t\t\t\t\t\t repeated spaces (in case the value included spaces to begin with). */\r\n\t\t\t\t\t\t\t\t\t\t\textracted = (converted || propertyValue).toString().match(CSS.RegEx.valueUnwrap)[1].replace(/,(\\s+)?/g, " ");\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t/* So long as this isn\'t <=IE8, add a fourth (alpha) component if it\'s missing and default it to 1 (visible). */\r\n\t\t\t\t\t\t\t\t\t\tif ((!IE || IE > 8) && extracted.split(" ").length === 3) {\r\n\t\t\t\t\t\t\t\t\t\t\textracted += " 1";\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\treturn extracted;\r\n\t\t\t\t\t\t\t\t\tcase "inject":\r\n\t\t\t\t\t\t\t\t\t\t/* If we have a pattern then it might already have the right values */\r\n\t\t\t\t\t\t\t\t\t\tif (/^rgb/.test(propertyValue)) {\r\n\t\t\t\t\t\t\t\t\t\t\treturn propertyValue;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t/* If this is IE<=8 and an alpha component exists, strip it off. */\r\n\t\t\t\t\t\t\t\t\t\tif (IE <= 8) {\r\n\t\t\t\t\t\t\t\t\t\t\tif (propertyValue.split(" ").length === 4) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tpropertyValue = propertyValue.split(/\\s+/).slice(0, 3).join(" ");\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t/* Otherwise, add a fourth (alpha) component if it\'s missing and default it to 1 (visible). */\r\n\t\t\t\t\t\t\t\t\t\t} else if (propertyValue.split(" ").length === 3) {\r\n\t\t\t\t\t\t\t\t\t\t\tpropertyValue += " 1";\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t/* Re-insert the browser-appropriate wrapper("rgb/rgba()"), insert commas, and strip off decimal units\r\n\t\t\t\t\t\t\t\t\t\t on all values but the fourth (R, G, and B only accept whole numbers). */\r\n\t\t\t\t\t\t\t\t\t\treturn (IE <= 8 ? "rgb" : "rgba") + "(" + propertyValue.replace(/\\s+/g, ",").replace(/\\.(\\d)+(?=,)/g, "") + ")";\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t})();\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/**************\r\n\t\t\t\t\t Dimensions\r\n\t\t\t\t\t **************/\r\n\t\t\t\t\tfunction augmentDimension(name, element, wantInner) {\r\n\t\t\t\t\t\tvar isBorderBox = CSS.getPropertyValue(element, "boxSizing").toString().toLowerCase() === "border-box";\r\n\r\n\t\t\t\t\t\tif (isBorderBox === (wantInner || false)) {\r\n\t\t\t\t\t\t\t/* in box-sizing mode, the CSS width / height accessors already give the outerWidth / outerHeight. */\r\n\t\t\t\t\t\t\tvar i,\r\n\t\t\t\t\t\t\t\t\tvalue,\r\n\t\t\t\t\t\t\t\t\taugment = 0,\r\n\t\t\t\t\t\t\t\t\tsides = name === "width" ? ["Left", "Right"] : ["Top", "Bottom"],\r\n\t\t\t\t\t\t\t\t\tfields = ["padding" + sides[0], "padding" + sides[1], "border" + sides[0] + "Width", "border" + sides[1] + "Width"];\r\n\r\n\t\t\t\t\t\t\tfor (i = 0; i < fields.length; i++) {\r\n\t\t\t\t\t\t\t\tvalue = parseFloat(CSS.getPropertyValue(element, fields[i]));\r\n\t\t\t\t\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\t\t\t\t\taugment += value;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn wantInner ? -augment : augment;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tfunction getDimension(name, wantInner) {\r\n\t\t\t\t\t\treturn function(type, element, propertyValue) {\r\n\t\t\t\t\t\t\tswitch (type) {\r\n\t\t\t\t\t\t\t\tcase "name":\r\n\t\t\t\t\t\t\t\t\treturn name;\r\n\t\t\t\t\t\t\t\tcase "extract":\r\n\t\t\t\t\t\t\t\t\treturn parseFloat(propertyValue) + augmentDimension(name, element, wantInner);\r\n\t\t\t\t\t\t\t\tcase "inject":\r\n\t\t\t\t\t\t\t\t\treturn (parseFloat(propertyValue) - augmentDimension(name, element, wantInner)) + "px";\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tCSS.Normalizations.registered.innerWidth = getDimension("width", true);\r\n\t\t\t\t\tCSS.Normalizations.registered.innerHeight = getDimension("height", true);\r\n\t\t\t\t\tCSS.Normalizations.registered.outerWidth = getDimension("width");\r\n\t\t\t\t\tCSS.Normalizations.registered.outerHeight = getDimension("height");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/************************\r\n\t\t\t CSS Property Names\r\n\t\t\t ************************/\r\n\r\n\t\t\tNames: {\r\n\t\t\t\t/* Camelcase a property name into its JavaScript notation (e.g. "background-color" ==> "backgroundColor").\r\n\t\t\t\t Camelcasing is used to normalize property names between and across calls. */\r\n\t\t\t\tcamelCase: function(property) {\r\n\t\t\t\t\treturn property.replace(/-(\\w)/g, function(match, subMatch) {\r\n\t\t\t\t\t\treturn subMatch.toUpperCase();\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\t/* For SVG elements, some properties (namely, dimensional ones) are GET/SET via the element\'s HTML attributes (instead of via CSS styles). */\r\n\t\t\t\tSVGAttribute: function(property) {\r\n\t\t\t\t\tvar SVGAttributes = "width|height|x|y|cx|cy|r|rx|ry|x1|x2|y1|y2";\r\n\r\n\t\t\t\t\t/* Certain browsers require an SVG transform to be applied as an attribute. (Otherwise, application via CSS is preferable due to 3D support.) */\r\n\t\t\t\t\tif (IE || (Velocity.State.isAndroid && !Velocity.State.isChrome)) {\r\n\t\t\t\t\t\tSVGAttributes += "|transform";\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\treturn new RegExp("^(" + SVGAttributes + ")$", "i").test(property);\r\n\t\t\t\t},\r\n\t\t\t\t/* Determine whether a property should be set with a vendor prefix. */\r\n\t\t\t\t/* If a prefixed version of the property exists, return it. Otherwise, return the original property name.\r\n\t\t\t\t If the property is not at all supported by the browser, return a false flag. */\r\n\t\t\t\tprefixCheck: function(property) {\r\n\t\t\t\t\t/* If this property has already been checked, return the cached value. */\r\n\t\t\t\t\tif (Velocity.State.prefixMatches[property]) {\r\n\t\t\t\t\t\treturn [Velocity.State.prefixMatches[property], true];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar vendors = ["", "Webkit", "Moz", "ms", "O"];\r\n\r\n\t\t\t\t\t\tfor (var i = 0, vendorsLength = vendors.length; i < vendorsLength; i++) {\r\n\t\t\t\t\t\t\tvar propertyPrefixed;\r\n\r\n\t\t\t\t\t\t\tif (i === 0) {\r\n\t\t\t\t\t\t\t\tpropertyPrefixed = property;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t/* Capitalize the first letter of the property to conform to JavaScript vendor prefix notation (e.g. webkitFilter). */\r\n\t\t\t\t\t\t\t\tpropertyPrefixed = vendors[i] + property.replace(/^\\w/, function(match) {\r\n\t\t\t\t\t\t\t\t\treturn match.toUpperCase();\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* Check if the browser supports this property as prefixed. */\r\n\t\t\t\t\t\t\tif (Type.isString(Velocity.State.prefixElement.style[propertyPrefixed])) {\r\n\t\t\t\t\t\t\t\t/* Cache the match. */\r\n\t\t\t\t\t\t\t\tVelocity.State.prefixMatches[property] = propertyPrefixed;\r\n\r\n\t\t\t\t\t\t\t\treturn [propertyPrefixed, true];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* If the browser doesn\'t support this property in any form, include a false flag so that the caller can decide how to proceed. */\r\n\t\t\t\t\t\treturn [property, false];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/************************\r\n\t\t\t CSS Property Values\r\n\t\t\t ************************/\r\n\r\n\t\t\tValues: {\r\n\t\t\t\t/* Hex to RGB conversion. Copyright Tim Down: http://stackoverflow.com/questions/5623838/rgb-to-hex-and-hex-to-rgb */\r\n\t\t\t\thexToRgb: function(hex) {\r\n\t\t\t\t\tvar shortformRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i,\r\n\t\t\t\t\t\t\tlongformRegex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i,\r\n\t\t\t\t\t\t\trgbParts;\r\n\r\n\t\t\t\t\thex = hex.replace(shortformRegex, function(m, r, g, b) {\r\n\t\t\t\t\t\treturn r + r + g + g + b + b;\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\trgbParts = longformRegex.exec(hex);\r\n\r\n\t\t\t\t\treturn rgbParts ? [parseInt(rgbParts[1], 16), parseInt(rgbParts[2], 16), parseInt(rgbParts[3], 16)] : [0, 0, 0];\r\n\t\t\t\t},\r\n\t\t\t\tisCSSNullValue: function(value) {\r\n\t\t\t\t\t/* The browser defaults CSS values that have not been set to either 0 or one of several possible null-value strings.\r\n\t\t\t\t\t Thus, we check for both falsiness and these special strings. */\r\n\t\t\t\t\t/* Null-value checking is performed to default the special strings to 0 (for the sake of tweening) or their hook\r\n\t\t\t\t\t templates as defined as CSS.Hooks (for the sake of hook injection/extraction). */\r\n\t\t\t\t\t/* Note: Chrome returns "rgba(0, 0, 0, 0)" for an undefined color whereas IE returns "transparent". */\r\n\t\t\t\t\treturn (!value || /^(none|auto|transparent|(rgba\\(0, ?0, ?0, ?0\\)))$/i.test(value));\r\n\t\t\t\t},\r\n\t\t\t\t/* Retrieve a property\'s default unit type. Used for assigning a unit type when one is not supplied by the user. */\r\n\t\t\t\tgetUnitType: function(property) {\r\n\t\t\t\t\tif (/^(rotate|skew)/i.test(property)) {\r\n\t\t\t\t\t\treturn "deg";\r\n\t\t\t\t\t} else if (/(^(scale|scaleX|scaleY|scaleZ|alpha|flexGrow|flexHeight|zIndex|fontWeight)$)|((opacity|red|green|blue|alpha)$)/i.test(property)) {\r\n\t\t\t\t\t\t/* The above properties are unitless. */\r\n\t\t\t\t\t\treturn "";\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t/* Default to px for all other properties. */\r\n\t\t\t\t\t\treturn "px";\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t/* HTML elements default to an associated display type when they\'re not set to display:none. */\r\n\t\t\t\t/* Note: This function is used for correctly setting the non-"none" display value in certain Velocity redirects, such as fadeIn/Out. */\r\n\t\t\t\tgetDisplayType: function(element) {\r\n\t\t\t\t\tvar tagName = element && element.tagName.toString().toLowerCase();\r\n\r\n\t\t\t\t\tif (/^(b|big|i|small|tt|abbr|acronym|cite|code|dfn|em|kbd|strong|samp|var|a|bdo|br|img|map|object|q|script|span|sub|sup|button|input|label|select|textarea)$/i.test(tagName)) {\r\n\t\t\t\t\t\treturn "inline";\r\n\t\t\t\t\t} else if (/^(li)$/i.test(tagName)) {\r\n\t\t\t\t\t\treturn "list-item";\r\n\t\t\t\t\t} else if (/^(tr)$/i.test(tagName)) {\r\n\t\t\t\t\t\treturn "table-row";\r\n\t\t\t\t\t} else if (/^(table)$/i.test(tagName)) {\r\n\t\t\t\t\t\treturn "table";\r\n\t\t\t\t\t} else if (/^(tbody)$/i.test(tagName)) {\r\n\t\t\t\t\t\treturn "table-row-group";\r\n\t\t\t\t\t\t/* Default to "block" when no match is found. */\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn "block";\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t/* The class add/remove functions are used to temporarily apply a "velocity-animating" class to elements while they\'re animating. */\r\n\t\t\t\taddClass: function(element, className) {\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tif (element.classList) {\r\n\t\t\t\t\t\t\telement.classList.add(className);\r\n\t\t\t\t\t\t} else if (Type.isString(element.className)) {\r\n\t\t\t\t\t\t\t// Element.className is around 15% faster then set/getAttribute\r\n\t\t\t\t\t\t\telement.className += (element.className.length ? " " : "") + className;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// Work around for IE strict mode animating SVG - and anything else that doesn\'t behave correctly - the same way jQuery does it\r\n\t\t\t\t\t\t\tvar currentClass = element.getAttribute(IE <= 7 ? "className" : "class") || "";\r\n\r\n\t\t\t\t\t\t\telement.setAttribute("class", currentClass + (currentClass ? " " : "") + className);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tremoveClass: function(element, className) {\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tif (element.classList) {\r\n\t\t\t\t\t\t\telement.classList.remove(className);\r\n\t\t\t\t\t\t} else if (Type.isString(element.className)) {\r\n\t\t\t\t\t\t\t// Element.className is around 15% faster then set/getAttribute\r\n\t\t\t\t\t\t\t// TODO: Need some jsperf tests on performance - can we get rid of the regex and maybe use split / array manipulation?\r\n\t\t\t\t\t\t\telement.className = element.className.toString().replace(new RegExp("(^|\\\\s)" + className.split(" ").join("|") + "(\\\\s|$)", "gi"), " ");\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// Work around for IE strict mode animating SVG - and anything else that doesn\'t behave correctly - the same way jQuery does it\r\n\t\t\t\t\t\t\tvar currentClass = element.getAttribute(IE <= 7 ? "className" : "class") || "";\r\n\r\n\t\t\t\t\t\t\telement.setAttribute("class", currentClass.replace(new RegExp("(^|\\s)" + className.split(" ").join("|") + "(\\s|$)", "gi"), " "));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/****************************\r\n\t\t\t Style Getting & Setting\r\n\t\t\t ****************************/\r\n\r\n\t\t\t/* The singular getPropertyValue, which routes the logic for all normalizations, hooks, and standard CSS properties. */\r\n\t\t\tgetPropertyValue: function(element, property, rootPropertyValue, forceStyleLookup) {\r\n\t\t\t\t/* Get an element\'s computed property value. */\r\n\t\t\t\t/* Note: Retrieving the value of a CSS property cannot simply be performed by checking an element\'s\r\n\t\t\t\t style attribute (which only reflects user-defined values). Instead, the browser must be queried for a property\'s\r\n\t\t\t\t *computed* value. You can read more about getComputedStyle here: https://developer.mozilla.org/en/docs/Web/API/window.getComputedStyle */\r\n\t\t\t\tfunction computePropertyValue(element, property) {\r\n\t\t\t\t\t/* When box-sizing isn\'t set to border-box, height and width style values are incorrectly computed when an\r\n\t\t\t\t\t element\'s scrollbars are visible (which expands the element\'s dimensions). Thus, we defer to the more accurate\r\n\t\t\t\t\t offsetHeight/Width property, which includes the total dimensions for interior, border, padding, and scrollbar.\r\n\t\t\t\t\t We subtract border and padding to get the sum of interior + scrollbar. */\r\n\t\t\t\t\tvar computedValue = 0;\r\n\r\n\t\t\t\t\t/* IE<=8 doesn\'t support window.getComputedStyle, thus we defer to jQuery, which has an extensive array\r\n\t\t\t\t\t of hacks to accurately retrieve IE8 property values. Re-implementing that logic here is not worth bloating the\r\n\t\t\t\t\t codebase for a dying browser. The performance repercussions of using jQuery here are minimal since\r\n\t\t\t\t\t Velocity is optimized to rarely (and sometimes never) query the DOM. Further, the $.css() codepath isn\'t that slow. */\r\n\t\t\t\t\tif (IE <= 8) {\r\n\t\t\t\t\t\tcomputedValue = $.css(element, property); /* GET */\r\n\t\t\t\t\t\t/* All other browsers support getComputedStyle. The returned live object reference is cached onto its\r\n\t\t\t\t\t\t associated element so that it does not need to be refetched upon every GET. */\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t/* Browsers do not return height and width values for elements that are set to display:"none". Thus, we temporarily\r\n\t\t\t\t\t\t toggle display to the element type\'s default value. */\r\n\t\t\t\t\t\tvar toggleDisplay = false;\r\n\r\n\t\t\t\t\t\tif (/^(width|height)$/.test(property) && CSS.getPropertyValue(element, "display") === 0) {\r\n\t\t\t\t\t\t\ttoggleDisplay = true;\r\n\t\t\t\t\t\t\tCSS.setPropertyValue(element, "display", CSS.Values.getDisplayType(element));\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tvar revertDisplay = function() {\r\n\t\t\t\t\t\t\tif (toggleDisplay) {\r\n\t\t\t\t\t\t\t\tCSS.setPropertyValue(element, "display", "none");\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\tif (!forceStyleLookup) {\r\n\t\t\t\t\t\t\tif (property === "height" && CSS.getPropertyValue(element, "boxSizing").toString().toLowerCase() !== "border-box") {\r\n\t\t\t\t\t\t\t\tvar contentBoxHeight = element.offsetHeight - (parseFloat(CSS.getPropertyValue(element, "borderTopWidth")) || 0) - (parseFloat(CSS.getPropertyValue(element, "borderBottomWidth")) || 0) - (parseFloat(CSS.getPropertyValue(element, "paddingTop")) || 0) - (parseFloat(CSS.getPropertyValue(element, "paddingBottom")) || 0);\r\n\t\t\t\t\t\t\t\trevertDisplay();\r\n\r\n\t\t\t\t\t\t\t\treturn contentBoxHeight;\r\n\t\t\t\t\t\t\t} else if (property === "width" && CSS.getPropertyValue(element, "boxSizing").toString().toLowerCase() !== "border-box") {\r\n\t\t\t\t\t\t\t\tvar contentBoxWidth = element.offsetWidth - (parseFloat(CSS.getPropertyValue(element, "borderLeftWidth")) || 0) - (parseFloat(CSS.getPropertyValue(element, "borderRightWidth")) || 0) - (parseFloat(CSS.getPropertyValue(element, "paddingLeft")) || 0) - (parseFloat(CSS.getPropertyValue(element, "paddingRight")) || 0);\r\n\t\t\t\t\t\t\t\trevertDisplay();\r\n\r\n\t\t\t\t\t\t\t\treturn contentBoxWidth;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tvar computedStyle;\r\n\r\n\t\t\t\t\t\t/* For elements that Velocity hasn\'t been called on directly (e.g. when Velocity queries the DOM on behalf\r\n\t\t\t\t\t\t of a parent of an element its animating), perform a direct getComputedStyle lookup since the object isn\'t cached. */\r\n\t\t\t\t\t\tif (Data(element) === undefined) {\r\n\t\t\t\t\t\t\tcomputedStyle = window.getComputedStyle(element, null); /* GET */\r\n\t\t\t\t\t\t\t/* If the computedStyle object has yet to be cached, do so now. */\r\n\t\t\t\t\t\t} else if (!Data(element).computedStyle) {\r\n\t\t\t\t\t\t\tcomputedStyle = Data(element).computedStyle = window.getComputedStyle(element, null); /* GET */\r\n\t\t\t\t\t\t\t/* If computedStyle is cached, use it. */\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tcomputedStyle = Data(element).computedStyle;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* IE and Firefox do not return a value for the generic borderColor -- they only return individual values for each border side\'s color.\r\n\t\t\t\t\t\t Also, in all browsers, when border colors aren\'t all the same, a compound value is returned that Velocity isn\'t setup to parse.\r\n\t\t\t\t\t\t So, as a polyfill for querying individual border side colors, we just return the top border\'s color and animate all borders from that value. */\r\n\t\t\t\t\t\tif (property === "borderColor") {\r\n\t\t\t\t\t\t\tproperty = "borderTopColor";\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* IE9 has a bug in which the "filter" property must be accessed from computedStyle using the getPropertyValue method\r\n\t\t\t\t\t\t instead of a direct property lookup. The getPropertyValue method is slower than a direct lookup, which is why we avoid it by default. */\r\n\t\t\t\t\t\tif (IE === 9 && property === "filter") {\r\n\t\t\t\t\t\t\tcomputedValue = computedStyle.getPropertyValue(property); /* GET */\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tcomputedValue = computedStyle[property];\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Fall back to the property\'s style value (if defined) when computedValue returns nothing,\r\n\t\t\t\t\t\t which can happen when the element hasn\'t been painted. */\r\n\t\t\t\t\t\tif (computedValue === "" || computedValue === null) {\r\n\t\t\t\t\t\t\tcomputedValue = element.style[property];\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\trevertDisplay();\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* For top, right, bottom, and left (TRBL) values that are set to "auto" on elements of "fixed" or "absolute" position,\r\n\t\t\t\t\t defer to jQuery for converting "auto" to a numeric value. (For elements with a "static" or "relative" position, "auto" has the same\r\n\t\t\t\t\t effect as being set to 0, so no conversion is necessary.) */\r\n\t\t\t\t\t/* An example of why numeric conversion is necessary: When an element with "position:absolute" has an untouched "left"\r\n\t\t\t\t\t property, which reverts to "auto", left\'s value is 0 relative to its parent element, but is often non-zero relative\r\n\t\t\t\t\t to its *containing* (not parent) element, which is the nearest "position:relative" ancestor or the viewport (and always the viewport in the case of "position:fixed"). */\r\n\t\t\t\t\tif (computedValue === "auto" && /^(top|right|bottom|left)$/i.test(property)) {\r\n\t\t\t\t\t\tvar position = computePropertyValue(element, "position"); /* GET */\r\n\r\n\t\t\t\t\t\t/* For absolute positioning, jQuery\'s $.position() only returns values for top and left;\r\n\t\t\t\t\t\t right and bottom will have their "auto" value reverted to 0. */\r\n\t\t\t\t\t\t/* Note: A jQuery object must be created here since jQuery doesn\'t have a low-level alias for $.position().\r\n\t\t\t\t\t\t Not a big deal since we\'re currently in a GET batch anyway. */\r\n\t\t\t\t\t\tif (position === "fixed" || (position === "absolute" && /top|left/i.test(property))) {\r\n\t\t\t\t\t\t\t/* Note: jQuery strips the pixel unit from its returned values; we re-add it here to conform with computePropertyValue\'s behavior. */\r\n\t\t\t\t\t\t\tcomputedValue = $(element).position()[property] + "px"; /* GET */\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\treturn computedValue;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar propertyValue;\r\n\r\n\t\t\t\t/* If this is a hooked property (e.g. "clipLeft" instead of the root property of "clip"),\r\n\t\t\t\t extract the hook\'s value from a normalized rootPropertyValue using CSS.Hooks.extractValue(). */\r\n\t\t\t\tif (CSS.Hooks.registered[property]) {\r\n\t\t\t\t\tvar hook = property,\r\n\t\t\t\t\t\t\thookRoot = CSS.Hooks.getRoot(hook);\r\n\r\n\t\t\t\t\t/* If a cached rootPropertyValue wasn\'t passed in (which Velocity always attempts to do in order to avoid requerying the DOM),\r\n\t\t\t\t\t query the DOM for the root property\'s value. */\r\n\t\t\t\t\tif (rootPropertyValue === undefined) {\r\n\t\t\t\t\t\t/* Since the browser is now being directly queried, use the official post-prefixing property name for this lookup. */\r\n\t\t\t\t\t\trootPropertyValue = CSS.getPropertyValue(element, CSS.Names.prefixCheck(hookRoot)[0]); /* GET */\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* If this root has a normalization registered, peform the associated normalization extraction. */\r\n\t\t\t\t\tif (CSS.Normalizations.registered[hookRoot]) {\r\n\t\t\t\t\t\trootPropertyValue = CSS.Normalizations.registered[hookRoot]("extract", element, rootPropertyValue);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* Extract the hook\'s value. */\r\n\t\t\t\t\tpropertyValue = CSS.Hooks.extractValue(hook, rootPropertyValue);\r\n\r\n\t\t\t\t\t/* If this is a normalized property (e.g. "opacity" becomes "filter" in <=IE8) or "translateX" becomes "transform"),\r\n\t\t\t\t\t normalize the property\'s name and value, and handle the special case of transforms. */\r\n\t\t\t\t\t/* Note: Normalizing a property is mutually exclusive from hooking a property since hook-extracted values are strictly\r\n\t\t\t\t\t numerical and therefore do not require normalization extraction. */\r\n\t\t\t\t} else if (CSS.Normalizations.registered[property]) {\r\n\t\t\t\t\tvar normalizedPropertyName,\r\n\t\t\t\t\t\t\tnormalizedPropertyValue;\r\n\r\n\t\t\t\t\tnormalizedPropertyName = CSS.Normalizations.registered[property]("name", element);\r\n\r\n\t\t\t\t\t/* Transform values are calculated via normalization extraction (see below), which checks against the element\'s transformCache.\r\n\t\t\t\t\t At no point do transform GETs ever actually query the DOM; initial stylesheet values are never processed.\r\n\t\t\t\t\t This is because parsing 3D transform matrices is not always accurate and would bloat our codebase;\r\n\t\t\t\t\t thus, normalization extraction defaults initial transform values to their zero-values (e.g. 1 for scaleX and 0 for translateX). */\r\n\t\t\t\t\tif (normalizedPropertyName !== "transform") {\r\n\t\t\t\t\t\tnormalizedPropertyValue = computePropertyValue(element, CSS.Names.prefixCheck(normalizedPropertyName)[0]); /* GET */\r\n\r\n\t\t\t\t\t\t/* If the value is a CSS null-value and this property has a hook template, use that zero-value template so that hooks can be extracted from it. */\r\n\t\t\t\t\t\tif (CSS.Values.isCSSNullValue(normalizedPropertyValue) && CSS.Hooks.templates[property]) {\r\n\t\t\t\t\t\t\tnormalizedPropertyValue = CSS.Hooks.templates[property][1];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tpropertyValue = CSS.Normalizations.registered[property]("extract", element, normalizedPropertyValue);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* If a (numeric) value wasn\'t produced via hook extraction or normalization, query the DOM. */\r\n\t\t\t\tif (!/^[\\d-]/.test(propertyValue)) {\r\n\t\t\t\t\t/* For SVG elements, dimensional properties (which SVGAttribute() detects) are tweened via\r\n\t\t\t\t\t their HTML attribute values instead of their CSS style values. */\r\n\t\t\t\t\tvar data = Data(element);\r\n\r\n\t\t\t\t\tif (data && data.isSVG && CSS.Names.SVGAttribute(property)) {\r\n\t\t\t\t\t\t/* Since the height/width attribute values must be set manually, they don\'t reflect computed values.\r\n\t\t\t\t\t\t Thus, we use use getBBox() to ensure we always get values for elements with undefined height/width attributes. */\r\n\t\t\t\t\t\tif (/^(height|width)$/i.test(property)) {\r\n\t\t\t\t\t\t\t/* Firefox throws an error if .getBBox() is called on an SVG that isn\'t attached to the DOM. */\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\tpropertyValue = element.getBBox()[property];\r\n\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\tpropertyValue = 0;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t/* Otherwise, access the attribute value directly. */\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tpropertyValue = element.getAttribute(property);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tpropertyValue = computePropertyValue(element, CSS.Names.prefixCheck(property)[0]); /* GET */\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* Since property lookups are for animation purposes (which entails computing the numeric delta between start and end values),\r\n\t\t\t\t convert CSS null-values to an integer of value 0. */\r\n\t\t\t\tif (CSS.Values.isCSSNullValue(propertyValue)) {\r\n\t\t\t\t\tpropertyValue = 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (Velocity.debug >= 2) {\r\n\t\t\t\t\tconsole.log("Get " + property + ": " + propertyValue);\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn propertyValue;\r\n\t\t\t},\r\n\t\t\t/* The singular setPropertyValue, which routes the logic for all normalizations, hooks, and standard CSS properties. */\r\n\t\t\tsetPropertyValue: function(element, property, propertyValue, rootPropertyValue, scrollData) {\r\n\t\t\t\tvar propertyName = property;\r\n\r\n\t\t\t\t/* In order to be subjected to call options and element queueing, scroll animation is routed through Velocity as if it were a standard CSS property. */\r\n\t\t\t\tif (property === "scroll") {\r\n\t\t\t\t\t/* If a container option is present, scroll the container instead of the browser window. */\r\n\t\t\t\t\tif (scrollData.container) {\r\n\t\t\t\t\t\tscrollData.container["scroll" + scrollData.direction] = propertyValue;\r\n\t\t\t\t\t\t/* Otherwise, Velocity defaults to scrolling the browser window. */\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (scrollData.direction === "Left") {\r\n\t\t\t\t\t\t\twindow.scrollTo(propertyValue, scrollData.alternateValue);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\twindow.scrollTo(scrollData.alternateValue, propertyValue);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t/* Transforms (translateX, rotateZ, etc.) are applied to a per-element transformCache object, which is manually flushed via flushTransformCache().\r\n\t\t\t\t\t Thus, for now, we merely cache transforms being SET. */\r\n\t\t\t\t\tif (CSS.Normalizations.registered[property] && CSS.Normalizations.registered[property]("name", element) === "transform") {\r\n\t\t\t\t\t\t/* Perform a normalization injection. */\r\n\t\t\t\t\t\t/* Note: The normalization logic handles the transformCache updating. */\r\n\t\t\t\t\t\tCSS.Normalizations.registered[property]("inject", element, propertyValue);\r\n\r\n\t\t\t\t\t\tpropertyName = "transform";\r\n\t\t\t\t\t\tpropertyValue = Data(element).transformCache[property];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t/* Inject hooks. */\r\n\t\t\t\t\t\tif (CSS.Hooks.registered[property]) {\r\n\t\t\t\t\t\t\tvar hookName = property,\r\n\t\t\t\t\t\t\t\t\thookRoot = CSS.Hooks.getRoot(property);\r\n\r\n\t\t\t\t\t\t\t/* If a cached rootPropertyValue was not provided, query the DOM for the hookRoot\'s current value. */\r\n\t\t\t\t\t\t\trootPropertyValue = rootPropertyValue || CSS.getPropertyValue(element, hookRoot); /* GET */\r\n\r\n\t\t\t\t\t\t\tpropertyValue = CSS.Hooks.injectValue(hookName, propertyValue, rootPropertyValue);\r\n\t\t\t\t\t\t\tproperty = hookRoot;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Normalize names and values. */\r\n\t\t\t\t\t\tif (CSS.Normalizations.registered[property]) {\r\n\t\t\t\t\t\t\tpropertyValue = CSS.Normalizations.registered[property]("inject", element, propertyValue);\r\n\t\t\t\t\t\t\tproperty = CSS.Normalizations.registered[property]("name", element);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Assign the appropriate vendor prefix before performing an official style update. */\r\n\t\t\t\t\t\tpropertyName = CSS.Names.prefixCheck(property)[0];\r\n\r\n\t\t\t\t\t\t/* A try/catch is used for IE<=8, which throws an error when "invalid" CSS values are set, e.g. a negative width.\r\n\t\t\t\t\t\t Try/catch is avoided for other browsers since it incurs a performance overhead. */\r\n\t\t\t\t\t\tif (IE <= 8) {\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\telement.style[propertyName] = propertyValue;\r\n\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\tif (Velocity.debug) {\r\n\t\t\t\t\t\t\t\t\tconsole.log("Browser does not support [" + propertyValue + "] for [" + propertyName + "]");\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t/* SVG elements have their dimensional properties (width, height, x, y, cx, etc.) applied directly as attributes instead of as styles. */\r\n\t\t\t\t\t\t\t/* Note: IE8 does not support SVG elements, so it\'s okay that we skip it for SVG animation. */\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tvar data = Data(element);\r\n\r\n\t\t\t\t\t\t\tif (data && data.isSVG && CSS.Names.SVGAttribute(property)) {\r\n\t\t\t\t\t\t\t\t/* Note: For SVG attributes, vendor-prefixed property names are never used. */\r\n\t\t\t\t\t\t\t\t/* Note: Not all CSS properties can be animated via attributes, but the browser won\'t throw an error for unsupported properties. */\r\n\t\t\t\t\t\t\t\telement.setAttribute(property, propertyValue);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\telement.style[propertyName] = propertyValue;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (Velocity.debug >= 2) {\r\n\t\t\t\t\t\t\tconsole.log("Set " + property + " (" + propertyName + "): " + propertyValue);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* Return the normalized property name and value in case the caller wants to know how these values were modified before being applied to the DOM. */\r\n\t\t\t\treturn [propertyName, propertyValue];\r\n\t\t\t},\r\n\t\t\t/* To increase performance by batching transform updates into a single SET, transforms are not directly applied to an element until flushTransformCache() is called. */\r\n\t\t\t/* Note: Velocity applies transform properties in the same order that they are chronogically introduced to the element\'s CSS styles. */\r\n\t\t\tflushTransformCache: function(element) {\r\n\t\t\t\tvar transformString = "",\r\n\t\t\t\t\t\tdata = Data(element);\r\n\r\n\t\t\t\t/* Certain browsers require that SVG transforms be applied as an attribute. However, the SVG transform attribute takes a modified version of CSS\'s transform string\r\n\t\t\t\t (units are dropped and, except for skewX/Y, subproperties are merged into their master property -- e.g. scaleX and scaleY are merged into scale(X Y). */\r\n\t\t\t\tif ((IE || (Velocity.State.isAndroid && !Velocity.State.isChrome)) && data && data.isSVG) {\r\n\t\t\t\t\t/* Since transform values are stored in their parentheses-wrapped form, we use a helper function to strip out their numeric values.\r\n\t\t\t\t\t Further, SVG transform properties only take unitless (representing pixels) values, so it\'s okay that parseFloat() strips the unit suffixed to the float value. */\r\n\t\t\t\t\tvar getTransformFloat = function(transformProperty) {\r\n\t\t\t\t\t\treturn parseFloat(CSS.getPropertyValue(element, transformProperty));\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t/* Create an object to organize all the transforms that we\'ll apply to the SVG element. To keep the logic simple,\r\n\t\t\t\t\t we process *all* transform properties -- even those that may not be explicitly applied (since they default to their zero-values anyway). */\r\n\t\t\t\t\tvar SVGTransforms = {\r\n\t\t\t\t\t\ttranslate: [getTransformFloat("translateX"), getTransformFloat("translateY")],\r\n\t\t\t\t\t\tskewX: [getTransformFloat("skewX")], skewY: [getTransformFloat("skewY")],\r\n\t\t\t\t\t\t/* If the scale property is set (non-1), use that value for the scaleX and scaleY values\r\n\t\t\t\t\t\t (this behavior mimics the result of animating all these properties at once on HTML elements). */\r\n\t\t\t\t\t\tscale: getTransformFloat("scale") !== 1 ? [getTransformFloat("scale"), getTransformFloat("scale")] : [getTransformFloat("scaleX"), getTransformFloat("scaleY")],\r\n\t\t\t\t\t\t/* Note: SVG\'s rotate transform takes three values: rotation degrees followed by the X and Y values\r\n\t\t\t\t\t\t defining the rotation\'s origin point. We ignore the origin values (default them to 0). */\r\n\t\t\t\t\t\trotate: [getTransformFloat("rotateZ"), 0, 0]\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t/* Iterate through the transform properties in the user-defined property map order.\r\n\t\t\t\t\t (This mimics the behavior of non-SVG transform animation.) */\r\n\t\t\t\t\t$.each(Data(element).transformCache, function(transformName) {\r\n\t\t\t\t\t\t/* Except for with skewX/Y, revert the axis-specific transform subproperties to their axis-free master\r\n\t\t\t\t\t\t properties so that they match up with SVG\'s accepted transform properties. */\r\n\t\t\t\t\t\tif (/^translate/i.test(transformName)) {\r\n\t\t\t\t\t\t\ttransformName = "translate";\r\n\t\t\t\t\t\t} else if (/^scale/i.test(transformName)) {\r\n\t\t\t\t\t\t\ttransformName = "scale";\r\n\t\t\t\t\t\t} else if (/^rotate/i.test(transformName)) {\r\n\t\t\t\t\t\t\ttransformName = "rotate";\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Check that we haven\'t yet deleted the property from the SVGTransforms container. */\r\n\t\t\t\t\t\tif (SVGTransforms[transformName]) {\r\n\t\t\t\t\t\t\t/* Append the transform property in the SVG-supported transform format. As per the spec, surround the space-delimited values in parentheses. */\r\n\t\t\t\t\t\t\ttransformString += transformName + "(" + SVGTransforms[transformName].join(" ") + ")" + " ";\r\n\r\n\t\t\t\t\t\t\t/* After processing an SVG transform property, delete it from the SVGTransforms container so we don\'t\r\n\t\t\t\t\t\t\t re-insert the same master property if we encounter another one of its axis-specific properties. */\r\n\t\t\t\t\t\t\tdelete SVGTransforms[transformName];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar transformValue,\r\n\t\t\t\t\t\t\tperspective;\r\n\r\n\t\t\t\t\t/* Transform properties are stored as members of the transformCache object. Concatenate all the members into a string. */\r\n\t\t\t\t\t$.each(Data(element).transformCache, function(transformName) {\r\n\t\t\t\t\t\ttransformValue = Data(element).transformCache[transformName];\r\n\r\n\t\t\t\t\t\t/* Transform\'s perspective subproperty must be set first in order to take effect. Store it temporarily. */\r\n\t\t\t\t\t\tif (transformName === "transformPerspective") {\r\n\t\t\t\t\t\t\tperspective = transformValue;\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* IE9 only supports one rotation type, rotateZ, which it refers to as "rotate". */\r\n\t\t\t\t\t\tif (IE === 9 && transformName === "rotateZ") {\r\n\t\t\t\t\t\t\ttransformName = "rotate";\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\ttransformString += transformName + transformValue + " ";\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t/* If present, set the perspective subproperty first. */\r\n\t\t\t\t\tif (perspective) {\r\n\t\t\t\t\t\ttransformString = "perspective" + perspective + " " + transformString;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tCSS.setPropertyValue(element, "transform", transformString);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t/* Register hooks and normalizations. */\r\n\t\tCSS.Hooks.register();\r\n\t\tCSS.Normalizations.register();\r\n\r\n\t\t/* Allow hook setting in the same fashion as jQuery\'s $.css(). */\r\n\t\tVelocity.hook = function(elements, arg2, arg3) {\r\n\t\t\tvar value;\r\n\r\n\t\t\telements = sanitizeElements(elements);\r\n\r\n\t\t\t$.each(elements, function(i, element) {\r\n\t\t\t\t/* Initialize Velocity\'s per-element data cache if this element hasn\'t previously been animated. */\r\n\t\t\t\tif (Data(element) === undefined) {\r\n\t\t\t\t\tVelocity.init(element);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* Get property value. If an element set was passed in, only return the value for the first element. */\r\n\t\t\t\tif (arg3 === undefined) {\r\n\t\t\t\t\tif (value === undefined) {\r\n\t\t\t\t\t\tvalue = CSS.getPropertyValue(element, arg2);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t/* Set property value. */\r\n\t\t\t\t} else {\r\n\t\t\t\t\t/* sPV returns an array of the normalized propertyName/propertyValue pair used to update the DOM. */\r\n\t\t\t\t\tvar adjustedSet = CSS.setPropertyValue(element, arg2, arg3);\r\n\r\n\t\t\t\t\t/* Transform properties don\'t automatically set. They have to be flushed to the DOM. */\r\n\t\t\t\t\tif (adjustedSet[0] === "transform") {\r\n\t\t\t\t\t\tVelocity.CSS.flushTransformCache(element);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tvalue = adjustedSet;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\treturn value;\r\n\t\t};\r\n\r\n\t\t/*****************\r\n\t\t Animation\r\n\t\t *****************/\r\n\r\n\t\tvar animate = function() {\r\n\t\t\tvar opts;\r\n\r\n\t\t\t/******************\r\n\t\t\t Call Chain\r\n\t\t\t ******************/\r\n\r\n\t\t\t/* Logic for determining what to return to the call stack when exiting out of Velocity. */\r\n\t\t\tfunction getChain() {\r\n\t\t\t\t/* If we are using the utility function, attempt to return this call\'s promise. If no promise library was detected,\r\n\t\t\t\t default to null instead of returning the targeted elements so that utility function\'s return value is standardized. */\r\n\t\t\t\tif (isUtility) {\r\n\t\t\t\t\treturn promiseData.promise || null;\r\n\t\t\t\t\t/* Otherwise, if we\'re using $.fn, return the jQuery-/Zepto-wrapped element set. */\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn elementsWrapped;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/*************************\r\n\t\t\t Arguments Assignment\r\n\t\t\t *************************/\r\n\r\n\t\t\t/* To allow for expressive CoffeeScript code, Velocity supports an alternative syntax in which "elements" (or "e"), "properties" (or "p"), and "options" (or "o")\r\n\t\t\t objects are defined on a container object that\'s passed in as Velocity\'s sole argument. */\r\n\t\t\t/* Note: Some browsers automatically populate arguments with a "properties" object. We detect it by checking for its default "names" property. */\r\n\t\t\tvar syntacticSugar = (arguments[0] && (arguments[0].p || (($.isPlainObject(arguments[0].properties) && !arguments[0].properties.names) || Type.isString(arguments[0].properties)))),\r\n\t\t\t\t\t/* Whether Velocity was called via the utility function (as opposed to on a jQuery/Zepto object). */\r\n\t\t\t\t\tisUtility,\r\n\t\t\t\t\t/* When Velocity is called via the utility function ($.Velocity()/Velocity()), elements are explicitly\r\n\t\t\t\t\t passed in as the first parameter. Thus, argument positioning varies. We normalize them here. */\r\n\t\t\t\t\telementsWrapped,\r\n\t\t\t\t\targumentIndex;\r\n\r\n\t\t\tvar elements,\r\n\t\t\t\t\tpropertiesMap,\r\n\t\t\t\t\toptions;\r\n\r\n\t\t\t/* Detect jQuery/Zepto elements being animated via the $.fn method. */\r\n\t\t\tif (Type.isWrapped(this)) {\r\n\t\t\t\tisUtility = false;\r\n\r\n\t\t\t\targumentIndex = 0;\r\n\t\t\t\telements = this;\r\n\t\t\t\telementsWrapped = this;\r\n\t\t\t\t/* Otherwise, raw elements are being animated via the utility function. */\r\n\t\t\t} else {\r\n\t\t\t\tisUtility = true;\r\n\r\n\t\t\t\targumentIndex = 1;\r\n\t\t\t\telements = syntacticSugar ? (arguments[0].elements || arguments[0].e) : arguments[0];\r\n\t\t\t}\r\n\r\n\t\t\t/***************\r\n\t\t\t Promises\r\n\t\t\t ***************/\r\n\r\n\t\t\tvar promiseData = {\r\n\t\t\t\tpromise: null,\r\n\t\t\t\tresolver: null,\r\n\t\t\t\trejecter: null\r\n\t\t\t};\r\n\r\n\t\t\t/* If this call was made via the utility function (which is the default method of invocation when jQuery/Zepto are not being used), and if\r\n\t\t\t promise support was detected, create a promise object for this call and store references to its resolver and rejecter methods. The resolve\r\n\t\t\t method is used when a call completes naturally or is prematurely stopped by the user. In both cases, completeCall() handles the associated\r\n\t\t\t call cleanup and promise resolving logic. The reject method is used when an invalid set of arguments is passed into a Velocity call. */\r\n\t\t\t/* Note: Velocity employs a call-based queueing architecture, which means that stopping an animating element actually stops the full call that\r\n\t\t\t triggered it -- not that one element exclusively. Similarly, there is one promise per call, and all elements targeted by a Velocity call are\r\n\t\t\t grouped together for the purposes of resolving and rejecting a promise. */\r\n\t\t\tif (isUtility && Velocity.Promise) {\r\n\t\t\t\tpromiseData.promise = new Velocity.Promise(function(resolve, reject) {\r\n\t\t\t\t\tpromiseData.resolver = resolve;\r\n\t\t\t\t\tpromiseData.rejecter = reject;\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\tif (syntacticSugar) {\r\n\t\t\t\tpropertiesMap = arguments[0].properties || arguments[0].p;\r\n\t\t\t\toptions = arguments[0].options || arguments[0].o;\r\n\t\t\t} else {\r\n\t\t\t\tpropertiesMap = arguments[argumentIndex];\r\n\t\t\t\toptions = arguments[argumentIndex + 1];\r\n\t\t\t}\r\n\r\n\t\t\telements = sanitizeElements(elements);\r\n\r\n\t\t\tif (!elements) {\r\n\t\t\t\tif (promiseData.promise) {\r\n\t\t\t\t\tif (!propertiesMap || !options || options.promiseRejectEmpty !== false) {\r\n\t\t\t\t\t\tpromiseData.rejecter();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tpromiseData.resolver();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t/* The length of the element set (in the form of a nodeList or an array of elements) is defaulted to 1 in case a\r\n\t\t\t single raw DOM element is passed in (which doesn\'t contain a length property). */\r\n\t\t\tvar elementsLength = elements.length,\r\n\t\t\t\t\telementsIndex = 0;\r\n\r\n\t\t\t/***************************\r\n\t\t\t Argument Overloading\r\n\t\t\t ***************************/\r\n\r\n\t\t\t/* Support is included for jQuery\'s argument overloading: $.animate(propertyMap [, duration] [, easing] [, complete]).\r\n\t\t\t Overloading is detected by checking for the absence of an object being passed into options. */\r\n\t\t\t/* Note: The stop/finish/pause/resume actions do not accept animation options, and are therefore excluded from this check. */\r\n\t\t\tif (!/^(stop|finish|finishAll|pause|resume)$/i.test(propertiesMap) && !$.isPlainObject(options)) {\r\n\t\t\t\t/* The utility function shifts all arguments one position to the right, so we adjust for that offset. */\r\n\t\t\t\tvar startingArgumentPosition = argumentIndex + 1;\r\n\r\n\t\t\t\toptions = {};\r\n\r\n\t\t\t\t/* Iterate through all options arguments */\r\n\t\t\t\tfor (var i = startingArgumentPosition; i < arguments.length; i++) {\r\n\t\t\t\t\t/* Treat a number as a duration. Parse it out. */\r\n\t\t\t\t\t/* Note: The following RegEx will return true if passed an array with a number as its first item.\r\n\t\t\t\t\t Thus, arrays are skipped from this check. */\r\n\t\t\t\t\tif (!Type.isArray(arguments[i]) && (/^(fast|normal|slow)$/i.test(arguments[i]) || /^\\d/.test(arguments[i]))) {\r\n\t\t\t\t\t\toptions.duration = arguments[i];\r\n\t\t\t\t\t\t/* Treat strings and arrays as easings. */\r\n\t\t\t\t\t} else if (Type.isString(arguments[i]) || Type.isArray(arguments[i])) {\r\n\t\t\t\t\t\toptions.easing = arguments[i];\r\n\t\t\t\t\t\t/* Treat a function as a complete callback. */\r\n\t\t\t\t\t} else if (Type.isFunction(arguments[i])) {\r\n\t\t\t\t\t\toptions.complete = arguments[i];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/*********************\r\n\t\t\t Action Detection\r\n\t\t\t *********************/\r\n\r\n\t\t\t/* Velocity\'s behavior is categorized into "actions": Elements can either be specially scrolled into view,\r\n\t\t\t or they can be started, stopped, paused, resumed, or reversed . If a literal or referenced properties map is passed in as Velocity\'s\r\n\t\t\t first argument, the associated action is "start". Alternatively, "scroll", "reverse", "pause", "resume" or "stop" can be passed in \r\n\t\t\t instead of a properties map. */\r\n\t\t\tvar action;\r\n\r\n\t\t\tswitch (propertiesMap) {\r\n\t\t\t\tcase "scroll":\r\n\t\t\t\t\taction = "scroll";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase "reverse":\r\n\t\t\t\t\taction = "reverse";\r\n\t\t\t\t\tbreak;\r\n\r\n\t\t\t\tcase "pause":\r\n\r\n\t\t\t\t\t/*******************\r\n\t\t\t\t\t Action: Pause\r\n\t\t\t\t\t *******************/\r\n\r\n\t\t\t\t\tvar currentTime = (new Date()).getTime();\r\n\r\n\t\t\t\t\t/* Handle delay timers */\r\n\t\t\t\t\t$.each(elements, function(i, element) {\r\n\t\t\t\t\t\tpauseDelayOnElement(element, currentTime);\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t/* Pause and Resume are call-wide (not on a per element basis). Thus, calling pause or resume on a \r\n\t\t\t\t\t single element will cause any calls that containt tweens for that element to be paused/resumed\r\n\t\t\t\t\t as well. */\r\n\r\n\t\t\t\t\t/* Iterate through all calls and pause any that contain any of our elements */\r\n\t\t\t\t\t$.each(Velocity.State.calls, function(i, activeCall) {\r\n\r\n\t\t\t\t\t\tvar found = false;\r\n\t\t\t\t\t\t/* Inactive calls are set to false by the logic inside completeCall(). Skip them. */\r\n\t\t\t\t\t\tif (activeCall) {\r\n\t\t\t\t\t\t\t/* Iterate through the active call\'s targeted elements. */\r\n\t\t\t\t\t\t\t$.each(activeCall[1], function(k, activeElement) {\r\n\t\t\t\t\t\t\t\tvar queueName = (options === undefined) ? "" : options;\r\n\r\n\t\t\t\t\t\t\t\tif (queueName !== true && (activeCall[2].queue !== queueName) && !(options === undefined && activeCall[2].queue === false)) {\r\n\t\t\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t/* Iterate through the calls targeted by the stop command. */\r\n\t\t\t\t\t\t\t\t$.each(elements, function(l, element) {\r\n\t\t\t\t\t\t\t\t\t/* Check that this call was applied to the target element. */\r\n\t\t\t\t\t\t\t\t\tif (element === activeElement) {\r\n\r\n\t\t\t\t\t\t\t\t\t\t/* Set call to paused */\r\n\t\t\t\t\t\t\t\t\t\tactiveCall[5] = {\r\n\t\t\t\t\t\t\t\t\t\t\tresume: false\r\n\t\t\t\t\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\t\t\t\t\t/* Once we match an element, we can bounce out to the next call entirely */\r\n\t\t\t\t\t\t\t\t\t\tfound = true;\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t/* Proceed to check next call if we have already matched */\r\n\t\t\t\t\t\t\t\tif (found) {\r\n\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t/* Since pause creates no new tweens, exit out of Velocity. */\r\n\t\t\t\t\treturn getChain();\r\n\r\n\t\t\t\tcase "resume":\r\n\r\n\t\t\t\t\t/*******************\r\n\t\t\t\t\t Action: Resume\r\n\t\t\t\t\t *******************/\r\n\r\n\t\t\t\t\t/* Handle delay timers */\r\n\t\t\t\t\t$.each(elements, function(i, element) {\r\n\t\t\t\t\t\tresumeDelayOnElement(element, currentTime);\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t/* Pause and Resume are call-wide (not on a per elemnt basis). Thus, calling pause or resume on a \r\n\t\t\t\t\t single element will cause any calls that containt tweens for that element to be paused/resumed\r\n\t\t\t\t\t as well. */\r\n\r\n\t\t\t\t\t/* Iterate through all calls and pause any that contain any of our elements */\r\n\t\t\t\t\t$.each(Velocity.State.calls, function(i, activeCall) {\r\n\t\t\t\t\t\tvar found = false;\r\n\t\t\t\t\t\t/* Inactive calls are set to false by the logic inside completeCall(). Skip them. */\r\n\t\t\t\t\t\tif (activeCall) {\r\n\t\t\t\t\t\t\t/* Iterate through the active call\'s targeted elements. */\r\n\t\t\t\t\t\t\t$.each(activeCall[1], function(k, activeElement) {\r\n\t\t\t\t\t\t\t\tvar queueName = (options === undefined) ? "" : options;\r\n\r\n\t\t\t\t\t\t\t\tif (queueName !== true && (activeCall[2].queue !== queueName) && !(options === undefined && activeCall[2].queue === false)) {\r\n\t\t\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t/* Skip any calls that have never been paused */\r\n\t\t\t\t\t\t\t\tif (!activeCall[5]) {\r\n\t\t\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t/* Iterate through the calls targeted by the stop command. */\r\n\t\t\t\t\t\t\t\t$.each(elements, function(l, element) {\r\n\t\t\t\t\t\t\t\t\t/* Check that this call was applied to the target element. */\r\n\t\t\t\t\t\t\t\t\tif (element === activeElement) {\r\n\r\n\t\t\t\t\t\t\t\t\t\t/* Flag a pause object to be resumed, which will occur during the next tick. In\r\n\t\t\t\t\t\t\t\t\t\t addition, the pause object will at that time be deleted */\r\n\t\t\t\t\t\t\t\t\t\tactiveCall[5].resume = true;\r\n\r\n\t\t\t\t\t\t\t\t\t\t/* Once we match an element, we can bounce out to the next call entirely */\r\n\t\t\t\t\t\t\t\t\t\tfound = true;\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t/* Proceed to check next call if we have already matched */\r\n\t\t\t\t\t\t\t\tif (found) {\r\n\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t/* Since resume creates no new tweens, exit out of Velocity. */\r\n\t\t\t\t\treturn getChain();\r\n\r\n\t\t\t\tcase "finish":\r\n\t\t\t\tcase "finishAll":\r\n\t\t\t\tcase "stop":\r\n\t\t\t\t\t/*******************\r\n\t\t\t\t\t Action: Stop\r\n\t\t\t\t\t *******************/\r\n\r\n\t\t\t\t\t/* Clear the currently-active delay on each targeted element. */\r\n\t\t\t\t\t$.each(elements, function(i, element) {\r\n\t\t\t\t\t\tif (Data(element) && Data(element).delayTimer) {\r\n\t\t\t\t\t\t\t/* Stop the timer from triggering its cached next() function. */\r\n\t\t\t\t\t\t\tclearTimeout(Data(element).delayTimer.setTimeout);\r\n\r\n\t\t\t\t\t\t\t/* Manually call the next() function so that the subsequent queue items can progress. */\r\n\t\t\t\t\t\t\tif (Data(element).delayTimer.next) {\r\n\t\t\t\t\t\t\t\tData(element).delayTimer.next();\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tdelete Data(element).delayTimer;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* If we want to finish everything in the queue, we have to iterate through it\r\n\t\t\t\t\t\t and call each function. This will make them active calls below, which will\r\n\t\t\t\t\t\t cause them to be applied via the duration setting. */\r\n\t\t\t\t\t\tif (propertiesMap === "finishAll" && (options === true || Type.isString(options))) {\r\n\t\t\t\t\t\t\t/* Iterate through the items in the element\'s queue. */\r\n\t\t\t\t\t\t\t$.each($.queue(element, Type.isString(options) ? options : ""), function(_, item) {\r\n\t\t\t\t\t\t\t\t/* The queue array can contain an "inprogress" string, which we skip. */\r\n\t\t\t\t\t\t\t\tif (Type.isFunction(item)) {\r\n\t\t\t\t\t\t\t\t\titem();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t/* Clearing the $.queue() array is achieved by resetting it to []. */\r\n\t\t\t\t\t\t\t$.queue(element, Type.isString(options) ? options : "", []);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tvar callsToStop = [];\r\n\r\n\t\t\t\t\t/* When the stop action is triggered, the elements\' currently active call is immediately stopped. The active call might have\r\n\t\t\t\t\t been applied to multiple elements, in which case all of the call\'s elements will be stopped. When an element\r\n\t\t\t\t\t is stopped, the next item in its animation queue is immediately triggered. */\r\n\t\t\t\t\t/* An additional argument may be passed in to clear an element\'s remaining queued calls. Either true (which defaults to the "fx" queue)\r\n\t\t\t\t\t or a custom queue string can be passed in. */\r\n\t\t\t\t\t/* Note: The stop command runs prior to Velocity\'s Queueing phase since its behavior is intended to take effect *immediately*,\r\n\t\t\t\t\t regardless of the element\'s current queue state. */\r\n\r\n\t\t\t\t\t/* Iterate through every active call. */\r\n\t\t\t\t\t$.each(Velocity.State.calls, function(i, activeCall) {\r\n\t\t\t\t\t\t/* Inactive calls are set to false by the logic inside completeCall(). Skip them. */\r\n\t\t\t\t\t\tif (activeCall) {\r\n\t\t\t\t\t\t\t/* Iterate through the active call\'s targeted elements. */\r\n\t\t\t\t\t\t\t$.each(activeCall[1], function(k, activeElement) {\r\n\t\t\t\t\t\t\t\t/* If true was passed in as a secondary argument, clear absolutely all calls on this element. Otherwise, only\r\n\t\t\t\t\t\t\t\t clear calls associated with the relevant queue. */\r\n\t\t\t\t\t\t\t\t/* Call stopping logic works as follows:\r\n\t\t\t\t\t\t\t\t - options === true --\x3e stop current default queue calls (and queue:false calls), including remaining queued ones.\r\n\t\t\t\t\t\t\t\t - options === undefined --\x3e stop current queue:"" call and all queue:false calls.\r\n\t\t\t\t\t\t\t\t - options === false --\x3e stop only queue:false calls.\r\n\t\t\t\t\t\t\t\t - options === "custom" --\x3e stop current queue:"custom" call, including remaining queued ones (there is no functionality to only clear the currently-running queue:"custom" call). */\r\n\t\t\t\t\t\t\t\tvar queueName = (options === undefined) ? "" : options;\r\n\r\n\t\t\t\t\t\t\t\tif (queueName !== true && (activeCall[2].queue !== queueName) && !(options === undefined && activeCall[2].queue === false)) {\r\n\t\t\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t/* Iterate through the calls targeted by the stop command. */\r\n\t\t\t\t\t\t\t\t$.each(elements, function(l, element) {\r\n\t\t\t\t\t\t\t\t\t/* Check that this call was applied to the target element. */\r\n\t\t\t\t\t\t\t\t\tif (element === activeElement) {\r\n\t\t\t\t\t\t\t\t\t\t/* Optionally clear the remaining queued calls. If we\'re doing "finishAll" this won\'t find anything,\r\n\t\t\t\t\t\t\t\t\t\t due to the queue-clearing above. */\r\n\t\t\t\t\t\t\t\t\t\tif (options === true || Type.isString(options)) {\r\n\t\t\t\t\t\t\t\t\t\t\t/* Iterate through the items in the element\'s queue. */\r\n\t\t\t\t\t\t\t\t\t\t\t$.each($.queue(element, Type.isString(options) ? options : ""), function(_, item) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t/* The queue array can contain an "inprogress" string, which we skip. */\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (Type.isFunction(item)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/* Pass the item\'s callback a flag indicating that we want to abort from the queue call.\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t (Specifically, the queue will resolve the call\'s associated promise then abort.)  */\r\n\t\t\t\t\t\t\t\t\t\t\t\t\titem(null, true);\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t/* Clearing the $.queue() array is achieved by resetting it to []. */\r\n\t\t\t\t\t\t\t\t\t\t\t$.queue(element, Type.isString(options) ? options : "", []);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\tif (propertiesMap === "stop") {\r\n\t\t\t\t\t\t\t\t\t\t\t/* Since "reverse" uses cached start values (the previous call\'s endValues), these values must be\r\n\t\t\t\t\t\t\t\t\t\t\t changed to reflect the final value that the elements were actually tweened to. */\r\n\t\t\t\t\t\t\t\t\t\t\t/* Note: If only queue:false/queue:"custom" animations are currently running on an element, it won\'t have a tweensContainer\r\n\t\t\t\t\t\t\t\t\t\t\t object. Also, queue:false/queue:"custom" animations can\'t be reversed. */\r\n\t\t\t\t\t\t\t\t\t\t\tvar data = Data(element);\r\n\t\t\t\t\t\t\t\t\t\t\tif (data && data.tweensContainer && (queueName === true || queueName === "")) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t$.each(data.tweensContainer, function(m, activeTween) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tactiveTween.endValue = activeTween.currentValue;\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\tcallsToStop.push(i);\r\n\t\t\t\t\t\t\t\t\t\t} else if (propertiesMap === "finish" || propertiesMap === "finishAll") {\r\n\t\t\t\t\t\t\t\t\t\t\t/* To get active tweens to finish immediately, we forcefully shorten their durations to 1ms so that\r\n\t\t\t\t\t\t\t\t\t\t\t they finish upon the next rAf tick then proceed with normal call completion logic. */\r\n\t\t\t\t\t\t\t\t\t\t\tactiveCall[2].duration = 1;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t/* Prematurely call completeCall() on each matched active call. Pass an additional flag for "stop" to indicate\r\n\t\t\t\t\t that the complete callback and display:none setting should be skipped since we\'re completing prematurely. */\r\n\t\t\t\t\tif (propertiesMap === "stop") {\r\n\t\t\t\t\t\t$.each(callsToStop, function(i, j) {\r\n\t\t\t\t\t\t\tcompleteCall(j, true);\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tif (promiseData.promise) {\r\n\t\t\t\t\t\t\t/* Immediately resolve the promise associated with this stop call since stop runs synchronously. */\r\n\t\t\t\t\t\t\tpromiseData.resolver(elements);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* Since we\'re stopping, and not proceeding with queueing, exit out of Velocity. */\r\n\t\t\t\t\treturn getChain();\r\n\r\n\t\t\t\tdefault:\r\n\t\t\t\t\t/* Treat a non-empty plain object as a literal properties map. */\r\n\t\t\t\t\tif ($.isPlainObject(propertiesMap) && !Type.isEmptyObject(propertiesMap)) {\r\n\t\t\t\t\t\taction = "start";\r\n\r\n\t\t\t\t\t\t/****************\r\n\t\t\t\t\t\t Redirects\r\n\t\t\t\t\t\t ****************/\r\n\r\n\t\t\t\t\t\t/* Check if a string matches a registered redirect (see Redirects above). */\r\n\t\t\t\t\t} else if (Type.isString(propertiesMap) && Velocity.Redirects[propertiesMap]) {\r\n\t\t\t\t\t\topts = $.extend({}, options);\r\n\r\n\t\t\t\t\t\tvar durationOriginal = opts.duration,\r\n\t\t\t\t\t\t\t\tdelayOriginal = opts.delay || 0;\r\n\r\n\t\t\t\t\t\t/* If the backwards option was passed in, reverse the element set so that elements animate from the last to the first. */\r\n\t\t\t\t\t\tif (opts.backwards === true) {\r\n\t\t\t\t\t\t\telements = $.extend(true, [], elements).reverse();\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Individually trigger the redirect for each element in the set to prevent users from having to handle iteration logic in their redirect. */\r\n\t\t\t\t\t\t$.each(elements, function(elementIndex, element) {\r\n\t\t\t\t\t\t\t/* If the stagger option was passed in, successively delay each element by the stagger value (in ms). Retain the original delay value. */\r\n\t\t\t\t\t\t\tif (parseFloat(opts.stagger)) {\r\n\t\t\t\t\t\t\t\topts.delay = delayOriginal + (parseFloat(opts.stagger) * elementIndex);\r\n\t\t\t\t\t\t\t} else if (Type.isFunction(opts.stagger)) {\r\n\t\t\t\t\t\t\t\topts.delay = delayOriginal + opts.stagger.call(element, elementIndex, elementsLength);\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* If the drag option was passed in, successively increase/decrease (depending on the presense of opts.backwards)\r\n\t\t\t\t\t\t\t the duration of each element\'s animation, using floors to prevent producing very short durations. */\r\n\t\t\t\t\t\t\tif (opts.drag) {\r\n\t\t\t\t\t\t\t\t/* Default the duration of UI pack effects (callouts and transitions) to 1000ms instead of the usual default duration of 400ms. */\r\n\t\t\t\t\t\t\t\topts.duration = parseFloat(durationOriginal) || (/^(callout|transition)/.test(propertiesMap) ? 1000 : DURATION_DEFAULT);\r\n\r\n\t\t\t\t\t\t\t\t/* For each element, take the greater duration of: A) animation completion percentage relative to the original duration,\r\n\t\t\t\t\t\t\t\t B) 75% of the original duration, or C) a 200ms fallback (in case duration is already set to a low value).\r\n\t\t\t\t\t\t\t\t The end result is a baseline of 75% of the redirect\'s duration that increases/decreases as the end of the element set is approached. */\r\n\t\t\t\t\t\t\t\topts.duration = Math.max(opts.duration * (opts.backwards ? 1 - elementIndex / elementsLength : (elementIndex + 1) / elementsLength), opts.duration * 0.75, 200);\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* Pass in the call\'s opts object so that the redirect can optionally extend it. It defaults to an empty object instead of null to\r\n\t\t\t\t\t\t\t reduce the opts checking logic required inside the redirect. */\r\n\t\t\t\t\t\t\tVelocity.Redirects[propertiesMap].call(element, element, opts || {}, elementIndex, elementsLength, elements, promiseData.promise ? promiseData : undefined);\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t/* Since the animation logic resides within the redirect\'s own code, abort the remainder of this call.\r\n\t\t\t\t\t\t (The performance overhead up to this point is virtually non-existant.) */\r\n\t\t\t\t\t\t/* Note: The jQuery call chain is kept intact by returning the complete element set. */\r\n\t\t\t\t\t\treturn getChain();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar abortError = "Velocity: First argument (" + propertiesMap + ") was not a property map, a known action, or a registered redirect. Aborting.";\r\n\r\n\t\t\t\t\t\tif (promiseData.promise) {\r\n\t\t\t\t\t\t\tpromiseData.rejecter(new Error(abortError));\r\n\t\t\t\t\t\t} else if (window.console) {\r\n\t\t\t\t\t\t\tconsole.log(abortError);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\treturn getChain();\r\n\t\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/**************************\r\n\t\t\t Call-Wide Variables\r\n\t\t\t **************************/\r\n\r\n\t\t\t/* A container for CSS unit conversion ratios (e.g. %, rem, and em ==> px) that is used to cache ratios across all elements\r\n\t\t\t being animated in a single Velocity call. Calculating unit ratios necessitates DOM querying and updating, and is therefore\r\n\t\t\t avoided (via caching) wherever possible. This container is call-wide instead of page-wide to avoid the risk of using stale\r\n\t\t\t conversion metrics across Velocity animations that are not immediately consecutively chained. */\r\n\t\t\tvar callUnitConversionData = {\r\n\t\t\t\tlastParent: null,\r\n\t\t\t\tlastPosition: null,\r\n\t\t\t\tlastFontSize: null,\r\n\t\t\t\tlastPercentToPxWidth: null,\r\n\t\t\t\tlastPercentToPxHeight: null,\r\n\t\t\t\tlastEmToPx: null,\r\n\t\t\t\tremToPx: null,\r\n\t\t\t\tvwToPx: null,\r\n\t\t\t\tvhToPx: null\r\n\t\t\t};\r\n\r\n\t\t\t/* A container for all the ensuing tween data and metadata associated with this call. This container gets pushed to the page-wide\r\n\t\t\t Velocity.State.calls array that is processed during animation ticking. */\r\n\t\t\tvar call = [];\r\n\r\n\t\t\t/************************\r\n\t\t\t Element Processing\r\n\t\t\t ************************/\r\n\r\n\t\t\t/* Element processing consists of three parts -- data processing that cannot go stale and data processing that *can* go stale (i.e. third-party style modifications):\r\n\t\t\t 1) Pre-Queueing: Element-wide variables, including the element\'s data storage, are instantiated. Call options are prepared. If triggered, the Stop action is executed.\r\n\t\t\t 2) Queueing: The logic that runs once this call has reached its point of execution in the element\'s $.queue() stack. Most logic is placed here to avoid risking it becoming stale.\r\n\t\t\t 3) Pushing: Consolidation of the tween data followed by its push onto the global in-progress calls container.\r\n\t\t\t `elementArrayIndex` allows passing index of the element in the original array to value functions.\r\n\t\t\t If `elementsIndex` were used instead the index would be determined by the elements\' per-element queue.\r\n\t\t\t */\r\n\t\t\tfunction processElement(element, elementArrayIndex) {\r\n\r\n\t\t\t\t/*************************\r\n\t\t\t\t Part I: Pre-Queueing\r\n\t\t\t\t *************************/\r\n\r\n\t\t\t\t/***************************\r\n\t\t\t\t Element-Wide Variables\r\n\t\t\t\t ***************************/\r\n\r\n\t\t\t\tvar /* The runtime opts object is the extension of the current call\'s options and Velocity\'s page-wide option defaults. */\r\n\t\t\t\t\t\topts = $.extend({}, Velocity.defaults, options),\r\n\t\t\t\t\t\t/* A container for the processed data associated with each property in the propertyMap.\r\n\t\t\t\t\t\t (Each property in the map produces its own "tween".) */\r\n\t\t\t\t\t\ttweensContainer = {},\r\n\t\t\t\t\t\telementUnitConversionData;\r\n\r\n\t\t\t\t/******************\r\n\t\t\t\t Element Init\r\n\t\t\t\t ******************/\r\n\r\n\t\t\t\tif (Data(element) === undefined) {\r\n\t\t\t\t\tVelocity.init(element);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/******************\r\n\t\t\t\t Option: Delay\r\n\t\t\t\t ******************/\r\n\r\n\t\t\t\t/* Since queue:false doesn\'t respect the item\'s existing queue, we avoid injecting its delay here (it\'s set later on). */\r\n\t\t\t\t/* Note: Velocity rolls its own delay function since jQuery doesn\'t have a utility alias for $.fn.delay()\r\n\t\t\t\t (and thus requires jQuery element creation, which we avoid since its overhead includes DOM querying). */\r\n\t\t\t\tif (parseFloat(opts.delay) && opts.queue !== false) {\r\n\t\t\t\t\t$.queue(element, opts.queue, function(next, clearQueue) {\r\n\t\t\t\t\t\tif (clearQueue === true) {\r\n\t\t\t\t\t\t\t/* Do not continue with animation queueing. */\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* This is a flag used to indicate to the upcoming completeCall() function that this queue entry was initiated by Velocity. See completeCall() for further details. */\r\n\t\t\t\t\t\tVelocity.velocityQueueEntryFlag = true;\r\n\r\n\t\t\t\t\t\t/* The ensuing queue item (which is assigned to the "next" argument that $.queue() automatically passes in) will be triggered after a setTimeout delay.\r\n\t\t\t\t\t\t The setTimeout is stored so that it can be subjected to clearTimeout() if this animation is prematurely stopped via Velocity\'s "stop" command, and\r\n\t\t\t\t\t\t delayBegin/delayTime is used to ensure we can "pause" and "resume" a tween that is still mid-delay. */\r\n\r\n\t\t\t\t\t\t/* Temporarily store delayed elements to facilite access for global pause/resume */\r\n\t\t\t\t\t\tvar callIndex = Velocity.State.delayedElements.count++;\r\n\t\t\t\t\t\tVelocity.State.delayedElements[callIndex] = element;\r\n\r\n\t\t\t\t\t\tvar delayComplete = (function(index) {\r\n\t\t\t\t\t\t\treturn function() {\r\n\t\t\t\t\t\t\t\t/* Clear the temporary element */\r\n\t\t\t\t\t\t\t\tVelocity.State.delayedElements[index] = false;\r\n\r\n\t\t\t\t\t\t\t\t/* Finally, issue the call */\r\n\t\t\t\t\t\t\t\tnext();\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t})(callIndex);\r\n\r\n\r\n\t\t\t\t\t\tData(element).delayBegin = (new Date()).getTime();\r\n\t\t\t\t\t\tData(element).delay = parseFloat(opts.delay);\r\n\t\t\t\t\t\tData(element).delayTimer = {\r\n\t\t\t\t\t\t\tsetTimeout: setTimeout(next, parseFloat(opts.delay)),\r\n\t\t\t\t\t\t\tnext: delayComplete\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/*********************\r\n\t\t\t\t Option: Duration\r\n\t\t\t\t *********************/\r\n\r\n\t\t\t\t/* Support for jQuery\'s named durations. */\r\n\t\t\t\tswitch (opts.duration.toString().toLowerCase()) {\r\n\t\t\t\t\tcase "fast":\r\n\t\t\t\t\t\topts.duration = 200;\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\tcase "normal":\r\n\t\t\t\t\t\topts.duration = DURATION_DEFAULT;\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\tcase "slow":\r\n\t\t\t\t\t\topts.duration = 600;\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t/* Remove the potential "ms" suffix and default to 1 if the user is attempting to set a duration of 0 (in order to produce an immediate style change). */\r\n\t\t\t\t\t\topts.duration = parseFloat(opts.duration) || 1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/************************\r\n\t\t\t\t Global Option: Mock\r\n\t\t\t\t ************************/\r\n\r\n\t\t\t\tif (Velocity.mock !== false) {\r\n\t\t\t\t\t/* In mock mode, all animations are forced to 1ms so that they occur immediately upon the next rAF tick.\r\n\t\t\t\t\t Alternatively, a multiplier can be passed in to time remap all delays and durations. */\r\n\t\t\t\t\tif (Velocity.mock === true) {\r\n\t\t\t\t\t\topts.duration = opts.delay = 1;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\topts.duration *= parseFloat(Velocity.mock) || 1;\r\n\t\t\t\t\t\topts.delay *= parseFloat(Velocity.mock) || 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/*******************\r\n\t\t\t\t Option: Easing\r\n\t\t\t\t *******************/\r\n\r\n\t\t\t\topts.easing = getEasing(opts.easing, opts.duration);\r\n\r\n\t\t\t\t/**********************\r\n\t\t\t\t Option: Callbacks\r\n\t\t\t\t **********************/\r\n\r\n\t\t\t\t/* Callbacks must functions. Otherwise, default to null. */\r\n\t\t\t\tif (opts.begin && !Type.isFunction(opts.begin)) {\r\n\t\t\t\t\topts.begin = null;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (opts.progress && !Type.isFunction(opts.progress)) {\r\n\t\t\t\t\topts.progress = null;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (opts.complete && !Type.isFunction(opts.complete)) {\r\n\t\t\t\t\topts.complete = null;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/*********************************\r\n\t\t\t\t Option: Display & Visibility\r\n\t\t\t\t *********************************/\r\n\r\n\t\t\t\t/* Refer to Velocity\'s documentation (VelocityJS.org/#displayAndVisibility) for a description of the display and visibility options\' behavior. */\r\n\t\t\t\t/* Note: We strictly check for undefined instead of falsiness because display accepts an empty string value. */\r\n\t\t\t\tif (opts.display !== undefined && opts.display !== null) {\r\n\t\t\t\t\topts.display = opts.display.toString().toLowerCase();\r\n\r\n\t\t\t\t\t/* Users can pass in a special "auto" value to instruct Velocity to set the element to its default display value. */\r\n\t\t\t\t\tif (opts.display === "auto") {\r\n\t\t\t\t\t\topts.display = Velocity.CSS.Values.getDisplayType(element);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (opts.visibility !== undefined && opts.visibility !== null) {\r\n\t\t\t\t\topts.visibility = opts.visibility.toString().toLowerCase();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/**********************\r\n\t\t\t\t Option: mobileHA\r\n\t\t\t\t **********************/\r\n\r\n\t\t\t\t/* When set to true, and if this is a mobile device, mobileHA automatically enables hardware acceleration (via a null transform hack)\r\n\t\t\t\t on animating elements. HA is removed from the element at the completion of its animation. */\r\n\t\t\t\t/* Note: Android Gingerbread doesn\'t support HA. If a null transform hack (mobileHA) is in fact set, it will prevent other tranform subproperties from taking effect. */\r\n\t\t\t\t/* Note: You can read more about the use of mobileHA in Velocity\'s documentation: VelocityJS.org/#mobileHA. */\r\n\t\t\t\topts.mobileHA = (opts.mobileHA && Velocity.State.isMobile && !Velocity.State.isGingerbread);\r\n\r\n\t\t\t\t/***********************\r\n\t\t\t\t Part II: Queueing\r\n\t\t\t\t ***********************/\r\n\r\n\t\t\t\t/* When a set of elements is targeted by a Velocity call, the set is broken up and each element has the current Velocity call individually queued onto it.\r\n\t\t\t\t In this way, each element\'s existing queue is respected; some elements may already be animating and accordingly should not have this current Velocity call triggered immediately. */\r\n\t\t\t\t/* In each queue, tween data is processed for each animating property then pushed onto the call-wide calls array. When the last element in the set has had its tweens processed,\r\n\t\t\t\t the call array is pushed to Velocity.State.calls for live processing by the requestAnimationFrame tick. */\r\n\t\t\t\tfunction buildQueue(next) {\r\n\t\t\t\t\tvar data, lastTweensContainer;\r\n\r\n\t\t\t\t\t/*******************\r\n\t\t\t\t\t Option: Begin\r\n\t\t\t\t\t *******************/\r\n\r\n\t\t\t\t\t/* The begin callback is fired once per call -- not once per elemenet -- and is passed the full raw DOM element set as both its context and its first argument. */\r\n\t\t\t\t\tif (opts.begin && elementsIndex === 0) {\r\n\t\t\t\t\t\t/* We throw callbacks in a setTimeout so that thrown errors don\'t halt the execution of Velocity itself. */\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\topts.begin.call(elements, elements);\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t\tthrow error;\r\n\t\t\t\t\t\t\t}, 1);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/*****************************************\r\n\t\t\t\t\t Tween Data Construction (for Scroll)\r\n\t\t\t\t\t *****************************************/\r\n\r\n\t\t\t\t\t/* Note: In order to be subjected to chaining and animation options, scroll\'s tweening is routed through Velocity as if it were a standard CSS property animation. */\r\n\t\t\t\t\tif (action === "scroll") {\r\n\t\t\t\t\t\t/* The scroll action uniquely takes an optional "offset" option -- specified in pixels -- that offsets the targeted scroll position. */\r\n\t\t\t\t\t\tvar scrollDirection = (/^x$/i.test(opts.axis) ? "Left" : "Top"),\r\n\t\t\t\t\t\t\t\tscrollOffset = parseFloat(opts.offset) || 0,\r\n\t\t\t\t\t\t\t\tscrollPositionCurrent,\r\n\t\t\t\t\t\t\t\tscrollPositionCurrentAlternate,\r\n\t\t\t\t\t\t\t\tscrollPositionEnd;\r\n\r\n\t\t\t\t\t\t/* Scroll also uniquely takes an optional "container" option, which indicates the parent element that should be scrolled --\r\n\t\t\t\t\t\t as opposed to the browser window itself. This is useful for scrolling toward an element that\'s inside an overflowing parent element. */\r\n\t\t\t\t\t\tif (opts.container) {\r\n\t\t\t\t\t\t\t/* Ensure that either a jQuery object or a raw DOM element was passed in. */\r\n\t\t\t\t\t\t\tif (Type.isWrapped(opts.container) || Type.isNode(opts.container)) {\r\n\t\t\t\t\t\t\t\t/* Extract the raw DOM element from the jQuery wrapper. */\r\n\t\t\t\t\t\t\t\topts.container = opts.container[0] || opts.container;\r\n\t\t\t\t\t\t\t\t/* Note: Unlike other properties in Velocity, the browser\'s scroll position is never cached since it so frequently changes\r\n\t\t\t\t\t\t\t\t (due to the user\'s natural interaction with the page). */\r\n\t\t\t\t\t\t\t\tscrollPositionCurrent = opts.container["scroll" + scrollDirection]; /* GET */\r\n\r\n\t\t\t\t\t\t\t\t/* $.position() values are relative to the container\'s currently viewable area (without taking into account the container\'s true dimensions\r\n\t\t\t\t\t\t\t\t -- say, for example, if the container was not overflowing). Thus, the scroll end value is the sum of the child element\'s position *and*\r\n\t\t\t\t\t\t\t\t the scroll container\'s current scroll position. */\r\n\t\t\t\t\t\t\t\tscrollPositionEnd = (scrollPositionCurrent + $(element).position()[scrollDirection.toLowerCase()]) + scrollOffset; /* GET */\r\n\t\t\t\t\t\t\t\t/* If a value other than a jQuery object or a raw DOM element was passed in, default to null so that this option is ignored. */\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\topts.container = null;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t/* If the window itself is being scrolled -- not a containing element -- perform a live scroll position lookup using\r\n\t\t\t\t\t\t\t the appropriate cached property names (which differ based on browser type). */\r\n\t\t\t\t\t\t\tscrollPositionCurrent = Velocity.State.scrollAnchor[Velocity.State["scrollProperty" + scrollDirection]]; /* GET */\r\n\t\t\t\t\t\t\t/* When scrolling the browser window, cache the alternate axis\'s current value since window.scrollTo() doesn\'t let us change only one value at a time. */\r\n\t\t\t\t\t\t\tscrollPositionCurrentAlternate = Velocity.State.scrollAnchor[Velocity.State["scrollProperty" + (scrollDirection === "Left" ? "Top" : "Left")]]; /* GET */\r\n\r\n\t\t\t\t\t\t\t/* Unlike $.position(), $.offset() values are relative to the browser window\'s true dimensions -- not merely its currently viewable area --\r\n\t\t\t\t\t\t\t and therefore end values do not need to be compounded onto current values. */\r\n\t\t\t\t\t\t\tscrollPositionEnd = $(element).offset()[scrollDirection.toLowerCase()] + scrollOffset; /* GET */\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Since there\'s only one format that scroll\'s associated tweensContainer can take, we create it manually. */\r\n\t\t\t\t\t\ttweensContainer = {\r\n\t\t\t\t\t\t\tscroll: {\r\n\t\t\t\t\t\t\t\trootPropertyValue: false,\r\n\t\t\t\t\t\t\t\tstartValue: scrollPositionCurrent,\r\n\t\t\t\t\t\t\t\tcurrentValue: scrollPositionCurrent,\r\n\t\t\t\t\t\t\t\tendValue: scrollPositionEnd,\r\n\t\t\t\t\t\t\t\tunitType: "",\r\n\t\t\t\t\t\t\t\teasing: opts.easing,\r\n\t\t\t\t\t\t\t\tscrollData: {\r\n\t\t\t\t\t\t\t\t\tcontainer: opts.container,\r\n\t\t\t\t\t\t\t\t\tdirection: scrollDirection,\r\n\t\t\t\t\t\t\t\t\talternateValue: scrollPositionCurrentAlternate\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\telement: element\r\n\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\tif (Velocity.debug) {\r\n\t\t\t\t\t\t\tconsole.log("tweensContainer (scroll): ", tweensContainer.scroll, element);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/******************************************\r\n\t\t\t\t\t\t Tween Data Construction (for Reverse)\r\n\t\t\t\t\t\t ******************************************/\r\n\r\n\t\t\t\t\t\t/* Reverse acts like a "start" action in that a property map is animated toward. The only difference is\r\n\t\t\t\t\t\t that the property map used for reverse is the inverse of the map used in the previous call. Thus, we manipulate\r\n\t\t\t\t\t\t the previous call to construct our new map: use the previous map\'s end values as our new map\'s start values. Copy over all other data. */\r\n\t\t\t\t\t\t/* Note: Reverse can be directly called via the "reverse" parameter, or it can be indirectly triggered via the loop option. (Loops are composed of multiple reverses.) */\r\n\t\t\t\t\t\t/* Note: Reverse calls do not need to be consecutively chained onto a currently-animating element in order to operate on cached values;\r\n\t\t\t\t\t\t there is no harm to reverse being called on a potentially stale data cache since reverse\'s behavior is simply defined\r\n\t\t\t\t\t\t as reverting to the element\'s values as they were prior to the previous *Velocity* call. */\r\n\t\t\t\t\t} else if (action === "reverse") {\r\n\t\t\t\t\t\tdata = Data(element);\r\n\r\n\t\t\t\t\t\t/* Abort if there is no prior animation data to reverse to. */\r\n\t\t\t\t\t\tif (!data) {\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (!data.tweensContainer) {\r\n\t\t\t\t\t\t\t/* Dequeue the element so that this queue entry releases itself immediately, allowing subsequent queue entries to run. */\r\n\t\t\t\t\t\t\t$.dequeue(element, opts.queue);\r\n\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t/*********************\r\n\t\t\t\t\t\t\t Options Parsing\r\n\t\t\t\t\t\t\t *********************/\r\n\r\n\t\t\t\t\t\t\t/* If the element was hidden via the display option in the previous call,\r\n\t\t\t\t\t\t\t revert display to "auto" prior to reversal so that the element is visible again. */\r\n\t\t\t\t\t\t\tif (data.opts.display === "none") {\r\n\t\t\t\t\t\t\t\tdata.opts.display = "auto";\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tif (data.opts.visibility === "hidden") {\r\n\t\t\t\t\t\t\t\tdata.opts.visibility = "visible";\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* If the loop option was set in the previous call, disable it so that "reverse" calls aren\'t recursively generated.\r\n\t\t\t\t\t\t\t Further, remove the previous call\'s callback options; typically, users do not want these to be refired. */\r\n\t\t\t\t\t\t\tdata.opts.loop = false;\r\n\t\t\t\t\t\t\tdata.opts.begin = null;\r\n\t\t\t\t\t\t\tdata.opts.complete = null;\r\n\r\n\t\t\t\t\t\t\t/* Since we\'re extending an opts object that has already been extended with the defaults options object,\r\n\t\t\t\t\t\t\t we remove non-explicitly-defined properties that are auto-assigned values. */\r\n\t\t\t\t\t\t\tif (!options.easing) {\r\n\t\t\t\t\t\t\t\tdelete opts.easing;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tif (!options.duration) {\r\n\t\t\t\t\t\t\t\tdelete opts.duration;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* The opts object used for reversal is an extension of the options object optionally passed into this\r\n\t\t\t\t\t\t\t reverse call plus the options used in the previous Velocity call. */\r\n\t\t\t\t\t\t\topts = $.extend({}, data.opts, opts);\r\n\r\n\t\t\t\t\t\t\t/*************************************\r\n\t\t\t\t\t\t\t Tweens Container Reconstruction\r\n\t\t\t\t\t\t\t *************************************/\r\n\r\n\t\t\t\t\t\t\t/* Create a deepy copy (indicated via the true flag) of the previous call\'s tweensContainer. */\r\n\t\t\t\t\t\t\tlastTweensContainer = $.extend(true, {}, data ? data.tweensContainer : null);\r\n\r\n\t\t\t\t\t\t\t/* Manipulate the previous tweensContainer by replacing its end values and currentValues with its start values. */\r\n\t\t\t\t\t\t\tfor (var lastTween in lastTweensContainer) {\r\n\t\t\t\t\t\t\t\t/* In addition to tween data, tweensContainers contain an element property that we ignore here. */\r\n\t\t\t\t\t\t\t\tif (lastTweensContainer.hasOwnProperty(lastTween) && lastTween !== "element") {\r\n\t\t\t\t\t\t\t\t\tvar lastStartValue = lastTweensContainer[lastTween].startValue;\r\n\r\n\t\t\t\t\t\t\t\t\tlastTweensContainer[lastTween].startValue = lastTweensContainer[lastTween].currentValue = lastTweensContainer[lastTween].endValue;\r\n\t\t\t\t\t\t\t\t\tlastTweensContainer[lastTween].endValue = lastStartValue;\r\n\r\n\t\t\t\t\t\t\t\t\t/* Easing is the only option that embeds into the individual tween data (since it can be defined on a per-property basis).\r\n\t\t\t\t\t\t\t\t\t Accordingly, every property\'s easing value must be updated when an options object is passed in with a reverse call.\r\n\t\t\t\t\t\t\t\t\t The side effect of this extensibility is that all per-property easing values are forcefully reset to the new value. */\r\n\t\t\t\t\t\t\t\t\tif (!Type.isEmptyObject(options)) {\r\n\t\t\t\t\t\t\t\t\t\tlastTweensContainer[lastTween].easing = opts.easing;\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\tif (Velocity.debug) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log("reverse tweensContainer (" + lastTween + "): " + JSON.stringify(lastTweensContainer[lastTween]), element);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\ttweensContainer = lastTweensContainer;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/*****************************************\r\n\t\t\t\t\t\t Tween Data Construction (for Start)\r\n\t\t\t\t\t\t *****************************************/\r\n\r\n\t\t\t\t\t} else if (action === "start") {\r\n\r\n\t\t\t\t\t\t/*************************\r\n\t\t\t\t\t\t Value Transferring\r\n\t\t\t\t\t\t *************************/\r\n\r\n\t\t\t\t\t\t/* If this queue entry follows a previous Velocity-initiated queue entry *and* if this entry was created\r\n\t\t\t\t\t\t while the element was in the process of being animated by Velocity, then this current call is safe to use\r\n\t\t\t\t\t\t the end values from the prior call as its start values. Velocity attempts to perform this value transfer\r\n\t\t\t\t\t\t process whenever possible in order to avoid requerying the DOM. */\r\n\t\t\t\t\t\t/* If values aren\'t transferred from a prior call and start values were not forcefed by the user (more on this below),\r\n\t\t\t\t\t\t then the DOM is queried for the element\'s current values as a last resort. */\r\n\t\t\t\t\t\t/* Note: Conversely, animation reversal (and looping) *always* perform inter-call value transfers; they never requery the DOM. */\r\n\r\n\t\t\t\t\t\tdata = Data(element);\r\n\r\n\t\t\t\t\t\t/* The per-element isAnimating flag is used to indicate whether it\'s safe (i.e. the data isn\'t stale)\r\n\t\t\t\t\t\t to transfer over end values to use as start values. If it\'s set to true and there is a previous\r\n\t\t\t\t\t\t Velocity call to pull values from, do so. */\r\n\t\t\t\t\t\tif (data && data.tweensContainer && data.isAnimating === true) {\r\n\t\t\t\t\t\t\tlastTweensContainer = data.tweensContainer;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/***************************\r\n\t\t\t\t\t\t Tween Data Calculation\r\n\t\t\t\t\t\t ***************************/\r\n\r\n\t\t\t\t\t\t/* This function parses property data and defaults endValue, easing, and startValue as appropriate. */\r\n\t\t\t\t\t\t/* Property map values can either take the form of 1) a single value representing the end value,\r\n\t\t\t\t\t\t or 2) an array in the form of [ endValue, [, easing] [, startValue] ].\r\n\t\t\t\t\t\t The optional third parameter is a forcefed startValue to be used instead of querying the DOM for\r\n\t\t\t\t\t\t the element\'s current value. Read Velocity\'s docmentation to learn more about forcefeeding: VelocityJS.org/#forcefeeding */\r\n\t\t\t\t\t\tvar parsePropertyValue = function(valueData, skipResolvingEasing) {\r\n\t\t\t\t\t\t\tvar endValue, easing, startValue;\r\n\r\n\t\t\t\t\t\t\t/* If we have a function as the main argument then resolve it first, in case it returns an array that needs to be split */\r\n\t\t\t\t\t\t\tif (Type.isFunction(valueData)) {\r\n\t\t\t\t\t\t\t\tvalueData = valueData.call(element, elementArrayIndex, elementsLength);\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* Handle the array format, which can be structured as one of three potential overloads:\r\n\t\t\t\t\t\t\t A) [ endValue, easing, startValue ], B) [ endValue, easing ], or C) [ endValue, startValue ] */\r\n\t\t\t\t\t\t\tif (Type.isArray(valueData)) {\r\n\t\t\t\t\t\t\t\t/* endValue is always the first item in the array. Don\'t bother validating endValue\'s value now\r\n\t\t\t\t\t\t\t\t since the ensuing property cycling logic does that. */\r\n\t\t\t\t\t\t\t\tendValue = valueData[0];\r\n\r\n\t\t\t\t\t\t\t\t/* Two-item array format: If the second item is a number, function, or hex string, treat it as a\r\n\t\t\t\t\t\t\t\t start value since easings can only be non-hex strings or arrays. */\r\n\t\t\t\t\t\t\t\tif ((!Type.isArray(valueData[1]) && /^[\\d-]/.test(valueData[1])) || Type.isFunction(valueData[1]) || CSS.RegEx.isHex.test(valueData[1])) {\r\n\t\t\t\t\t\t\t\t\tstartValue = valueData[1];\r\n\t\t\t\t\t\t\t\t\t/* Two or three-item array: If the second item is a non-hex string easing name or an array, treat it as an easing. */\r\n\t\t\t\t\t\t\t\t} else if ((Type.isString(valueData[1]) && !CSS.RegEx.isHex.test(valueData[1]) && Velocity.Easings[valueData[1]]) || Type.isArray(valueData[1])) {\r\n\t\t\t\t\t\t\t\t\teasing = skipResolvingEasing ? valueData[1] : getEasing(valueData[1], opts.duration);\r\n\r\n\t\t\t\t\t\t\t\t\t/* Don\'t bother validating startValue\'s value now since the ensuing property cycling logic inherently does that. */\r\n\t\t\t\t\t\t\t\t\tstartValue = valueData[2];\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tstartValue = valueData[1] || valueData[2];\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t/* Handle the single-value format. */\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tendValue = valueData;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* Default to the call\'s easing if a per-property easing type was not defined. */\r\n\t\t\t\t\t\t\tif (!skipResolvingEasing) {\r\n\t\t\t\t\t\t\t\teasing = easing || opts.easing;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* If functions were passed in as values, pass the function the current element as its context,\r\n\t\t\t\t\t\t\t plus the element\'s index and the element set\'s size as arguments. Then, assign the returned value. */\r\n\t\t\t\t\t\t\tif (Type.isFunction(endValue)) {\r\n\t\t\t\t\t\t\t\tendValue = endValue.call(element, elementArrayIndex, elementsLength);\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tif (Type.isFunction(startValue)) {\r\n\t\t\t\t\t\t\t\tstartValue = startValue.call(element, elementArrayIndex, elementsLength);\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* Allow startValue to be left as undefined to indicate to the ensuing code that its value was not forcefed. */\r\n\t\t\t\t\t\t\treturn [endValue || 0, easing, startValue];\r\n\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\tvar fixPropertyValue = function(property, valueData) {\r\n\t\t\t\t\t\t\t/* In case this property is a hook, there are circumstances where we will intend to work on the hook\'s root property and not the hooked subproperty. */\r\n\t\t\t\t\t\t\tvar rootProperty = CSS.Hooks.getRoot(property),\r\n\t\t\t\t\t\t\t\t\trootPropertyValue = false,\r\n\t\t\t\t\t\t\t\t\t/* Parse out endValue, easing, and startValue from the property\'s data. */\r\n\t\t\t\t\t\t\t\t\tendValue = valueData[0],\r\n\t\t\t\t\t\t\t\t\teasing = valueData[1],\r\n\t\t\t\t\t\t\t\t\tstartValue = valueData[2],\r\n\t\t\t\t\t\t\t\t\tpattern;\r\n\r\n\t\t\t\t\t\t\t/**************************\r\n\t\t\t\t\t\t\t Start Value Sourcing\r\n\t\t\t\t\t\t\t **************************/\r\n\r\n\t\t\t\t\t\t\t/* Other than for the dummy tween property, properties that are not supported by the browser (and do not have an associated normalization) will\r\n\t\t\t\t\t\t\t inherently produce no style changes when set, so they are skipped in order to decrease animation tick overhead.\r\n\t\t\t\t\t\t\t Property support is determined via prefixCheck(), which returns a false flag when no supported is detected. */\r\n\t\t\t\t\t\t\t/* Note: Since SVG elements have some of their properties directly applied as HTML attributes,\r\n\t\t\t\t\t\t\t there is no way to check for their explicit browser support, and so we skip skip this check for them. */\r\n\t\t\t\t\t\t\tif ((!data || !data.isSVG) && rootProperty !== "tween" && CSS.Names.prefixCheck(rootProperty)[1] === false && CSS.Normalizations.registered[rootProperty] === undefined) {\r\n\t\t\t\t\t\t\t\tif (Velocity.debug) {\r\n\t\t\t\t\t\t\t\t\tconsole.log("Skipping [" + rootProperty + "] due to a lack of browser support.");\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* If the display option is being set to a non-"none" (e.g. "block") and opacity (filter on IE<=8) is being\r\n\t\t\t\t\t\t\t animated to an endValue of non-zero, the user\'s intention is to fade in from invisible, thus we forcefeed opacity\r\n\t\t\t\t\t\t\t a startValue of 0 if its startValue hasn\'t already been sourced by value transferring or prior forcefeeding. */\r\n\t\t\t\t\t\t\tif (((opts.display !== undefined && opts.display !== null && opts.display !== "none") || (opts.visibility !== undefined && opts.visibility !== "hidden")) && /opacity|filter/.test(property) && !startValue && endValue !== 0) {\r\n\t\t\t\t\t\t\t\tstartValue = 0;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* If values have been transferred from the previous Velocity call, extract the endValue and rootPropertyValue\r\n\t\t\t\t\t\t\t for all of the current call\'s properties that were *also* animated in the previous call. */\r\n\t\t\t\t\t\t\t/* Note: Value transferring can optionally be disabled by the user via the _cacheValues option. */\r\n\t\t\t\t\t\t\tif (opts._cacheValues && lastTweensContainer && lastTweensContainer[property]) {\r\n\t\t\t\t\t\t\t\tif (startValue === undefined) {\r\n\t\t\t\t\t\t\t\t\tstartValue = lastTweensContainer[property].endValue + lastTweensContainer[property].unitType;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t/* The previous call\'s rootPropertyValue is extracted from the element\'s data cache since that\'s the\r\n\t\t\t\t\t\t\t\t instance of rootPropertyValue that gets freshly updated by the tweening process, whereas the rootPropertyValue\r\n\t\t\t\t\t\t\t\t attached to the incoming lastTweensContainer is equal to the root property\'s value prior to any tweening. */\r\n\t\t\t\t\t\t\t\trootPropertyValue = data.rootPropertyValueCache[rootProperty];\r\n\t\t\t\t\t\t\t\t/* If values were not transferred from a previous Velocity call, query the DOM as needed. */\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t/* Handle hooked properties. */\r\n\t\t\t\t\t\t\t\tif (CSS.Hooks.registered[property]) {\r\n\t\t\t\t\t\t\t\t\tif (startValue === undefined) {\r\n\t\t\t\t\t\t\t\t\t\trootPropertyValue = CSS.getPropertyValue(element, rootProperty); /* GET */\r\n\t\t\t\t\t\t\t\t\t\t/* Note: The following getPropertyValue() call does not actually trigger a DOM query;\r\n\t\t\t\t\t\t\t\t\t\t getPropertyValue() will extract the hook from rootPropertyValue. */\r\n\t\t\t\t\t\t\t\t\t\tstartValue = CSS.getPropertyValue(element, property, rootPropertyValue);\r\n\t\t\t\t\t\t\t\t\t\t/* If startValue is already defined via forcefeeding, do not query the DOM for the root property\'s value;\r\n\t\t\t\t\t\t\t\t\t\t just grab rootProperty\'s zero-value template from CSS.Hooks. This overwrites the element\'s actual\r\n\t\t\t\t\t\t\t\t\t\t root property value (if one is set), but this is acceptable since the primary reason users forcefeed is\r\n\t\t\t\t\t\t\t\t\t\t to avoid DOM queries, and thus we likewise avoid querying the DOM for the root property\'s value. */\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t/* Grab this hook\'s zero-value template, e.g. "0px 0px 0px black". */\r\n\t\t\t\t\t\t\t\t\t\trootPropertyValue = CSS.Hooks.templates[rootProperty][1];\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* Handle non-hooked properties that haven\'t already been defined via forcefeeding. */\r\n\t\t\t\t\t\t\t\t} else if (startValue === undefined) {\r\n\t\t\t\t\t\t\t\t\tstartValue = CSS.getPropertyValue(element, property); /* GET */\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/**************************\r\n\t\t\t\t\t\t\t Value Data Extraction\r\n\t\t\t\t\t\t\t **************************/\r\n\r\n\t\t\t\t\t\t\tvar separatedValue,\r\n\t\t\t\t\t\t\t\t\tendValueUnitType,\r\n\t\t\t\t\t\t\t\t\tstartValueUnitType,\r\n\t\t\t\t\t\t\t\t\toperator = false;\r\n\r\n\t\t\t\t\t\t\t/* Separates a property value into its numeric value and its unit type. */\r\n\t\t\t\t\t\t\tvar separateValue = function(property, value) {\r\n\t\t\t\t\t\t\t\tvar unitType,\r\n\t\t\t\t\t\t\t\t\t\tnumericValue;\r\n\r\n\t\t\t\t\t\t\t\tnumericValue = (value || "0")\r\n\t\t\t\t\t\t\t\t\t\t.toString()\r\n\t\t\t\t\t\t\t\t\t\t.toLowerCase()\r\n\t\t\t\t\t\t\t\t\t\t/* Match the unit type at the end of the value. */\r\n\t\t\t\t\t\t\t\t\t\t.replace(/[%A-z]+$/, function(match) {\r\n\t\t\t\t\t\t\t\t\t\t\t/* Grab the unit type. */\r\n\t\t\t\t\t\t\t\t\t\t\tunitType = match;\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t/* Strip the unit type off of value. */\r\n\t\t\t\t\t\t\t\t\t\t\treturn "";\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t/* If no unit type was supplied, assign one that is appropriate for this property (e.g. "deg" for rotateZ or "px" for width). */\r\n\t\t\t\t\t\t\t\tif (!unitType) {\r\n\t\t\t\t\t\t\t\t\tunitType = CSS.Values.getUnitType(property);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\treturn [numericValue, unitType];\r\n\t\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\t\tif (startValue !== endValue && Type.isString(startValue) && Type.isString(endValue)) {\r\n\t\t\t\t\t\t\t\tpattern = "";\r\n\t\t\t\t\t\t\t\tvar iStart = 0, // index in startValue\r\n\t\t\t\t\t\t\t\t\t\tiEnd = 0, // index in endValue\r\n\t\t\t\t\t\t\t\t\t\taStart = [], // array of startValue numbers\r\n\t\t\t\t\t\t\t\t\t\taEnd = [], // array of endValue numbers\r\n\t\t\t\t\t\t\t\t\t\tinCalc = 0, // Keep track of being inside a "calc()" so we don\'t duplicate it\r\n\t\t\t\t\t\t\t\t\t\tinRGB = 0, // Keep track of being inside an RGB as we can\'t use fractional values\r\n\t\t\t\t\t\t\t\t\t\tinRGBA = 0; // Keep track of being inside an RGBA as we must pass fractional for the alpha channel\r\n\r\n\t\t\t\t\t\t\t\tstartValue = CSS.Hooks.fixColors(startValue);\r\n\t\t\t\t\t\t\t\tendValue = CSS.Hooks.fixColors(endValue);\r\n\t\t\t\t\t\t\t\twhile (iStart < startValue.length && iEnd < endValue.length) {\r\n\t\t\t\t\t\t\t\t\tvar cStart = startValue[iStart],\r\n\t\t\t\t\t\t\t\t\t\t\tcEnd = endValue[iEnd];\r\n\r\n\t\t\t\t\t\t\t\t\tif (/[\\d\\.-]/.test(cStart) && /[\\d\\.-]/.test(cEnd)) {\r\n\t\t\t\t\t\t\t\t\t\tvar tStart = cStart, // temporary character buffer\r\n\t\t\t\t\t\t\t\t\t\t\t\ttEnd = cEnd, // temporary character buffer\r\n\t\t\t\t\t\t\t\t\t\t\t\tdotStart = ".", // Make sure we can only ever match a single dot in a decimal\r\n\t\t\t\t\t\t\t\t\t\t\t\tdotEnd = "."; // Make sure we can only ever match a single dot in a decimal\r\n\r\n\t\t\t\t\t\t\t\t\t\twhile (++iStart < startValue.length) {\r\n\t\t\t\t\t\t\t\t\t\t\tcStart = startValue[iStart];\r\n\t\t\t\t\t\t\t\t\t\t\tif (cStart === dotStart) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tdotStart = ".."; // Can never match two characters\r\n\t\t\t\t\t\t\t\t\t\t\t} else if (!/\\d/.test(cStart)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\ttStart += cStart;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\twhile (++iEnd < endValue.length) {\r\n\t\t\t\t\t\t\t\t\t\t\tcEnd = endValue[iEnd];\r\n\t\t\t\t\t\t\t\t\t\t\tif (cEnd === dotEnd) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tdotEnd = ".."; // Can never match two characters\r\n\t\t\t\t\t\t\t\t\t\t\t} else if (!/\\d/.test(cEnd)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\ttEnd += cEnd;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tvar uStart = CSS.Hooks.getUnit(startValue, iStart), // temporary unit type\r\n\t\t\t\t\t\t\t\t\t\t\t\tuEnd = CSS.Hooks.getUnit(endValue, iEnd); // temporary unit type\r\n\r\n\t\t\t\t\t\t\t\t\t\tiStart += uStart.length;\r\n\t\t\t\t\t\t\t\t\t\tiEnd += uEnd.length;\r\n\t\t\t\t\t\t\t\t\t\tif (uStart === uEnd) {\r\n\t\t\t\t\t\t\t\t\t\t\t// Same units\r\n\t\t\t\t\t\t\t\t\t\t\tif (tStart === tEnd) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Same numbers, so just copy over\r\n\t\t\t\t\t\t\t\t\t\t\t\tpattern += tStart + uStart;\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Different numbers, so store them\r\n\t\t\t\t\t\t\t\t\t\t\t\tpattern += "{" + aStart.length + (inRGB ? "!" : "") + "}" + uStart;\r\n\t\t\t\t\t\t\t\t\t\t\t\taStart.push(parseFloat(tStart));\r\n\t\t\t\t\t\t\t\t\t\t\t\taEnd.push(parseFloat(tEnd));\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t// Different units, so put into a "calc(from + to)" and animate each side to/from zero\r\n\t\t\t\t\t\t\t\t\t\t\tvar nStart = parseFloat(tStart),\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tnEnd = parseFloat(tEnd);\r\n\r\n\t\t\t\t\t\t\t\t\t\t\tpattern += (inCalc < 5 ? "calc" : "") + "("\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t+ (nStart ? "{" + aStart.length + (inRGB ? "!" : "") + "}" : "0") + uStart\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t+ " + "\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t+ (nEnd ? "{" + (aStart.length + (nStart ? 1 : 0)) + (inRGB ? "!" : "") + "}" : "0") + uEnd\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t+ ")";\r\n\t\t\t\t\t\t\t\t\t\t\tif (nStart) {\r\n\t\t\t\t\t\t\t\t\t\t\t\taStart.push(nStart);\r\n\t\t\t\t\t\t\t\t\t\t\t\taEnd.push(0);\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tif (nEnd) {\r\n\t\t\t\t\t\t\t\t\t\t\t\taStart.push(0);\r\n\t\t\t\t\t\t\t\t\t\t\t\taEnd.push(nEnd);\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t} else if (cStart === cEnd) {\r\n\t\t\t\t\t\t\t\t\t\tpattern += cStart;\r\n\t\t\t\t\t\t\t\t\t\tiStart++;\r\n\t\t\t\t\t\t\t\t\t\tiEnd++;\r\n\t\t\t\t\t\t\t\t\t\t// Keep track of being inside a calc()\r\n\t\t\t\t\t\t\t\t\t\tif (inCalc === 0 && cStart === "c"\r\n\t\t\t\t\t\t\t\t\t\t\t\t|| inCalc === 1 && cStart === "a"\r\n\t\t\t\t\t\t\t\t\t\t\t\t|| inCalc === 2 && cStart === "l"\r\n\t\t\t\t\t\t\t\t\t\t\t\t|| inCalc === 3 && cStart === "c"\r\n\t\t\t\t\t\t\t\t\t\t\t\t|| inCalc >= 4 && cStart === "("\r\n\t\t\t\t\t\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\t\t\t\tinCalc++;\r\n\t\t\t\t\t\t\t\t\t\t} else if ((inCalc && inCalc < 5)\r\n\t\t\t\t\t\t\t\t\t\t\t\t|| inCalc >= 4 && cStart === ")" && --inCalc < 5) {\r\n\t\t\t\t\t\t\t\t\t\t\tinCalc = 0;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t// Keep track of being inside an rgb() / rgba()\r\n\t\t\t\t\t\t\t\t\t\tif (inRGB === 0 && cStart === "r"\r\n\t\t\t\t\t\t\t\t\t\t\t\t|| inRGB === 1 && cStart === "g"\r\n\t\t\t\t\t\t\t\t\t\t\t\t|| inRGB === 2 && cStart === "b"\r\n\t\t\t\t\t\t\t\t\t\t\t\t|| inRGB === 3 && cStart === "a"\r\n\t\t\t\t\t\t\t\t\t\t\t\t|| inRGB >= 3 && cStart === "("\r\n\t\t\t\t\t\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\t\t\t\tif (inRGB === 3 && cStart === "a") {\r\n\t\t\t\t\t\t\t\t\t\t\t\tinRGBA = 1;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tinRGB++;\r\n\t\t\t\t\t\t\t\t\t\t} else if (inRGBA && cStart === ",") {\r\n\t\t\t\t\t\t\t\t\t\t\tif (++inRGBA > 3) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tinRGB = inRGBA = 0;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t} else if ((inRGBA && inRGB < (inRGBA ? 5 : 4))\r\n\t\t\t\t\t\t\t\t\t\t\t\t|| inRGB >= (inRGBA ? 4 : 3) && cStart === ")" && --inRGB < (inRGBA ? 5 : 4)) {\r\n\t\t\t\t\t\t\t\t\t\t\tinRGB = inRGBA = 0;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tinCalc = 0;\r\n\t\t\t\t\t\t\t\t\t\t// TODO: changing units, fixing colours\r\n\t\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (iStart !== startValue.length || iEnd !== endValue.length) {\r\n\t\t\t\t\t\t\t\t\tif (Velocity.debug) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.error("Trying to pattern match mis-matched strings [\\"" + endValue + "\\", \\"" + startValue + "\\"]");\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tpattern = undefined;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (pattern) {\r\n\t\t\t\t\t\t\t\t\tif (aStart.length) {\r\n\t\t\t\t\t\t\t\t\t\tif (Velocity.debug) {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log("Pattern found \\"" + pattern + "\\" -> ", aStart, aEnd, "[" + startValue + "," + endValue + "]");\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tstartValue = aStart;\r\n\t\t\t\t\t\t\t\t\t\tendValue = aEnd;\r\n\t\t\t\t\t\t\t\t\t\tendValueUnitType = startValueUnitType = "";\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tpattern = undefined;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tif (!pattern) {\r\n\t\t\t\t\t\t\t\t/* Separate startValue. */\r\n\t\t\t\t\t\t\t\tseparatedValue = separateValue(property, startValue);\r\n\t\t\t\t\t\t\t\tstartValue = separatedValue[0];\r\n\t\t\t\t\t\t\t\tstartValueUnitType = separatedValue[1];\r\n\r\n\t\t\t\t\t\t\t\t/* Separate endValue, and extract a value operator (e.g. "+=", "-=") if one exists. */\r\n\t\t\t\t\t\t\t\tseparatedValue = separateValue(property, endValue);\r\n\t\t\t\t\t\t\t\tendValue = separatedValue[0].replace(/^([+-\\/*])=/, function(match, subMatch) {\r\n\t\t\t\t\t\t\t\t\toperator = subMatch;\r\n\r\n\t\t\t\t\t\t\t\t\t/* Strip the operator off of the value. */\r\n\t\t\t\t\t\t\t\t\treturn "";\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tendValueUnitType = separatedValue[1];\r\n\r\n\t\t\t\t\t\t\t\t/* Parse float values from endValue and startValue. Default to 0 if NaN is returned. */\r\n\t\t\t\t\t\t\t\tstartValue = parseFloat(startValue) || 0;\r\n\t\t\t\t\t\t\t\tendValue = parseFloat(endValue) || 0;\r\n\r\n\t\t\t\t\t\t\t\t/***************************************\r\n\t\t\t\t\t\t\t\t Property-Specific Value Conversion\r\n\t\t\t\t\t\t\t\t ***************************************/\r\n\r\n\t\t\t\t\t\t\t\t/* Custom support for properties that don\'t actually accept the % unit type, but where pollyfilling is trivial and relatively foolproof. */\r\n\t\t\t\t\t\t\t\tif (endValueUnitType === "%") {\r\n\t\t\t\t\t\t\t\t\t/* A %-value fontSize/lineHeight is relative to the parent\'s fontSize (as opposed to the parent\'s dimensions),\r\n\t\t\t\t\t\t\t\t\t which is identical to the em unit\'s behavior, so we piggyback off of that. */\r\n\t\t\t\t\t\t\t\t\tif (/^(fontSize|lineHeight)$/.test(property)) {\r\n\t\t\t\t\t\t\t\t\t\t/* Convert % into an em decimal value. */\r\n\t\t\t\t\t\t\t\t\t\tendValue = endValue / 100;\r\n\t\t\t\t\t\t\t\t\t\tendValueUnitType = "em";\r\n\t\t\t\t\t\t\t\t\t\t/* For scaleX and scaleY, convert the value into its decimal format and strip off the unit type. */\r\n\t\t\t\t\t\t\t\t\t} else if (/^scale/.test(property)) {\r\n\t\t\t\t\t\t\t\t\t\tendValue = endValue / 100;\r\n\t\t\t\t\t\t\t\t\t\tendValueUnitType = "";\r\n\t\t\t\t\t\t\t\t\t\t/* For RGB components, take the defined percentage of 255 and strip off the unit type. */\r\n\t\t\t\t\t\t\t\t\t} else if (/(Red|Green|Blue)$/i.test(property)) {\r\n\t\t\t\t\t\t\t\t\t\tendValue = (endValue / 100) * 255;\r\n\t\t\t\t\t\t\t\t\t\tendValueUnitType = "";\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/***************************\r\n\t\t\t\t\t\t\t Unit Ratio Calculation\r\n\t\t\t\t\t\t\t ***************************/\r\n\r\n\t\t\t\t\t\t\t/* When queried, the browser returns (most) CSS property values in pixels. Therefore, if an endValue with a unit type of\r\n\t\t\t\t\t\t\t %, em, or rem is animated toward, startValue must be converted from pixels into the same unit type as endValue in order\r\n\t\t\t\t\t\t\t for value manipulation logic (increment/decrement) to proceed. Further, if the startValue was forcefed or transferred\r\n\t\t\t\t\t\t\t from a previous call, startValue may also not be in pixels. Unit conversion logic therefore consists of two steps:\r\n\t\t\t\t\t\t\t 1) Calculating the ratio of %/em/rem/vh/vw relative to pixels\r\n\t\t\t\t\t\t\t 2) Converting startValue into the same unit of measurement as endValue based on these ratios. */\r\n\t\t\t\t\t\t\t/* Unit conversion ratios are calculated by inserting a sibling node next to the target node, copying over its position property,\r\n\t\t\t\t\t\t\t setting values with the target unit type then comparing the returned pixel value. */\r\n\t\t\t\t\t\t\t/* Note: Even if only one of these unit types is being animated, all unit ratios are calculated at once since the overhead\r\n\t\t\t\t\t\t\t of batching the SETs and GETs together upfront outweights the potential overhead\r\n\t\t\t\t\t\t\t of layout thrashing caused by re-querying for uncalculated ratios for subsequently-processed properties. */\r\n\t\t\t\t\t\t\t/* Todo: Shift this logic into the calls\' first tick instance so that it\'s synced with RAF. */\r\n\t\t\t\t\t\t\tvar calculateUnitRatios = function() {\r\n\r\n\t\t\t\t\t\t\t\t/************************\r\n\t\t\t\t\t\t\t\t Same Ratio Checks\r\n\t\t\t\t\t\t\t\t ************************/\r\n\r\n\t\t\t\t\t\t\t\t/* The properties below are used to determine whether the element differs sufficiently from this call\'s\r\n\t\t\t\t\t\t\t\t previously iterated element to also differ in its unit conversion ratios. If the properties match up with those\r\n\t\t\t\t\t\t\t\t of the prior element, the prior element\'s conversion ratios are used. Like most optimizations in Velocity,\r\n\t\t\t\t\t\t\t\t this is done to minimize DOM querying. */\r\n\t\t\t\t\t\t\t\tvar sameRatioIndicators = {\r\n\t\t\t\t\t\t\t\t\tmyParent: element.parentNode || document.body, /* GET */\r\n\t\t\t\t\t\t\t\t\tposition: CSS.getPropertyValue(element, "position"), /* GET */\r\n\t\t\t\t\t\t\t\t\tfontSize: CSS.getPropertyValue(element, "fontSize") /* GET */\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t/* Determine if the same % ratio can be used. % is based on the element\'s position value and its parent\'s width and height dimensions. */\r\n\t\t\t\t\t\t\t\t\t\tsamePercentRatio = ((sameRatioIndicators.position === callUnitConversionData.lastPosition) && (sameRatioIndicators.myParent === callUnitConversionData.lastParent)),\r\n\t\t\t\t\t\t\t\t\t\t/* Determine if the same em ratio can be used. em is relative to the element\'s fontSize. */\r\n\t\t\t\t\t\t\t\t\t\tsameEmRatio = (sameRatioIndicators.fontSize === callUnitConversionData.lastFontSize);\r\n\r\n\t\t\t\t\t\t\t\t/* Store these ratio indicators call-wide for the next element to compare against. */\r\n\t\t\t\t\t\t\t\tcallUnitConversionData.lastParent = sameRatioIndicators.myParent;\r\n\t\t\t\t\t\t\t\tcallUnitConversionData.lastPosition = sameRatioIndicators.position;\r\n\t\t\t\t\t\t\t\tcallUnitConversionData.lastFontSize = sameRatioIndicators.fontSize;\r\n\r\n\t\t\t\t\t\t\t\t/***************************\r\n\t\t\t\t\t\t\t\t Element-Specific Units\r\n\t\t\t\t\t\t\t\t ***************************/\r\n\r\n\t\t\t\t\t\t\t\t/* Note: IE8 rounds to the nearest pixel when returning CSS values, thus we perform conversions using a measurement\r\n\t\t\t\t\t\t\t\t of 100 (instead of 1) to give our ratios a precision of at least 2 decimal values. */\r\n\t\t\t\t\t\t\t\tvar measurement = 100,\r\n\t\t\t\t\t\t\t\t\t\tunitRatios = {};\r\n\r\n\t\t\t\t\t\t\t\tif (!sameEmRatio || !samePercentRatio) {\r\n\t\t\t\t\t\t\t\t\tvar dummy = data && data.isSVG ? document.createElementNS("http://www.w3.org/2000/svg", "rect") : document.createElement("div");\r\n\r\n\t\t\t\t\t\t\t\t\tVelocity.init(dummy);\r\n\t\t\t\t\t\t\t\t\tsameRatioIndicators.myParent.appendChild(dummy);\r\n\r\n\t\t\t\t\t\t\t\t\t/* To accurately and consistently calculate conversion ratios, the element\'s cascaded overflow and box-sizing are stripped.\r\n\t\t\t\t\t\t\t\t\t Similarly, since width/height can be artificially constrained by their min-/max- equivalents, these are controlled for as well. */\r\n\t\t\t\t\t\t\t\t\t/* Note: Overflow must be also be controlled for per-axis since the overflow property overwrites its per-axis values. */\r\n\t\t\t\t\t\t\t\t\t$.each(["overflow", "overflowX", "overflowY"], function(i, property) {\r\n\t\t\t\t\t\t\t\t\t\tVelocity.CSS.setPropertyValue(dummy, property, "hidden");\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tVelocity.CSS.setPropertyValue(dummy, "position", sameRatioIndicators.position);\r\n\t\t\t\t\t\t\t\t\tVelocity.CSS.setPropertyValue(dummy, "fontSize", sameRatioIndicators.fontSize);\r\n\t\t\t\t\t\t\t\t\tVelocity.CSS.setPropertyValue(dummy, "boxSizing", "content-box");\r\n\r\n\t\t\t\t\t\t\t\t\t/* width and height act as our proxy properties for measuring the horizontal and vertical % ratios. */\r\n\t\t\t\t\t\t\t\t\t$.each(["minWidth", "maxWidth", "width", "minHeight", "maxHeight", "height"], function(i, property) {\r\n\t\t\t\t\t\t\t\t\t\tVelocity.CSS.setPropertyValue(dummy, property, measurement + "%");\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t/* paddingLeft arbitrarily acts as our proxy property for the em ratio. */\r\n\t\t\t\t\t\t\t\t\tVelocity.CSS.setPropertyValue(dummy, "paddingLeft", measurement + "em");\r\n\r\n\t\t\t\t\t\t\t\t\t/* Divide the returned value by the measurement to get the ratio between 1% and 1px. Default to 1 since working with 0 can produce Infinite. */\r\n\t\t\t\t\t\t\t\t\tunitRatios.percentToPxWidth = callUnitConversionData.lastPercentToPxWidth = (parseFloat(CSS.getPropertyValue(dummy, "width", null, true)) || 1) / measurement; /* GET */\r\n\t\t\t\t\t\t\t\t\tunitRatios.percentToPxHeight = callUnitConversionData.lastPercentToPxHeight = (parseFloat(CSS.getPropertyValue(dummy, "height", null, true)) || 1) / measurement; /* GET */\r\n\t\t\t\t\t\t\t\t\tunitRatios.emToPx = callUnitConversionData.lastEmToPx = (parseFloat(CSS.getPropertyValue(dummy, "paddingLeft")) || 1) / measurement; /* GET */\r\n\r\n\t\t\t\t\t\t\t\t\tsameRatioIndicators.myParent.removeChild(dummy);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tunitRatios.emToPx = callUnitConversionData.lastEmToPx;\r\n\t\t\t\t\t\t\t\t\tunitRatios.percentToPxWidth = callUnitConversionData.lastPercentToPxWidth;\r\n\t\t\t\t\t\t\t\t\tunitRatios.percentToPxHeight = callUnitConversionData.lastPercentToPxHeight;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t/***************************\r\n\t\t\t\t\t\t\t\t Element-Agnostic Units\r\n\t\t\t\t\t\t\t\t ***************************/\r\n\r\n\t\t\t\t\t\t\t\t/* Whereas % and em ratios are determined on a per-element basis, the rem unit only needs to be checked\r\n\t\t\t\t\t\t\t\t once per call since it\'s exclusively dependant upon document.body\'s fontSize. If this is the first time\r\n\t\t\t\t\t\t\t\t that calculateUnitRatios() is being run during this call, remToPx will still be set to its default value of null,\r\n\t\t\t\t\t\t\t\t so we calculate it now. */\r\n\t\t\t\t\t\t\t\tif (callUnitConversionData.remToPx === null) {\r\n\t\t\t\t\t\t\t\t\t/* Default to browsers\' default fontSize of 16px in the case of 0. */\r\n\t\t\t\t\t\t\t\t\tcallUnitConversionData.remToPx = parseFloat(CSS.getPropertyValue(document.body, "fontSize")) || 16; /* GET */\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t/* Similarly, viewport units are %-relative to the window\'s inner dimensions. */\r\n\t\t\t\t\t\t\t\tif (callUnitConversionData.vwToPx === null) {\r\n\t\t\t\t\t\t\t\t\tcallUnitConversionData.vwToPx = parseFloat(window.innerWidth) / 100; /* GET */\r\n\t\t\t\t\t\t\t\t\tcallUnitConversionData.vhToPx = parseFloat(window.innerHeight) / 100; /* GET */\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tunitRatios.remToPx = callUnitConversionData.remToPx;\r\n\t\t\t\t\t\t\t\tunitRatios.vwToPx = callUnitConversionData.vwToPx;\r\n\t\t\t\t\t\t\t\tunitRatios.vhToPx = callUnitConversionData.vhToPx;\r\n\r\n\t\t\t\t\t\t\t\tif (Velocity.debug >= 1) {\r\n\t\t\t\t\t\t\t\t\tconsole.log("Unit ratios: " + JSON.stringify(unitRatios), element);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\treturn unitRatios;\r\n\t\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\t\t/********************\r\n\t\t\t\t\t\t\t Unit Conversion\r\n\t\t\t\t\t\t\t ********************/\r\n\r\n\t\t\t\t\t\t\t/* The * and / operators, which are not passed in with an associated unit, inherently use startValue\'s unit. Skip value and unit conversion. */\r\n\t\t\t\t\t\t\tif (/[\\/*]/.test(operator)) {\r\n\t\t\t\t\t\t\t\tendValueUnitType = startValueUnitType;\r\n\t\t\t\t\t\t\t\t/* If startValue and endValue differ in unit type, convert startValue into the same unit type as endValue so that if endValueUnitType\r\n\t\t\t\t\t\t\t\t is a relative unit (%, em, rem), the values set during tweening will continue to be accurately relative even if the metrics they depend\r\n\t\t\t\t\t\t\t\t on are dynamically changing during the course of the animation. Conversely, if we always normalized into px and used px for setting values, the px ratio\r\n\t\t\t\t\t\t\t\t would become stale if the original unit being animated toward was relative and the underlying metrics change during the animation. */\r\n\t\t\t\t\t\t\t\t/* Since 0 is 0 in any unit type, no conversion is necessary when startValue is 0 -- we just start at 0 with endValueUnitType. */\r\n\t\t\t\t\t\t\t} else if ((startValueUnitType !== endValueUnitType) && startValue !== 0) {\r\n\t\t\t\t\t\t\t\t/* Unit conversion is also skipped when endValue is 0, but *startValueUnitType* must be used for tween values to remain accurate. */\r\n\t\t\t\t\t\t\t\t/* Note: Skipping unit conversion here means that if endValueUnitType was originally a relative unit, the animation won\'t relatively\r\n\t\t\t\t\t\t\t\t match the underlying metrics if they change, but this is acceptable since we\'re animating toward invisibility instead of toward visibility,\r\n\t\t\t\t\t\t\t\t which remains past the point of the animation\'s completion. */\r\n\t\t\t\t\t\t\t\tif (endValue === 0) {\r\n\t\t\t\t\t\t\t\t\tendValueUnitType = startValueUnitType;\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t/* By this point, we cannot avoid unit conversion (it\'s undesirable since it causes layout thrashing).\r\n\t\t\t\t\t\t\t\t\t If we haven\'t already, we trigger calculateUnitRatios(), which runs once per element per call. */\r\n\t\t\t\t\t\t\t\t\telementUnitConversionData = elementUnitConversionData || calculateUnitRatios();\r\n\r\n\t\t\t\t\t\t\t\t\t/* The following RegEx matches CSS properties that have their % values measured relative to the x-axis. */\r\n\t\t\t\t\t\t\t\t\t/* Note: W3C spec mandates that all of margin and padding\'s properties (even top and bottom) are %-relative to the *width* of the parent element. */\r\n\t\t\t\t\t\t\t\t\tvar axis = (/margin|padding|left|right|width|text|word|letter/i.test(property) || /X$/.test(property) || property === "x") ? "x" : "y";\r\n\r\n\t\t\t\t\t\t\t\t\t/* In order to avoid generating n^2 bespoke conversion functions, unit conversion is a two-step process:\r\n\t\t\t\t\t\t\t\t\t 1) Convert startValue into pixels. 2) Convert this new pixel value into endValue\'s unit type. */\r\n\t\t\t\t\t\t\t\t\tswitch (startValueUnitType) {\r\n\t\t\t\t\t\t\t\t\t\tcase "%":\r\n\t\t\t\t\t\t\t\t\t\t\t/* Note: translateX and translateY are the only properties that are %-relative to an element\'s own dimensions -- not its parent\'s dimensions.\r\n\t\t\t\t\t\t\t\t\t\t\t Velocity does not include a special conversion process to account for this behavior. Therefore, animating translateX/Y from a % value\r\n\t\t\t\t\t\t\t\t\t\t\t to a non-% value will produce an incorrect start value. Fortunately, this sort of cross-unit conversion is rarely done by users in practice. */\r\n\t\t\t\t\t\t\t\t\t\t\tstartValue *= (axis === "x" ? elementUnitConversionData.percentToPxWidth : elementUnitConversionData.percentToPxHeight);\r\n\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\t\t\t\t\t\tcase "px":\r\n\t\t\t\t\t\t\t\t\t\t\t/* px acts as our midpoint in the unit conversion process; do nothing. */\r\n\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\t\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t\t\t\t\t\tstartValue *= elementUnitConversionData[startValueUnitType + "ToPx"];\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t/* Invert the px ratios to convert into to the target unit. */\r\n\t\t\t\t\t\t\t\t\tswitch (endValueUnitType) {\r\n\t\t\t\t\t\t\t\t\t\tcase "%":\r\n\t\t\t\t\t\t\t\t\t\t\tstartValue *= 1 / (axis === "x" ? elementUnitConversionData.percentToPxWidth : elementUnitConversionData.percentToPxHeight);\r\n\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\t\t\t\t\t\tcase "px":\r\n\t\t\t\t\t\t\t\t\t\t\t/* startValue is already in px, do nothing; we\'re done. */\r\n\t\t\t\t\t\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\t\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t\t\t\t\t\tstartValue *= 1 / elementUnitConversionData[endValueUnitType + "ToPx"];\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/*********************\r\n\t\t\t\t\t\t\t Relative Values\r\n\t\t\t\t\t\t\t *********************/\r\n\r\n\t\t\t\t\t\t\t/* Operator logic must be performed last since it requires unit-normalized start and end values. */\r\n\t\t\t\t\t\t\t/* Note: Relative *percent values* do not behave how most people think; while one would expect "+=50%"\r\n\t\t\t\t\t\t\t to increase the property 1.5x its current value, it in fact increases the percent units in absolute terms:\r\n\t\t\t\t\t\t\t 50 points is added on top of the current % value. */\r\n\t\t\t\t\t\t\tswitch (operator) {\r\n\t\t\t\t\t\t\t\tcase "+":\r\n\t\t\t\t\t\t\t\t\tendValue = startValue + endValue;\r\n\t\t\t\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\t\t\t\tcase "-":\r\n\t\t\t\t\t\t\t\t\tendValue = startValue - endValue;\r\n\t\t\t\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\t\t\t\tcase "*":\r\n\t\t\t\t\t\t\t\t\tendValue = startValue * endValue;\r\n\t\t\t\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\t\t\t\tcase "/":\r\n\t\t\t\t\t\t\t\t\tendValue = startValue / endValue;\r\n\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/**************************\r\n\t\t\t\t\t\t\t tweensContainer Push\r\n\t\t\t\t\t\t\t **************************/\r\n\r\n\t\t\t\t\t\t\t/* Construct the per-property tween object, and push it to the element\'s tweensContainer. */\r\n\t\t\t\t\t\t\ttweensContainer[property] = {\r\n\t\t\t\t\t\t\t\trootPropertyValue: rootPropertyValue,\r\n\t\t\t\t\t\t\t\tstartValue: startValue,\r\n\t\t\t\t\t\t\t\tcurrentValue: startValue,\r\n\t\t\t\t\t\t\t\tendValue: endValue,\r\n\t\t\t\t\t\t\t\tunitType: endValueUnitType,\r\n\t\t\t\t\t\t\t\teasing: easing\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\tif (pattern) {\r\n\t\t\t\t\t\t\t\ttweensContainer[property].pattern = pattern;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tif (Velocity.debug) {\r\n\t\t\t\t\t\t\t\tconsole.log("tweensContainer (" + property + "): " + JSON.stringify(tweensContainer[property]), element);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\t/* Create a tween out of each property, and append its associated data to tweensContainer. */\r\n\t\t\t\t\t\tfor (var property in propertiesMap) {\r\n\r\n\t\t\t\t\t\t\tif (!propertiesMap.hasOwnProperty(property)) {\r\n\t\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t/* The original property name\'s format must be used for the parsePropertyValue() lookup,\r\n\t\t\t\t\t\t\t but we then use its camelCase styling to normalize it for manipulation. */\r\n\t\t\t\t\t\t\tvar propertyName = CSS.Names.camelCase(property),\r\n\t\t\t\t\t\t\t\t\tvalueData = parsePropertyValue(propertiesMap[property]);\r\n\r\n\t\t\t\t\t\t\t/* Find shorthand color properties that have been passed a hex string. */\r\n\t\t\t\t\t\t\t/* Would be quicker to use CSS.Lists.colors.includes() if possible */\r\n\t\t\t\t\t\t\tif (_inArray(CSS.Lists.colors, propertyName)) {\r\n\t\t\t\t\t\t\t\t/* Parse the value data for each shorthand. */\r\n\t\t\t\t\t\t\t\tvar endValue = valueData[0],\r\n\t\t\t\t\t\t\t\t\t\teasing = valueData[1],\r\n\t\t\t\t\t\t\t\t\t\tstartValue = valueData[2];\r\n\r\n\t\t\t\t\t\t\t\tif (CSS.RegEx.isHex.test(endValue)) {\r\n\t\t\t\t\t\t\t\t\t/* Convert the hex strings into their RGB component arrays. */\r\n\t\t\t\t\t\t\t\t\tvar colorComponents = ["Red", "Green", "Blue"],\r\n\t\t\t\t\t\t\t\t\t\t\tendValueRGB = CSS.Values.hexToRgb(endValue),\r\n\t\t\t\t\t\t\t\t\t\t\tstartValueRGB = startValue ? CSS.Values.hexToRgb(startValue) : undefined;\r\n\r\n\t\t\t\t\t\t\t\t\t/* Inject the RGB component tweens into propertiesMap. */\r\n\t\t\t\t\t\t\t\t\tfor (var i = 0; i < colorComponents.length; i++) {\r\n\t\t\t\t\t\t\t\t\t\tvar dataArray = [endValueRGB[i]];\r\n\r\n\t\t\t\t\t\t\t\t\t\tif (easing) {\r\n\t\t\t\t\t\t\t\t\t\t\tdataArray.push(easing);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\tif (startValueRGB !== undefined) {\r\n\t\t\t\t\t\t\t\t\t\t\tdataArray.push(startValueRGB[i]);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\tfixPropertyValue(propertyName + colorComponents[i], dataArray);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t/* If we have replaced a shortcut color value then don\'t update the standard property name */\r\n\t\t\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tfixPropertyValue(propertyName, valueData);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Along with its property data, store a reference to the element itself onto tweensContainer. */\r\n\t\t\t\t\t\ttweensContainer.element = element;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/*****************\r\n\t\t\t\t\t Call Push\r\n\t\t\t\t\t *****************/\r\n\r\n\t\t\t\t\t/* Note: tweensContainer can be empty if all of the properties in this call\'s property map were skipped due to not\r\n\t\t\t\t\t being supported by the browser. The element property is used for checking that the tweensContainer has been appended to. */\r\n\t\t\t\t\tif (tweensContainer.element) {\r\n\t\t\t\t\t\t/* Apply the "velocity-animating" indicator class. */\r\n\t\t\t\t\t\tCSS.Values.addClass(element, "velocity-animating");\r\n\r\n\t\t\t\t\t\t/* The call array houses the tweensContainers for each element being animated in the current call. */\r\n\t\t\t\t\t\tcall.push(tweensContainer);\r\n\r\n\t\t\t\t\t\tdata = Data(element);\r\n\r\n\t\t\t\t\t\tif (data) {\r\n\t\t\t\t\t\t\t/* Store the tweensContainer and options if we\'re working on the default effects queue, so that they can be used by the reverse command. */\r\n\t\t\t\t\t\t\tif (opts.queue === "") {\r\n\r\n\t\t\t\t\t\t\t\tdata.tweensContainer = tweensContainer;\r\n\t\t\t\t\t\t\t\tdata.opts = opts;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* Switch on the element\'s animating flag. */\r\n\t\t\t\t\t\t\tdata.isAnimating = true;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Once the final element in this call\'s element set has been processed, push the call array onto\r\n\t\t\t\t\t\t Velocity.State.calls for the animation tick to immediately begin processing. */\r\n\t\t\t\t\t\tif (elementsIndex === elementsLength - 1) {\r\n\t\t\t\t\t\t\t/* Add the current call plus its associated metadata (the element set and the call\'s options) onto the global call container.\r\n\t\t\t\t\t\t\t Anything on this call container is subjected to tick() processing. */\r\n\t\t\t\t\t\t\tVelocity.State.calls.push([call, elements, opts, null, promiseData.resolver, null, 0]);\r\n\r\n\t\t\t\t\t\t\t/* If the animation tick isn\'t running, start it. (Velocity shuts it off when there are no active calls to process.) */\r\n\t\t\t\t\t\t\tif (Velocity.State.isTicking === false) {\r\n\t\t\t\t\t\t\t\tVelocity.State.isTicking = true;\r\n\r\n\t\t\t\t\t\t\t\t/* Start the tick loop. */\r\n\t\t\t\t\t\t\t\ttick();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\telementsIndex++;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* When the queue option is set to false, the call skips the element\'s queue and fires immediately. */\r\n\t\t\t\tif (opts.queue === false) {\r\n\t\t\t\t\t/* Since this buildQueue call doesn\'t respect the element\'s existing queue (which is where a delay option would have been appended),\r\n\t\t\t\t\t we manually inject the delay property here with an explicit setTimeout. */\r\n\t\t\t\t\tif (opts.delay) {\r\n\r\n\t\t\t\t\t\t/* Temporarily store delayed elements to facilitate access for global pause/resume */\r\n\t\t\t\t\t\tvar callIndex = Velocity.State.delayedElements.count++;\r\n\t\t\t\t\t\tVelocity.State.delayedElements[callIndex] = element;\r\n\r\n\t\t\t\t\t\tvar delayComplete = (function(index) {\r\n\t\t\t\t\t\t\treturn function() {\r\n\t\t\t\t\t\t\t\t/* Clear the temporary element */\r\n\t\t\t\t\t\t\t\tVelocity.State.delayedElements[index] = false;\r\n\r\n\t\t\t\t\t\t\t\t/* Finally, issue the call */\r\n\t\t\t\t\t\t\t\tbuildQueue();\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t})(callIndex);\r\n\r\n\t\t\t\t\t\tData(element).delayBegin = (new Date()).getTime();\r\n\t\t\t\t\t\tData(element).delay = parseFloat(opts.delay);\r\n\t\t\t\t\t\tData(element).delayTimer = {\r\n\t\t\t\t\t\t\tsetTimeout: setTimeout(buildQueue, parseFloat(opts.delay)),\r\n\t\t\t\t\t\t\tnext: delayComplete\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tbuildQueue();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t/* Otherwise, the call undergoes element queueing as normal. */\r\n\t\t\t\t\t/* Note: To interoperate with jQuery, Velocity uses jQuery\'s own $.queue() stack for queuing logic. */\r\n\t\t\t\t} else {\r\n\t\t\t\t\t$.queue(element, opts.queue, function(next, clearQueue) {\r\n\t\t\t\t\t\t/* If the clearQueue flag was passed in by the stop command, resolve this call\'s promise. (Promises can only be resolved once,\r\n\t\t\t\t\t\t so it\'s fine if this is repeatedly triggered for each element in the associated call.) */\r\n\t\t\t\t\t\tif (clearQueue === true) {\r\n\t\t\t\t\t\t\tif (promiseData.promise) {\r\n\t\t\t\t\t\t\t\tpromiseData.resolver(elements);\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t/* Do not continue with animation queueing. */\r\n\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* This flag indicates to the upcoming completeCall() function that this queue entry was initiated by Velocity.\r\n\t\t\t\t\t\t See completeCall() for further details. */\r\n\t\t\t\t\t\tVelocity.velocityQueueEntryFlag = true;\r\n\r\n\t\t\t\t\t\tbuildQueue(next);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/*********************\r\n\t\t\t\t Auto-Dequeuing\r\n\t\t\t\t *********************/\r\n\r\n\t\t\t\t/* As per jQuery\'s $.queue() behavior, to fire the first non-custom-queue entry on an element, the element\r\n\t\t\t\t must be dequeued if its queue stack consists *solely* of the current call. (This can be determined by checking\r\n\t\t\t\t for the "inprogress" item that jQuery prepends to active queue stack arrays.) Regardless, whenever the element\'s\r\n\t\t\t\t queue is further appended with additional items -- including $.delay()\'s or even $.animate() calls, the queue\'s\r\n\t\t\t\t first entry is automatically fired. This behavior contrasts that of custom queues, which never auto-fire. */\r\n\t\t\t\t/* Note: When an element set is being subjected to a non-parallel Velocity call, the animation will not begin until\r\n\t\t\t\t each one of the elements in the set has reached the end of its individually pre-existing queue chain. */\r\n\t\t\t\t/* Note: Unfortunately, most people don\'t fully grasp jQuery\'s powerful, yet quirky, $.queue() function.\r\n\t\t\t\t Lean more here: http://stackoverflow.com/questions/1058158/can-somebody-explain-jquery-queue-to-me */\r\n\t\t\t\tif ((opts.queue === "" || opts.queue === "fx") && $.queue(element)[0] !== "inprogress") {\r\n\t\t\t\t\t$.dequeue(element);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/**************************\r\n\t\t\t Element Set Iteration\r\n\t\t\t **************************/\r\n\r\n\t\t\t/* If the "nodeType" property exists on the elements variable, we\'re animating a single element.\r\n\t\t\t Place it in an array so that $.each() can iterate over it. */\r\n\t\t\t$.each(elements, function(i, element) {\r\n\t\t\t\t/* Ensure each element in a set has a nodeType (is a real element) to avoid throwing errors. */\r\n\t\t\t\tif (Type.isNode(element)) {\r\n\t\t\t\t\tprocessElement(element, i);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t/******************\r\n\t\t\t Option: Loop\r\n\t\t\t ******************/\r\n\r\n\t\t\t/* The loop option accepts an integer indicating how many times the element should loop between the values in the\r\n\t\t\t current call\'s properties map and the element\'s property values prior to this call. */\r\n\t\t\t/* Note: The loop option\'s logic is performed here -- after element processing -- because the current call needs\r\n\t\t\t to undergo its queue insertion prior to the loop option generating its series of constituent "reverse" calls,\r\n\t\t\t which chain after the current call. Two reverse calls (two "alternations") constitute one loop. */\r\n\t\t\topts = $.extend({}, Velocity.defaults, options);\r\n\t\t\topts.loop = parseInt(opts.loop, 10);\r\n\t\t\tvar reverseCallsCount = (opts.loop * 2) - 1;\r\n\r\n\t\t\tif (opts.loop) {\r\n\t\t\t\t/* Double the loop count to convert it into its appropriate number of "reverse" calls.\r\n\t\t\t\t Subtract 1 from the resulting value since the current call is included in the total alternation count. */\r\n\t\t\t\tfor (var x = 0; x < reverseCallsCount; x++) {\r\n\t\t\t\t\t/* Since the logic for the reverse action occurs inside Queueing and therefore this call\'s options object\r\n\t\t\t\t\t isn\'t parsed until then as well, the current call\'s delay option must be explicitly passed into the reverse\r\n\t\t\t\t\t call so that the delay logic that occurs inside *Pre-Queueing* can process it. */\r\n\t\t\t\t\tvar reverseOptions = {\r\n\t\t\t\t\t\tdelay: opts.delay,\r\n\t\t\t\t\t\tprogress: opts.progress\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t/* If a complete callback was passed into this call, transfer it to the loop redirect\'s final "reverse" call\r\n\t\t\t\t\t so that it\'s triggered when the entire redirect is complete (and not when the very first animation is complete). */\r\n\t\t\t\t\tif (x === reverseCallsCount - 1) {\r\n\t\t\t\t\t\treverseOptions.display = opts.display;\r\n\t\t\t\t\t\treverseOptions.visibility = opts.visibility;\r\n\t\t\t\t\t\treverseOptions.complete = opts.complete;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tanimate(elements, "reverse", reverseOptions);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/***************\r\n\t\t\t Chaining\r\n\t\t\t ***************/\r\n\r\n\t\t\t/* Return the elements back to the call chain, with wrapped elements taking precedence in case Velocity was called via the $.fn. extension. */\r\n\t\t\treturn getChain();\r\n\t\t};\r\n\r\n\t\t/* Turn Velocity into the animation function, extended with the pre-existing Velocity object. */\r\n\t\tVelocity = $.extend(animate, Velocity);\r\n\t\t/* For legacy support, also expose the literal animate method. */\r\n\t\tVelocity.animate = animate;\r\n\r\n\t\t/**************\r\n\t\t Timing\r\n\t\t **************/\r\n\r\n\t\t/* Ticker function. */\r\n\t\tvar ticker = window.requestAnimationFrame || rAFShim;\r\n\r\n\t\t/* Inactive browser tabs pause rAF, which results in all active animations immediately sprinting to their completion states when the tab refocuses.\r\n\t\t To get around this, we dynamically switch rAF to setTimeout (which the browser *doesn\'t* pause) when the tab loses focus. We skip this for mobile\r\n\t\t devices to avoid wasting battery power on inactive tabs. */\r\n\t\t/* Note: Tab focus detection doesn\'t work on older versions of IE, but that\'s okay since they don\'t support rAF to begin with. */\r\n\t\tif (!Velocity.State.isMobile && document.hidden !== undefined) {\r\n\t\t\tvar updateTicker = function() {\r\n\t\t\t\t/* Reassign the rAF function (which the global tick() function uses) based on the tab\'s focus state. */\r\n\t\t\t\tif (document.hidden) {\r\n\t\t\t\t\tticker = function(callback) {\r\n\t\t\t\t\t\t/* The tick function needs a truthy first argument in order to pass its internal timestamp check. */\r\n\t\t\t\t\t\treturn setTimeout(function() {\r\n\t\t\t\t\t\t\tcallback(true);\r\n\t\t\t\t\t\t}, 16);\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t/* The rAF loop has been paused by the browser, so we manually restart the tick. */\r\n\t\t\t\t\ttick();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tticker = window.requestAnimationFrame || rAFShim;\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\t/* Page could be sitting in the background at this time (i.e. opened as new tab) so making sure we use correct ticker from the start */\r\n\t\t\tupdateTicker();\r\n\r\n\t\t\t/* And then run check again every time visibility changes */\r\n\t\t\tdocument.addEventListener("visibilitychange", updateTicker);\r\n\t\t}\r\n\r\n\t\t/************\r\n\t\t Tick\r\n\t\t ************/\r\n\r\n\t\t/* Note: All calls to Velocity are pushed to the Velocity.State.calls array, which is fully iterated through upon each tick. */\r\n\t\tfunction tick(timestamp) {\r\n\t\t\t/* An empty timestamp argument indicates that this is the first tick occurence since ticking was turned on.\r\n\t\t\t We leverage this metadata to fully ignore the first tick pass since RAF\'s initial pass is fired whenever\r\n\t\t\t the browser\'s next tick sync time occurs, which results in the first elements subjected to Velocity\r\n\t\t\t calls being animated out of sync with any elements animated immediately thereafter. In short, we ignore\r\n\t\t\t the first RAF tick pass so that elements being immediately consecutively animated -- instead of simultaneously animated\r\n\t\t\t by the same Velocity call -- are properly batched into the same initial RAF tick and consequently remain in sync thereafter. */\r\n\t\t\tif (timestamp) {\r\n\t\t\t\t/* We normally use RAF\'s high resolution timestamp but as it can be significantly offset when the browser is\r\n\t\t\t\t under high stress we give the option for choppiness over allowing the browser to drop huge chunks of frames.\r\n\t\t\t\t We use performance.now() and shim it if it doesn\'t exist for when the tab is hidden. */\r\n\t\t\t\tvar timeCurrent = Velocity.timestamp && timestamp !== true ? timestamp : performance.now();\r\n\r\n\t\t\t\t/********************\r\n\t\t\t\t Call Iteration\r\n\t\t\t\t ********************/\r\n\r\n\t\t\t\tvar callsLength = Velocity.State.calls.length;\r\n\r\n\t\t\t\t/* To speed up iterating over this array, it is compacted (falsey items -- calls that have completed -- are removed)\r\n\t\t\t\t when its length has ballooned to a point that can impact tick performance. This only becomes necessary when animation\r\n\t\t\t\t has been continuous with many elements over a long period of time; whenever all active calls are completed, completeCall() clears Velocity.State.calls. */\r\n\t\t\t\tif (callsLength > 10000) {\r\n\t\t\t\t\tVelocity.State.calls = compactSparseArray(Velocity.State.calls);\r\n\t\t\t\t\tcallsLength = Velocity.State.calls.length;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* Iterate through each active call. */\r\n\t\t\t\tfor (var i = 0; i < callsLength; i++) {\r\n\t\t\t\t\t/* When a Velocity call is completed, its Velocity.State.calls entry is set to false. Continue on to the next call. */\r\n\t\t\t\t\tif (!Velocity.State.calls[i]) {\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/************************\r\n\t\t\t\t\t Call-Wide Variables\r\n\t\t\t\t\t ************************/\r\n\r\n\t\t\t\t\tvar callContainer = Velocity.State.calls[i],\r\n\t\t\t\t\t\t\tcall = callContainer[0],\r\n\t\t\t\t\t\t\topts = callContainer[2],\r\n\t\t\t\t\t\t\ttimeStart = callContainer[3],\r\n\t\t\t\t\t\t\tfirstTick = !timeStart,\r\n\t\t\t\t\t\t\ttweenDummyValue = null,\r\n\t\t\t\t\t\t\tpauseObject = callContainer[5],\r\n\t\t\t\t\t\t\tmillisecondsEllapsed = callContainer[6];\r\n\r\n\r\n\r\n\t\t\t\t\t/* If timeStart is undefined, then this is the first time that this call has been processed by tick().\r\n\t\t\t\t\t We assign timeStart now so that its value is as close to the real animation start time as possible.\r\n\t\t\t\t\t (Conversely, had timeStart been defined when this call was added to Velocity.State.calls, the delay\r\n\t\t\t\t\t between that time and now would cause the first few frames of the tween to be skipped since\r\n\t\t\t\t\t percentComplete is calculated relative to timeStart.) */\r\n\t\t\t\t\t/* Further, subtract 16ms (the approximate resolution of RAF) from the current time value so that the\r\n\t\t\t\t\t first tick iteration isn\'t wasted by animating at 0% tween completion, which would produce the\r\n\t\t\t\t\t same style value as the element\'s current value. */\r\n\t\t\t\t\tif (!timeStart) {\r\n\t\t\t\t\t\ttimeStart = Velocity.State.calls[i][3] = timeCurrent - 16;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* If a pause object is present, skip processing unless it has been set to resume */\r\n\t\t\t\t\tif (pauseObject) {\r\n\t\t\t\t\t\tif (pauseObject.resume === true) {\r\n\t\t\t\t\t\t\t/* Update the time start to accomodate the paused completion amount */\r\n\t\t\t\t\t\t\ttimeStart = callContainer[3] = Math.round(timeCurrent - millisecondsEllapsed - 16);\r\n\r\n\t\t\t\t\t\t\t/* Remove pause object after processing */\r\n\t\t\t\t\t\t\tcallContainer[5] = null;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tmillisecondsEllapsed = callContainer[6] = timeCurrent - timeStart;\r\n\r\n\t\t\t\t\t/* The tween\'s completion percentage is relative to the tween\'s start time, not the tween\'s start value\r\n\t\t\t\t\t (which would result in unpredictable tween durations since JavaScript\'s timers are not particularly accurate).\r\n\t\t\t\t\t Accordingly, we ensure that percentComplete does not exceed 1. */\r\n\t\t\t\t\tvar percentComplete = Math.min((millisecondsEllapsed) / opts.duration, 1);\r\n\r\n\t\t\t\t\t/**********************\r\n\t\t\t\t\t Element Iteration\r\n\t\t\t\t\t **********************/\r\n\r\n\t\t\t\t\t/* For every call, iterate through each of the elements in its set. */\r\n\t\t\t\t\tfor (var j = 0, callLength = call.length; j < callLength; j++) {\r\n\t\t\t\t\t\tvar tweensContainer = call[j],\r\n\t\t\t\t\t\t\t\telement = tweensContainer.element;\r\n\r\n\t\t\t\t\t\t/* Check to see if this element has been deleted midway through the animation by checking for the\r\n\t\t\t\t\t\t continued existence of its data cache. If it\'s gone, or the element is currently paused, skip animating this element. */\r\n\t\t\t\t\t\tif (!Data(element)) {\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tvar transformPropertyExists = false;\r\n\r\n\t\t\t\t\t\t/**********************************\r\n\t\t\t\t\t\t Display & Visibility Toggling\r\n\t\t\t\t\t\t **********************************/\r\n\r\n\t\t\t\t\t\t/* If the display option is set to non-"none", set it upfront so that the element can become visible before tweening begins.\r\n\t\t\t\t\t\t (Otherwise, display\'s "none" value is set in completeCall() once the animation has completed.) */\r\n\t\t\t\t\t\tif (opts.display !== undefined && opts.display !== null && opts.display !== "none") {\r\n\t\t\t\t\t\t\tif (opts.display === "flex") {\r\n\t\t\t\t\t\t\t\tvar flexValues = ["-webkit-box", "-moz-box", "-ms-flexbox", "-webkit-flex"];\r\n\r\n\t\t\t\t\t\t\t\t$.each(flexValues, function(i, flexValue) {\r\n\t\t\t\t\t\t\t\t\tCSS.setPropertyValue(element, "display", flexValue);\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tCSS.setPropertyValue(element, "display", opts.display);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Same goes with the visibility option, but its "none" equivalent is "hidden". */\r\n\t\t\t\t\t\tif (opts.visibility !== undefined && opts.visibility !== "hidden") {\r\n\t\t\t\t\t\t\tCSS.setPropertyValue(element, "visibility", opts.visibility);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/************************\r\n\t\t\t\t\t\t Property Iteration\r\n\t\t\t\t\t\t ************************/\r\n\r\n\t\t\t\t\t\t/* For every element, iterate through each property. */\r\n\t\t\t\t\t\tfor (var property in tweensContainer) {\r\n\t\t\t\t\t\t\t/* Note: In addition to property tween data, tweensContainer contains a reference to its associated element. */\r\n\t\t\t\t\t\t\tif (tweensContainer.hasOwnProperty(property) && property !== "element") {\r\n\t\t\t\t\t\t\t\tvar tween = tweensContainer[property],\r\n\t\t\t\t\t\t\t\t\t\tcurrentValue,\r\n\t\t\t\t\t\t\t\t\t\t/* Easing can either be a pre-genereated function or a string that references a pre-registered easing\r\n\t\t\t\t\t\t\t\t\t\t on the Velocity.Easings object. In either case, return the appropriate easing *function*. */\r\n\t\t\t\t\t\t\t\t\t\teasing = Type.isString(tween.easing) ? Velocity.Easings[tween.easing] : tween.easing;\r\n\r\n\t\t\t\t\t\t\t\t/******************************\r\n\t\t\t\t\t\t\t\t Current Value Calculation\r\n\t\t\t\t\t\t\t\t ******************************/\r\n\r\n\t\t\t\t\t\t\t\tif (Type.isString(tween.pattern)) {\r\n\t\t\t\t\t\t\t\t\tvar patternReplace = percentComplete === 1 ?\r\n\t\t\t\t\t\t\t\t\t\t\tfunction($0, index, round) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tvar result = tween.endValue[index];\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn round ? Math.round(result) : result;\r\n\t\t\t\t\t\t\t\t\t\t\t} :\r\n\t\t\t\t\t\t\t\t\t\t\tfunction($0, index, round) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tvar startValue = tween.startValue[index],\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttweenDelta = tween.endValue[index] - startValue,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tresult = startValue + (tweenDelta * easing(percentComplete, opts, tweenDelta));\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn round ? Math.round(result) : result;\r\n\t\t\t\t\t\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\t\t\t\tcurrentValue = tween.pattern.replace(/{(\\d+)(!)?}/g, patternReplace);\r\n\t\t\t\t\t\t\t\t} else if (percentComplete === 1) {\r\n\t\t\t\t\t\t\t\t\t/* If this is the last tick pass (if we\'ve reached 100% completion for this tween),\r\n\t\t\t\t\t\t\t\t\t ensure that currentValue is explicitly set to its target endValue so that it\'s not subjected to any rounding. */\r\n\t\t\t\t\t\t\t\t\tcurrentValue = tween.endValue;\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t/* Otherwise, calculate currentValue based on the current delta from startValue. */\r\n\t\t\t\t\t\t\t\t\tvar tweenDelta = tween.endValue - tween.startValue;\r\n\r\n\t\t\t\t\t\t\t\t\tcurrentValue = tween.startValue + (tweenDelta * easing(percentComplete, opts, tweenDelta));\r\n\t\t\t\t\t\t\t\t\t/* If no value change is occurring, don\'t proceed with DOM updating. */\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (!firstTick && (currentValue === tween.currentValue)) {\r\n\t\t\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\ttween.currentValue = currentValue;\r\n\r\n\t\t\t\t\t\t\t\t/* If we\'re tweening a fake \'tween\' property in order to log transition values, update the one-per-call variable so that\r\n\t\t\t\t\t\t\t\t it can be passed into the progress callback. */\r\n\t\t\t\t\t\t\t\tif (property === "tween") {\r\n\t\t\t\t\t\t\t\t\ttweenDummyValue = currentValue;\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t/******************\r\n\t\t\t\t\t\t\t\t\t Hooks: Part I\r\n\t\t\t\t\t\t\t\t\t ******************/\r\n\t\t\t\t\t\t\t\t\tvar hookRoot;\r\n\r\n\t\t\t\t\t\t\t\t\t/* For hooked properties, the newly-updated rootPropertyValueCache is cached onto the element so that it can be used\r\n\t\t\t\t\t\t\t\t\t for subsequent hooks in this call that are associated with the same root property. If we didn\'t cache the updated\r\n\t\t\t\t\t\t\t\t\t rootPropertyValue, each subsequent update to the root property in this tick pass would reset the previous hook\'s\r\n\t\t\t\t\t\t\t\t\t updates to rootPropertyValue prior to injection. A nice performance byproduct of rootPropertyValue caching is that\r\n\t\t\t\t\t\t\t\t\t subsequently chained animations using the same hookRoot but a different hook can use this cached rootPropertyValue. */\r\n\t\t\t\t\t\t\t\t\tif (CSS.Hooks.registered[property]) {\r\n\t\t\t\t\t\t\t\t\t\thookRoot = CSS.Hooks.getRoot(property);\r\n\r\n\t\t\t\t\t\t\t\t\t\tvar rootPropertyValueCache = Data(element).rootPropertyValueCache[hookRoot];\r\n\r\n\t\t\t\t\t\t\t\t\t\tif (rootPropertyValueCache) {\r\n\t\t\t\t\t\t\t\t\t\t\ttween.rootPropertyValue = rootPropertyValueCache;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t/*****************\r\n\t\t\t\t\t\t\t\t\t DOM Update\r\n\t\t\t\t\t\t\t\t\t *****************/\r\n\r\n\t\t\t\t\t\t\t\t\t/* setPropertyValue() returns an array of the property name and property value post any normalization that may have been performed. */\r\n\t\t\t\t\t\t\t\t\t/* Note: To solve an IE<=8 positioning bug, the unit type is dropped when setting a property value of 0. */\r\n\t\t\t\t\t\t\t\t\tvar adjustedSetData = CSS.setPropertyValue(element, /* SET */\r\n\t\t\t\t\t\t\t\t\t\t\tproperty,\r\n\t\t\t\t\t\t\t\t\t\t\ttween.currentValue + (IE < 9 && parseFloat(currentValue) === 0 ? "" : tween.unitType),\r\n\t\t\t\t\t\t\t\t\t\t\ttween.rootPropertyValue,\r\n\t\t\t\t\t\t\t\t\t\t\ttween.scrollData);\r\n\r\n\t\t\t\t\t\t\t\t\t/*******************\r\n\t\t\t\t\t\t\t\t\t Hooks: Part II\r\n\t\t\t\t\t\t\t\t\t *******************/\r\n\r\n\t\t\t\t\t\t\t\t\t/* Now that we have the hook\'s updated rootPropertyValue (the post-processed value provided by adjustedSetData), cache it onto the element. */\r\n\t\t\t\t\t\t\t\t\tif (CSS.Hooks.registered[property]) {\r\n\t\t\t\t\t\t\t\t\t\t/* Since adjustedSetData contains normalized data ready for DOM updating, the rootPropertyValue needs to be re-extracted from its normalized form. ?? */\r\n\t\t\t\t\t\t\t\t\t\tif (CSS.Normalizations.registered[hookRoot]) {\r\n\t\t\t\t\t\t\t\t\t\t\tData(element).rootPropertyValueCache[hookRoot] = CSS.Normalizations.registered[hookRoot]("extract", null, adjustedSetData[1]);\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tData(element).rootPropertyValueCache[hookRoot] = adjustedSetData[1];\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t/***************\r\n\t\t\t\t\t\t\t\t\t Transforms\r\n\t\t\t\t\t\t\t\t\t ***************/\r\n\r\n\t\t\t\t\t\t\t\t\t/* Flag whether a transform property is being animated so that flushTransformCache() can be triggered once this tick pass is complete. */\r\n\t\t\t\t\t\t\t\t\tif (adjustedSetData[0] === "transform") {\r\n\t\t\t\t\t\t\t\t\t\ttransformPropertyExists = true;\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/****************\r\n\t\t\t\t\t\t mobileHA\r\n\t\t\t\t\t\t ****************/\r\n\r\n\t\t\t\t\t\t/* If mobileHA is enabled, set the translate3d transform to null to force hardware acceleration.\r\n\t\t\t\t\t\t It\'s safe to override this property since Velocity doesn\'t actually support its animation (hooks are used in its place). */\r\n\t\t\t\t\t\tif (opts.mobileHA) {\r\n\t\t\t\t\t\t\t/* Don\'t set the null transform hack if we\'ve already done so. */\r\n\t\t\t\t\t\t\tif (Data(element).transformCache.translate3d === undefined) {\r\n\t\t\t\t\t\t\t\t/* All entries on the transformCache object are later concatenated into a single transform string via flushTransformCache(). */\r\n\t\t\t\t\t\t\t\tData(element).transformCache.translate3d = "(0px, 0px, 0px)";\r\n\r\n\t\t\t\t\t\t\t\ttransformPropertyExists = true;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (transformPropertyExists) {\r\n\t\t\t\t\t\t\tCSS.flushTransformCache(element);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* The non-"none" display value is only applied to an element once -- when its associated call is first ticked through.\r\n\t\t\t\t\t Accordingly, it\'s set to false so that it isn\'t re-processed by this call in the next tick. */\r\n\t\t\t\t\tif (opts.display !== undefined && opts.display !== "none") {\r\n\t\t\t\t\t\tVelocity.State.calls[i][2].display = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (opts.visibility !== undefined && opts.visibility !== "hidden") {\r\n\t\t\t\t\t\tVelocity.State.calls[i][2].visibility = false;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* Pass the elements and the timing data (percentComplete, msRemaining, timeStart, tweenDummyValue) into the progress callback. */\r\n\t\t\t\t\tif (opts.progress) {\r\n\t\t\t\t\t\topts.progress.call(callContainer[1],\r\n\t\t\t\t\t\t\t\tcallContainer[1],\r\n\t\t\t\t\t\t\t\tpercentComplete,\r\n\t\t\t\t\t\t\t\tMath.max(0, (timeStart + opts.duration) - timeCurrent),\r\n\t\t\t\t\t\t\t\ttimeStart,\r\n\t\t\t\t\t\t\t\ttweenDummyValue);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* If this call has finished tweening, pass its index to completeCall() to handle call cleanup. */\r\n\t\t\t\t\tif (percentComplete === 1) {\r\n\t\t\t\t\t\tcompleteCall(i);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/* Note: completeCall() sets the isTicking flag to false when the last call on Velocity.State.calls has completed. */\r\n\t\t\tif (Velocity.State.isTicking) {\r\n\t\t\t\tticker(tick);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/**********************\r\n\t\t Call Completion\r\n\t\t **********************/\r\n\r\n\t\t/* Note: Unlike tick(), which processes all active calls at once, call completion is handled on a per-call basis. */\r\n\t\tfunction completeCall(callIndex, isStopped) {\r\n\t\t\t/* Ensure the call exists. */\r\n\t\t\tif (!Velocity.State.calls[callIndex]) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\t/* Pull the metadata from the call. */\r\n\t\t\tvar call = Velocity.State.calls[callIndex][0],\r\n\t\t\t\t\telements = Velocity.State.calls[callIndex][1],\r\n\t\t\t\t\topts = Velocity.State.calls[callIndex][2],\r\n\t\t\t\t\tresolver = Velocity.State.calls[callIndex][4];\r\n\r\n\t\t\tvar remainingCallsExist = false;\r\n\r\n\t\t\t/*************************\r\n\t\t\t Element Finalization\r\n\t\t\t *************************/\r\n\r\n\t\t\tfor (var i = 0, callLength = call.length; i < callLength; i++) {\r\n\t\t\t\tvar element = call[i].element;\r\n\r\n\t\t\t\t/* If the user set display to "none" (intending to hide the element), set it now that the animation has completed. */\r\n\t\t\t\t/* Note: display:none isn\'t set when calls are manually stopped (via Velocity("stop"). */\r\n\t\t\t\t/* Note: Display gets ignored with "reverse" calls and infinite loops, since this behavior would be undesirable. */\r\n\t\t\t\tif (!isStopped && !opts.loop) {\r\n\t\t\t\t\tif (opts.display === "none") {\r\n\t\t\t\t\t\tCSS.setPropertyValue(element, "display", opts.display);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (opts.visibility === "hidden") {\r\n\t\t\t\t\t\tCSS.setPropertyValue(element, "visibility", opts.visibility);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* If the element\'s queue is empty (if only the "inprogress" item is left at position 0) or if its queue is about to run\r\n\t\t\t\t a non-Velocity-initiated entry, turn off the isAnimating flag. A non-Velocity-initiatied queue entry\'s logic might alter\r\n\t\t\t\t an element\'s CSS values and thereby cause Velocity\'s cached value data to go stale. To detect if a queue entry was initiated by Velocity,\r\n\t\t\t\t we check for the existence of our special Velocity.queueEntryFlag declaration, which minifiers won\'t rename since the flag\r\n\t\t\t\t is assigned to jQuery\'s global $ object and thus exists out of Velocity\'s own scope. */\r\n\t\t\t\tvar data = Data(element);\r\n\r\n\t\t\t\tif (opts.loop !== true && ($.queue(element)[1] === undefined || !/\\.velocityQueueEntryFlag/i.test($.queue(element)[1]))) {\r\n\t\t\t\t\t/* The element may have been deleted. Ensure that its data cache still exists before acting on it. */\r\n\t\t\t\t\tif (data) {\r\n\t\t\t\t\t\tdata.isAnimating = false;\r\n\t\t\t\t\t\t/* Clear the element\'s rootPropertyValueCache, which will become stale. */\r\n\t\t\t\t\t\tdata.rootPropertyValueCache = {};\r\n\r\n\t\t\t\t\t\tvar transformHAPropertyExists = false;\r\n\t\t\t\t\t\t/* If any 3D transform subproperty is at its default value (regardless of unit type), remove it. */\r\n\t\t\t\t\t\t$.each(CSS.Lists.transforms3D, function(i, transformName) {\r\n\t\t\t\t\t\t\tvar defaultValue = /^scale/.test(transformName) ? 1 : 0,\r\n\t\t\t\t\t\t\t\t\tcurrentValue = data.transformCache[transformName];\r\n\r\n\t\t\t\t\t\t\tif (data.transformCache[transformName] !== undefined && new RegExp("^\\\\(" + defaultValue + "[^.]").test(currentValue)) {\r\n\t\t\t\t\t\t\t\ttransformHAPropertyExists = true;\r\n\r\n\t\t\t\t\t\t\t\tdelete data.transformCache[transformName];\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t/* Mobile devices have hardware acceleration removed at the end of the animation in order to avoid hogging the GPU\'s memory. */\r\n\t\t\t\t\t\tif (opts.mobileHA) {\r\n\t\t\t\t\t\t\ttransformHAPropertyExists = true;\r\n\t\t\t\t\t\t\tdelete data.transformCache.translate3d;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Flush the subproperty removals to the DOM. */\r\n\t\t\t\t\t\tif (transformHAPropertyExists) {\r\n\t\t\t\t\t\t\tCSS.flushTransformCache(element);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t/* Remove the "velocity-animating" indicator class. */\r\n\t\t\t\t\t\tCSS.Values.removeClass(element, "velocity-animating");\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/*********************\r\n\t\t\t\t Option: Complete\r\n\t\t\t\t *********************/\r\n\r\n\t\t\t\t/* Complete is fired once per call (not once per element) and is passed the full raw DOM element set as both its context and its first argument. */\r\n\t\t\t\t/* Note: Callbacks aren\'t fired when calls are manually stopped (via Velocity("stop"). */\r\n\t\t\t\tif (!isStopped && opts.complete && !opts.loop && (i === callLength - 1)) {\r\n\t\t\t\t\t/* We throw callbacks in a setTimeout so that thrown errors don\'t halt the execution of Velocity itself. */\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\topts.complete.call(elements, elements);\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tthrow error;\r\n\t\t\t\t\t\t}, 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/**********************\r\n\t\t\t\t Promise Resolving\r\n\t\t\t\t **********************/\r\n\r\n\t\t\t\t/* Note: Infinite loops don\'t return promises. */\r\n\t\t\t\tif (resolver && opts.loop !== true) {\r\n\t\t\t\t\tresolver(elements);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/****************************\r\n\t\t\t\t Option: Loop (Infinite)\r\n\t\t\t\t ****************************/\r\n\r\n\t\t\t\tif (data && opts.loop === true && !isStopped) {\r\n\t\t\t\t\t/* If a rotateX/Y/Z property is being animated by 360 deg with loop:true, swap tween start/end values to enable\r\n\t\t\t\t\t continuous iterative rotation looping. (Otherise, the element would just rotate back and forth.) */\r\n\t\t\t\t\t$.each(data.tweensContainer, function(propertyName, tweenContainer) {\r\n\t\t\t\t\t\tif (/^rotate/.test(propertyName) && ((parseFloat(tweenContainer.startValue) - parseFloat(tweenContainer.endValue)) % 360 === 0)) {\r\n\t\t\t\t\t\t\tvar oldStartValue = tweenContainer.startValue;\r\n\r\n\t\t\t\t\t\t\ttweenContainer.startValue = tweenContainer.endValue;\r\n\t\t\t\t\t\t\ttweenContainer.endValue = oldStartValue;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (/^backgroundPosition/.test(propertyName) && parseFloat(tweenContainer.endValue) === 100 && tweenContainer.unitType === "%") {\r\n\t\t\t\t\t\t\ttweenContainer.endValue = 0;\r\n\t\t\t\t\t\t\ttweenContainer.startValue = 100;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tVelocity(element, "reverse", {loop: true, delay: opts.delay});\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/***************\r\n\t\t\t\t Dequeueing\r\n\t\t\t\t ***************/\r\n\r\n\t\t\t\t/* Fire the next call in the queue so long as this call\'s queue wasn\'t set to false (to trigger a parallel animation),\r\n\t\t\t\t which would have already caused the next call to fire. Note: Even if the end of the animation queue has been reached,\r\n\t\t\t\t $.dequeue() must still be called in order to completely clear jQuery\'s animation queue. */\r\n\t\t\t\tif (opts.queue !== false) {\r\n\t\t\t\t\t$.dequeue(element, opts.queue);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/************************\r\n\t\t\t Calls Array Cleanup\r\n\t\t\t ************************/\r\n\r\n\t\t\t/* Since this call is complete, set it to false so that the rAF tick skips it. This array is later compacted via compactSparseArray().\r\n\t\t\t (For performance reasons, the call is set to false instead of being deleted from the array: http://www.html5rocks.com/en/tutorials/speed/v8/) */\r\n\t\t\tVelocity.State.calls[callIndex] = false;\r\n\r\n\t\t\t/* Iterate through the calls array to determine if this was the final in-progress animation.\r\n\t\t\t If so, set a flag to end ticking and clear the calls array. */\r\n\t\t\tfor (var j = 0, callsLength = Velocity.State.calls.length; j < callsLength; j++) {\r\n\t\t\t\tif (Velocity.State.calls[j] !== false) {\r\n\t\t\t\t\tremainingCallsExist = true;\r\n\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tif (remainingCallsExist === false) {\r\n\t\t\t\t/* tick() will detect this flag upon its next iteration and subsequently turn itself off. */\r\n\t\t\t\tVelocity.State.isTicking = false;\r\n\r\n\t\t\t\t/* Clear the calls array so that its length is reset. */\r\n\t\t\t\tdelete Velocity.State.calls;\r\n\t\t\t\tVelocity.State.calls = [];\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/******************\r\n\t\t Frameworks\r\n\t\t ******************/\r\n\r\n\t\t/* Both jQuery and Zepto allow their $.fn object to be extended to allow wrapped elements to be subjected to plugin calls.\r\n\t\t If either framework is loaded, register a "velocity" extension pointing to Velocity\'s core animate() method.  Velocity\r\n\t\t also registers itself onto a global container (window.jQuery || window.Zepto || window) so that certain features are\r\n\t\t accessible beyond just a per-element scope. This master object contains an .animate() method, which is later assigned to $.fn\r\n\t\t (if jQuery or Zepto are present). Accordingly, Velocity can both act on wrapped DOM elements and stand alone for targeting raw DOM elements. */\r\n\t\tglobal.Velocity = Velocity;\r\n\r\n\t\tif (global !== window) {\r\n\t\t\t/* Assign the element function to Velocity\'s core animate() method. */\r\n\t\t\tglobal.fn.velocity = animate;\r\n\t\t\t/* Assign the object function\'s defaults to Velocity\'s global defaults object. */\r\n\t\t\tglobal.fn.velocity.defaults = Velocity.defaults;\r\n\t\t}\r\n\r\n\t\t/***********************\r\n\t\t Packaged Redirects\r\n\t\t ***********************/\r\n\r\n\t\t/* slideUp, slideDown */\r\n\t\t$.each(["Down", "Up"], function(i, direction) {\r\n\t\t\tVelocity.Redirects["slide" + direction] = function(element, options, elementsIndex, elementsSize, elements, promiseData) {\r\n\t\t\t\tvar opts = $.extend({}, options),\r\n\t\t\t\t\t\tbegin = opts.begin,\r\n\t\t\t\t\t\tcomplete = opts.complete,\r\n\t\t\t\t\t\tinlineValues = {},\r\n\t\t\t\t\t\tcomputedValues = {height: "", marginTop: "", marginBottom: "", paddingTop: "", paddingBottom: ""};\r\n\r\n\t\t\t\tif (opts.display === undefined) {\r\n\t\t\t\t\t/* Show the element before slideDown begins and hide the element after slideUp completes. */\r\n\t\t\t\t\t/* Note: Inline elements cannot have dimensions animated, so they\'re reverted to inline-block. */\r\n\t\t\t\t\topts.display = (direction === "Down" ? (Velocity.CSS.Values.getDisplayType(element) === "inline" ? "inline-block" : "block") : "none");\r\n\t\t\t\t}\r\n\r\n\t\t\t\topts.begin = function() {\r\n\t\t\t\t\t/* If the user passed in a begin callback, fire it now. */\r\n\t\t\t\t\tif (elementsIndex === 0 && begin) {\r\n\t\t\t\t\t\tbegin.call(elements, elements);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* Cache the elements\' original vertical dimensional property values so that we can animate back to them. */\r\n\t\t\t\t\tfor (var property in computedValues) {\r\n\t\t\t\t\t\tif (!computedValues.hasOwnProperty(property)) {\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tinlineValues[property] = element.style[property];\r\n\r\n\t\t\t\t\t\t/* For slideDown, use forcefeeding to animate all vertical properties from 0. For slideUp,\r\n\t\t\t\t\t\t use forcefeeding to start from computed values and animate down to 0. */\r\n\t\t\t\t\t\tvar propertyValue = CSS.getPropertyValue(element, property);\r\n\t\t\t\t\t\tcomputedValues[property] = (direction === "Down") ? [propertyValue, 0] : [0, propertyValue];\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* Force vertical overflow content to clip so that sliding works as expected. */\r\n\t\t\t\t\tinlineValues.overflow = element.style.overflow;\r\n\t\t\t\t\telement.style.overflow = "hidden";\r\n\t\t\t\t};\r\n\r\n\t\t\t\topts.complete = function() {\r\n\t\t\t\t\t/* Reset element to its pre-slide inline values once its slide animation is complete. */\r\n\t\t\t\t\tfor (var property in inlineValues) {\r\n\t\t\t\t\t\tif (inlineValues.hasOwnProperty(property)) {\r\n\t\t\t\t\t\t\telement.style[property] = inlineValues[property];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/* If the user passed in a complete callback, fire it now. */\r\n\t\t\t\t\tif (elementsIndex === elementsSize - 1) {\r\n\t\t\t\t\t\tif (complete) {\r\n\t\t\t\t\t\t\tcomplete.call(elements, elements);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (promiseData) {\r\n\t\t\t\t\t\t\tpromiseData.resolver(elements);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\r\n\t\t\t\tVelocity(element, computedValues, opts);\r\n\t\t\t};\r\n\t\t});\r\n\r\n\t\t/* fadeIn, fadeOut */\r\n\t\t$.each(["In", "Out"], function(i, direction) {\r\n\t\t\tVelocity.Redirects["fade" + direction] = function(element, options, elementsIndex, elementsSize, elements, promiseData) {\r\n\t\t\t\tvar opts = $.extend({}, options),\r\n\t\t\t\t\t\tcomplete = opts.complete,\r\n\t\t\t\t\t\tpropertiesMap = {opacity: (direction === "In") ? 1 : 0};\r\n\r\n\t\t\t\t/* Since redirects are triggered individually for each element in the animated set, avoid repeatedly triggering\r\n\t\t\t\t callbacks by firing them only when the final element has been reached. */\r\n\t\t\t\tif (elementsIndex !== 0) {\r\n\t\t\t\t\topts.begin = null;\r\n\t\t\t\t}\r\n\t\t\t\tif (elementsIndex !== elementsSize - 1) {\r\n\t\t\t\t\topts.complete = null;\r\n\t\t\t\t} else {\r\n\t\t\t\t\topts.complete = function() {\r\n\t\t\t\t\t\tif (complete) {\r\n\t\t\t\t\t\t\tcomplete.call(elements, elements);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (promiseData) {\r\n\t\t\t\t\t\t\tpromiseData.resolver(elements);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* If a display was passed in, use it. Otherwise, default to "none" for fadeOut or the element-specific default for fadeIn. */\r\n\t\t\t\t/* Note: We allow users to pass in "null" to skip display setting altogether. */\r\n\t\t\t\tif (opts.display === undefined) {\r\n\t\t\t\t\topts.display = (direction === "In" ? "auto" : "none");\r\n\t\t\t\t}\r\n\r\n\t\t\t\tVelocity(this, propertiesMap, opts);\r\n\t\t\t};\r\n\t\t});\r\n\r\n\t\treturn Velocity;\r\n\t}((window.jQuery || window.Zepto || window), window, (window ? window.document : undefined));\r\n}));\r\n\r\n/******************\r\n Known Issues\r\n ******************/\r\n\r\n/* The CSS spec mandates that the translateX/Y/Z transforms are %-relative to the element itself -- not its parent.\r\n Velocity, however, doesn\'t make this distinction. Thus, converting to or from the % unit with these subproperties\r\n will produce an inaccurate conversion value. The same issue exists with the cx/cy attributes of SVG circles and ellipses. */\r\n\n\n//# sourceURL=webpack://HsNavScroller/./node_modules/velocity-animate/velocity.js?')},"./src/js/hs-nav-scroller.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return HsNavScroller; });\n/* harmony import */ var velocity_animate__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! velocity-animate */ \"./node_modules/velocity-animate/velocity.js\");\n/* harmony import */ var velocity_animate__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(velocity_animate__WEBPACK_IMPORTED_MODULE_0__);\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n/*\n* HSNavScroller Plugin\n* @version: 2.0.0 (Sat, 06 Jul 2021)\n* @requires: Velocity 1.5.2 or later\n* @author: HtmlStream\n* @license: Htmlstream Libraries (https://htmlstream.com/)\n* Copyright 2021 Htmlstream\n*/\n\nvar dataAttributeName = 'data-hs-nav-scroller-options';\nvar defaults = {\n  type: 'horizontal',\n  target: '.active',\n  offset: 0,\n  delay: 20\n};\n\nvar HsNavScroller = /*#__PURE__*/function () {\n  function HsNavScroller(el, options, id) {\n    _classCallCheck(this, HsNavScroller);\n\n    this.collection = [];\n    var that = this;\n    var elems;\n\n    if (el instanceof HTMLElement) {\n      elems = [el];\n    } else if (el instanceof Object) {\n      elems = el;\n    } else {\n      elems = document.querySelectorAll(el);\n    }\n\n    for (var i = 0; i < elems.length; i += 1) {\n      that.addToCollection(elems[i], options, id || elems[i].id);\n    }\n\n    if (!that.collection.length) {\n      return false;\n    } // initialization calls\n\n\n    that._init();\n\n    return this;\n  }\n\n  _createClass(HsNavScroller, [{\n    key: \"_init\",\n    value: function _init() {\n      var that = this;\n\n      var _loop = function _loop(i) {\n        var _$el = void 0;\n\n        var _options = void 0;\n\n        if (that.collection[i].hasOwnProperty('$initializedEl')) {\n          return \"continue\";\n        }\n\n        _$el = that.collection[i].$el;\n        _options = that.collection[i].options;\n\n        if (_options.type == 'vertical') {\n          velocity_animate__WEBPACK_IMPORTED_MODULE_0___default()(_$el, 'scroll', {\n            container: _$el,\n            offset: _$el.querySelector(_options.target).offsetTop - _options.offset,\n            duration: _options.delay,\n            axis: 'y'\n          });\n        } else if (_options.type == 'horizontal') {\n          _options.nav = _$el.querySelector('.nav');\n          _options.prev = _$el.querySelector('.hs-nav-scroller-arrow-prev');\n          _options.next = _$el.querySelector('.hs-nav-scroller-arrow-next');\n          _options.activeElementLeftPosition = _options.nav.querySelector(_options.target).offsetLeft;\n          _options.scrollMaxLeft = parseInt((_options.nav.scrollWidth.toFixed() - _options.nav.clientWidth).toFixed());\n          _options.scrollPosition = _options.nav.scrollLeft;\n\n          if (_options.scrollPosition <= 0) {\n            _options.prev.style.display = 'none';\n          }\n\n          if (_options.scrollMaxLeft <= 0) {\n            _options.next.style.display = 'none';\n          }\n\n          that.onResize(_$el, _options);\n          window.addEventListener('resize', function () {\n            return that.onResize(_$el, _options);\n          });\n\n          var navRect = _options.nav.getBoundingClientRect(),\n              prevRect = _options.prev.getBoundingClientRect(),\n              nextRect = _options.next.getBoundingClientRect();\n\n          if (_options.activeElementLeftPosition > navRect.width / 2) {\n            velocity_animate__WEBPACK_IMPORTED_MODULE_0___default()(_options.nav, 'scroll', {\n              container: _options.nav,\n              offset: _options.activeElementLeftPosition - _options.offset - prevRect.width,\n              duration: _options.delay,\n              axis: 'x'\n            });\n          }\n\n          _options.next.addEventListener('click', function () {\n            velocity_animate__WEBPACK_IMPORTED_MODULE_0___default()(_options.nav, 'scroll', {\n              container: _options.nav,\n              offset: _options.scrollPosition + _options.nav.clientWidth - nextRect.width,\n              duration: _options.delay,\n              axis: 'x'\n            });\n          });\n\n          _options.prev.addEventListener('click', function () {\n            velocity_animate__WEBPACK_IMPORTED_MODULE_0___default()(_options.nav, 'scroll', {\n              container: _options.nav,\n              offset: _options.scrollPosition - _options.nav.clientWidth + prevRect.width,\n              duration: _options.delay,\n              axis: 'x'\n            });\n          });\n\n          _options.nav.addEventListener('scroll', function () {\n            var scrollMaxLeft = (parseInt(_options.nav.scrollWidth.toFixed()) - parseInt(_options.nav.clientWidth)).toFixed(),\n                scrollPosition = _options.nav.scrollLeft; // Hide or Show Back Arrow\n\n            if (scrollPosition <= 0) {\n              _options.prev.style.display = 'none';\n            } else {\n              _options.prev.style.display = 'flex';\n            } // Hide or Show Next Arrow\n\n\n            if (scrollPosition >= scrollMaxLeft) {\n              _options.next.style.display = 'none';\n            } else {\n              _options.next.style.display = 'flex';\n            }\n          });\n        }\n      };\n\n      for (var i = 0; i < that.collection.length; i += 1) {\n        var _ret = _loop(i);\n\n        if (_ret === \"continue\") continue;\n      }\n    }\n  }, {\n    key: \"onResize\",\n    value: function onResize($el, settings) {\n      var scrollMaxLeft = parseInt(settings.nav.scrollWidth.toFixed()) - parseInt(settings.nav.clientWidth.toFixed()),\n          scrollPosition = settings.nav.scrollLeft;\n\n      if (scrollPosition <= 0) {\n        settings.prev.style.display = 'none';\n      } else {\n        settings.prev.style.display = 'flex';\n      }\n\n      if (scrollMaxLeft <= 0) {\n        settings.next.style.display = 'none';\n      } else {\n        settings.next.style.display = 'flex';\n      }\n    }\n  }, {\n    key: \"addToCollection\",\n    value: function addToCollection(item, options, id) {\n      this.collection.push({\n        $el: item,\n        id: id || null,\n        options: Object.assign({}, defaults, item.hasAttribute(dataAttributeName) ? JSON.parse(item.getAttribute(dataAttributeName)) : {}, options)\n      });\n    }\n  }, {\n    key: \"getItem\",\n    value: function getItem(item) {\n      if (typeof item === 'number') {\n        return this.collection[item].$initializedEl;\n      } else {\n        return this.collection.find(function (el) {\n          return el.id === item;\n        }).$initializedEl;\n      }\n    }\n  }]);\n\n  return HsNavScroller;\n}();\n\n\n\n//# sourceURL=webpack://HsNavScroller/./src/js/hs-nav-scroller.js?")}},e={},f.m=d,f.c=e,f.d=function(t,e,r){f.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},f.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},f.t=function(e,t){if(1&t&&(e=f(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(f.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)f.d(r,n,function(t){return e[t]}.bind(null,n));return r},f.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return f.d(e,"a",e),e},f.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},f.p="",f(f.s="./src/js/hs-nav-scroller.js").default;function f(t){if(e[t])return e[t].exports;var r=e[t]={i:t,l:!1,exports:{}};return d[t].call(r.exports,r,r.exports,f),r.l=!0,r.exports}var d,e});