!function(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.HSToggleSwitch=n():t.HSToggleSwitch=n()}(window,function(){return d={"./node_modules/countup.js/dist/countUp.min.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "CountUp", function() { return CountUp; });\nvar __assign=undefined&&undefined.__assign||function(){return(__assign=Object.assign||function(t){for(var i,a=1,s=arguments.length;a<s;a++)for(var n in i=arguments[a])Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n]);return t}).apply(this,arguments)},CountUp=function(){function t(t,i,a){var s=this;this.target=t,this.endVal=i,this.options=a,this.version="2.0.7",this.defaults={startVal:0,decimalPlaces:0,duration:2,useEasing:!0,useGrouping:!0,smartEasingThreshold:999,smartEasingAmount:333,separator:",",decimal:".",prefix:"",suffix:""},this.finalEndVal=null,this.useEasing=!0,this.countDown=!1,this.error="",this.startVal=0,this.paused=!0,this.count=function(t){s.startTime||(s.startTime=t);var i=t-s.startTime;s.remaining=s.duration-i,s.useEasing?s.countDown?s.frameVal=s.startVal-s.easingFn(i,0,s.startVal-s.endVal,s.duration):s.frameVal=s.easingFn(i,s.startVal,s.endVal-s.startVal,s.duration):s.countDown?s.frameVal=s.startVal-(s.startVal-s.endVal)*(i/s.duration):s.frameVal=s.startVal+(s.endVal-s.startVal)*(i/s.duration),s.countDown?s.frameVal=s.frameVal<s.endVal?s.endVal:s.frameVal:s.frameVal=s.frameVal>s.endVal?s.endVal:s.frameVal,s.frameVal=Number(s.frameVal.toFixed(s.options.decimalPlaces)),s.printValue(s.frameVal),i<s.duration?s.rAF=requestAnimationFrame(s.count):null!==s.finalEndVal?s.update(s.finalEndVal):s.callback&&s.callback()},this.formatNumber=function(t){var i,a,n,e,r,o=t<0?"-":"";if(i=Math.abs(t).toFixed(s.options.decimalPlaces),n=(a=(i+="").split("."))[0],e=a.length>1?s.options.decimal+a[1]:"",s.options.useGrouping){r="";for(var l=0,h=n.length;l<h;++l)0!==l&&l%3==0&&(r=s.options.separator+r),r=n[h-l-1]+r;n=r}return s.options.numerals&&s.options.numerals.length&&(n=n.replace(/[0-9]/g,function(t){return s.options.numerals[+t]}),e=e.replace(/[0-9]/g,function(t){return s.options.numerals[+t]})),o+s.options.prefix+n+e+s.options.suffix},this.easeOutExpo=function(t,i,a,s){return a*(1-Math.pow(2,-10*t/s))*1024/1023+i},this.options=__assign(__assign({},this.defaults),a),this.formattingFn=this.options.formattingFn?this.options.formattingFn:this.formatNumber,this.easingFn=this.options.easingFn?this.options.easingFn:this.easeOutExpo,this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.endVal=this.validateValue(i),this.options.decimalPlaces=Math.max(this.options.decimalPlaces),this.resetDuration(),this.options.separator=String(this.options.separator),this.useEasing=this.options.useEasing,""===this.options.separator&&(this.options.useGrouping=!1),this.el="string"==typeof t?document.getElementById(t):t,this.el?this.printValue(this.startVal):this.error="[CountUp] target is null or undefined"}return t.prototype.determineDirectionAndSmartEasing=function(){var t=this.finalEndVal?this.finalEndVal:this.endVal;this.countDown=this.startVal>t;var i=t-this.startVal;if(Math.abs(i)>this.options.smartEasingThreshold){this.finalEndVal=t;var a=this.countDown?1:-1;this.endVal=t+a*this.options.smartEasingAmount,this.duration=this.duration/2}else this.endVal=t,this.finalEndVal=null;this.finalEndVal?this.useEasing=!1:this.useEasing=this.options.useEasing},t.prototype.start=function(t){this.error||(this.callback=t,this.duration>0?(this.determineDirectionAndSmartEasing(),this.paused=!1,this.rAF=requestAnimationFrame(this.count)):this.printValue(this.endVal))},t.prototype.pauseResume=function(){this.paused?(this.startTime=null,this.duration=this.remaining,this.startVal=this.frameVal,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count)):cancelAnimationFrame(this.rAF),this.paused=!this.paused},t.prototype.reset=function(){cancelAnimationFrame(this.rAF),this.paused=!0,this.resetDuration(),this.startVal=this.validateValue(this.options.startVal),this.frameVal=this.startVal,this.printValue(this.startVal)},t.prototype.update=function(t){cancelAnimationFrame(this.rAF),this.startTime=null,this.endVal=this.validateValue(t),this.endVal!==this.frameVal&&(this.startVal=this.frameVal,this.finalEndVal||this.resetDuration(),this.finalEndVal=null,this.determineDirectionAndSmartEasing(),this.rAF=requestAnimationFrame(this.count))},t.prototype.printValue=function(t){var i=this.formattingFn(t);"INPUT"===this.el.tagName?this.el.value=i:"text"===this.el.tagName||"tspan"===this.el.tagName?this.el.textContent=i:this.el.innerHTML=i},t.prototype.ensureNumber=function(t){return"number"==typeof t&&!isNaN(t)},t.prototype.validateValue=function(t){var i=Number(t);return this.ensureNumber(i)?i:(this.error="[CountUp] invalid start or end value: "+t,null)},t.prototype.resetDuration=function(){this.startTime=null,this.duration=1e3*Number(this.options.duration),this.remaining=this.duration},t}();\n\n//# sourceURL=webpack://HSToggleSwitch/./node_modules/countup.js/dist/countUp.min.js?')},"./src/js/hs-toggle-switch.js":function(module,__webpack_exports__,__webpack_require__){"use strict";eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return HSToggleSwitch; });\n/* harmony import */ var countup_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! countup.js */ "./node_modules/countup.js/dist/countUp.min.js");\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n/*\n* HSToggleSwitch Plugin\n* @version: 1.0.0 (Mon, 12 Dec 2019)\n* @requires: countup.js v2.0.4\n* @author: HtmlStream\n* @event-namespace: .HSToggleSwitch\n* @license: Htmlstream Libraries (https://htmlstream.com/)\n* Copyright 2019 Htmlstream\n*/\n\nvar dataAttributeName = \'data-hs-toggle-switch-options\';\nvar dataAttributeItemName = \'data-hs-toggle-switch-item-options\';\nvar defaults = {\n  mode: \'toggle-count\',\n  targetSelector: undefined,\n  isChecked: false,\n  eventType: \'change\'\n};\n\nvar HSToggleSwitch = /*#__PURE__*/function () {\n  function HSToggleSwitch(el, options, id) {\n    _classCallCheck(this, HSToggleSwitch);\n\n    this.collection = [];\n    var that = this;\n    var elems;\n\n    if (el instanceof HTMLElement) {\n      elems = [el];\n    } else if (el instanceof Object) {\n      elems = el;\n    } else {\n      elems = document.querySelectorAll(el);\n    }\n\n    for (var i = 0; i < elems.length; i += 1) {\n      that.addToCollection(elems[i], options, id || elems[i].id);\n    }\n\n    if (!that.collection.length) {\n      return false;\n    } // initialization calls\n\n\n    that._init();\n\n    return this;\n  }\n\n  _createClass(HSToggleSwitch, [{\n    key: "_init",\n    value: function _init() {\n      var that = this;\n\n      var _loop = function _loop(i) {\n        var _$el = void 0;\n\n        var _options = void 0;\n\n        if (that.collection[i].hasOwnProperty(\'$initializedEl\')) {\n          return "continue";\n        }\n\n        _$el = that.collection[i].$el;\n        _options = that.collection[i].options;\n        _options.isChecked = _$el.checked;\n        _options.$targets = document.querySelectorAll(_options.targetSelector);\n\n        if (_options.mode === \'toggle-count\') {\n          if (_options.isChecked) {\n            _options.isChecked = true;\n\n            _options.$targets.forEach(function ($target) {\n              var currentDataSettings = $target.hasAttribute(dataAttributeItemName) ? JSON.parse($target.getAttribute(dataAttributeItemName)) : {};\n              $target.innerHTML = currentDataSettings.max;\n            });\n          }\n\n          _$el.addEventListener(_options.eventType, function () {\n            return that._toggleCount(_options);\n          });\n        }\n      };\n\n      for (var i = 0; i < that.collection.length; i += 1) {\n        var _ret = _loop(i);\n\n        if (_ret === "continue") continue;\n      }\n    } // Toggle Count\n\n  }, {\n    key: "_toggleCount",\n    value: function _toggleCount(settings) {\n      if (settings.isChecked) {\n        this._countDownEach(settings);\n      } else {\n        this._countUpEach(settings);\n      }\n    }\n  }, {\n    key: "_countUpEach",\n    value: function _countUpEach(settings) {\n      var _this = this;\n\n      settings.isChecked = true;\n      settings.$targets.forEach(function ($target) {\n        var currentDataSettings = $target.hasAttribute(dataAttributeItemName) ? JSON.parse($target.getAttribute(dataAttributeItemName)) : {};\n        var currentDefaults = {\n          duration: .5,\n          useEasing: false\n        },\n            currentOptions = {};\n        currentOptions = Object.assign({}, currentDefaults, currentDataSettings);\n\n        _this._countUp($target, currentOptions);\n      });\n    }\n  }, {\n    key: "_countDownEach",\n    value: function _countDownEach(settings) {\n      var _this2 = this;\n\n      settings.isChecked = false;\n      settings.$targets.forEach(function ($target) {\n        var currentDataSettings = $target.hasAttribute(dataAttributeItemName) ? JSON.parse($target.getAttribute(dataAttributeItemName)) : {};\n        var currentDefaults = {\n          duration: .5,\n          useEasing: false\n        },\n            currentOptions = {};\n        currentOptions = Object.assign({}, currentDefaults, currentDataSettings);\n\n        _this2._countDown($target, currentOptions);\n      });\n    }\n  }, {\n    key: "_countUp",\n    value: function _countUp(el, data) {\n      var defaults = {\n        startVal: data.min\n      };\n      var options = Object.assign({}, defaults, data);\n      var countUp = new countup_js__WEBPACK_IMPORTED_MODULE_0__["CountUp"](el, data.max, options);\n      countUp.start();\n    }\n  }, {\n    key: "_countDown",\n    value: function _countDown(el, data) {\n      var defaults = {\n        startVal: data.max\n      };\n      var options = Object.assign({}, defaults, data);\n      var countUp = new countup_js__WEBPACK_IMPORTED_MODULE_0__["CountUp"](el, data.min, options);\n      countUp.start();\n    }\n  }, {\n    key: "addToCollection",\n    value: function addToCollection(item, options, id) {\n      this.collection.push({\n        $el: item,\n        id: id || null,\n        options: Object.assign({}, defaults, item.hasAttribute(dataAttributeName) ? JSON.parse(item.getAttribute(dataAttributeName)) : {}, options)\n      });\n    }\n  }, {\n    key: "getItem",\n    value: function getItem(item) {\n      if (typeof item === \'number\') {\n        return this.collection[item].$initializedEl;\n      } else {\n        return this.collection.find(function (el) {\n          return el.id === item;\n        }).$initializedEl;\n      }\n    }\n  }]);\n\n  return HSToggleSwitch;\n}();\n\n\n\n//# sourceURL=webpack://HSToggleSwitch/./src/js/hs-toggle-switch.js?')}},e={},f.m=d,f.c=e,f.d=function(t,n,e){f.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:e})},f.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},f.t=function(n,t){if(1&t&&(n=f(n)),8&t)return n;if(4&t&&"object"==typeof n&&n&&n.__esModule)return n;var e=Object.create(null);if(f.r(e),Object.defineProperty(e,"default",{enumerable:!0,value:n}),2&t&&"string"!=typeof n)for(var i in n)f.d(e,i,function(t){return n[t]}.bind(null,i));return e},f.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return f.d(n,"a",n),n},f.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},f.p="",f(f.s="./src/js/hs-toggle-switch.js").default;function f(t){if(e[t])return e[t].exports;var n=e[t]={i:t,l:!1,exports:{}};return d[t].call(n.exports,n,n.exports,f),n.l=!0,n.exports}var d,e});