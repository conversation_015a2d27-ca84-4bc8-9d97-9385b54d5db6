<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.name') }} - Authorization Page</title>

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="{{ asset('vendor/bootstrap-icons/font/bootstrap-icons.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/hs-mega-menu/dist/hs-mega-menu.min.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/aos/dist/aos.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/swiper/swiper-bundle.min.css') }}">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="{{ asset('assets/css/theme.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/snippets.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/auth/authorization.css') }}">
</head>

<body>
    <div class="auth-container">
        <div class="auth-box">
            <div class="text-center mb-5 mb-md-7">
                <img src="{{ asset('assets/img/logo-dual-dark.png') }}" width="70%" alt="">
                <h1 class="h4 mt-6">Authorization Request</h1>
                <p><b>{{ auth()->user()->name }}</b> is requesting permission to access your account.</p>
            </div>
            <div class="buttons">
                <!-- Authorize Button -->
                <form method="post" action="{{ route('passport.authorizations.approve') }}">
                    @csrf

                    <input type="hidden" name="state" value="{{ $request->state }}">
                    <input type="hidden" name="client_id" value="{{ $client->getKey() }}">
                    <input type="hidden" name="auth_token" value="{{ $authToken }}">
                    <button type="submit" class="btn btn-success">Authorize</button>
                </form>

                <!-- Cancel Button -->
                <form method="post" action="{{ route('passport.authorizations.deny') }}">
                    @csrf
                    @method('DELETE')

                    <input type="hidden" name="state" value="{{ $request->state }}">
                    <input type="hidden" name="client_id" value="{{ $client->getKey() }}">
                    <input type="hidden" name="auth_token" value="{{ $authToken }}">
                    <button class="btn btn-danger">Cancel</button>
                </form>
            </div>

        </div>
    </div>
</body>

</html>
