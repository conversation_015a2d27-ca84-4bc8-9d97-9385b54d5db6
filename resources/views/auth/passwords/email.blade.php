<!DOCTYPE html>
<html lang="en" dir="">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="title" content="Sign in to GravityWrite; Your AI Writing Assistant">
    <meta name="description"
        content="Log in to GravityWrite account and unlock a world of seamless writing experience. Access your drafts, projects, and templates effortlessly. Start writing smarter with GravityWrite today!">
    <!-- Title -->
    <title>GravityWrite | Unleash Your Writing Potential</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/img/favicon.svg') }}">

    <!-- Font -->
    <link rel="stylesheet" href="{{ asset('vendor/bootstrap-icons/font/bootstrap-icons.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="https://app2.gravitywrite.com/vendor/bootstrap-icons/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ asset('vendor/hs-mega-menu/dist/hs-mega-menu.min.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/aos/dist/aos.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/swiper/swiper-bundle.min.css') }}">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="{{ asset('assets/css/theme.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/snippets.min.css') }}">
    <!-- Google Tag Manager -->
    {{-- <script>
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-NJSDRXR');
    </script> --}}
    <!-- End Google Tag Manager -->
    {{-- <script>
        (function(w,r){w._rwq=r;w[r]=w[r]||function(){(w[r].q=w[r].q||[]).push(arguments)}})(window,'rewardful');
    </script>
    <script async src='https://r.wdfl.co/rw.js' data-rewardful='b310e9'></script>
    <script src="https://www.google.com/recaptcha/api.js?render=6LdlkMAqAAAAABA-7qedWPpozkPa3RlQshqvmt2i"></script> --}}
    <style type="text/css">
        .form-label {
            font-size: 1rem !important;
        }
    </style>
</head>

<body>

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main">
        <!-- Form -->
        <div class="container content-space-3 content-space-t-lg-4 content-space-b-lg-3">
            <div class="flex-grow-1 mx-auto" style="max-width: 28rem;">
                <!-- Heading -->
                <div class="text-center mb-5 mb-md-7">
                    <div class="text-center mb-5 mb-md-7">
                        <img src="https://app2.gravitywrite.com//storage/17/6414225450e31_PNG---GW-horizontal-logo-dual-dark.png"
                            width="45%" alt="">
                        </br>
                        </br>
                        </br>
                        </br>
                        <h1 class="h2">Forgot password?</h1>
                        <p>Enter your email address below and we&#039;ll get you back on track.</p>
                    </div>
                    <!-- End Heading -->

                    <!-- Form -->
                    <form method="POST" id="passwordEmail" action="{{ route('password.email') }}"
                        class="js-validate needs-validation" novalidate>
                        @csrf
                        <!-- Form -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <label class="form-label" for="signupSimpleResetPasswordEmail" tabindex="0">Email Address</label>

                                <a class="form-label-link" href="{{ route('login') }}">
                                    <i class="bi-chevron-left small ms-1"></i> Back to Log in
                                </a>
                            </div>

                            <input type="email" class="form-control form-control-lg" name="email"
                                id="signupSimpleResetPasswordEmail" tabindex="1" placeholder="<EMAIL>"
                                aria-label="Enter your email address" required>

                            @error('email')
                                <span class="invalid-feedback text-start" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                            <span class="invalid-feedback text-start">Please enter a valid email address.</span>
                        </div>
                        <!-- End Form -->

                        <div class="mb-3"><small class="text-danger"></small></div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">Submit</button>
                        </div>
                        <div class="text-center">
                            <p>Having trouble? <a class="" target="_blank"
                                    href="https://gravitywrite.com/contact-us">Contact us!</a>
                            </p>
                        </div>
                    </form>
                    <!-- End Form -->
                </div>
            </div>
            <!-- End Form -->
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== SECONDARY CONTENTS ========== -->
    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="{{ asset('vendor/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>

    <!-- JS Implementing Plugins -->
    <script src="{{ asset('vendor/hs-header/dist/hs-header.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-mega-menu/dist/hs-mega-menu.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-show-animation/dist/hs-show-animation.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-go-to/dist/hs-go-to.min.js') }}"></script>
    <script src="{{ asset('vendor/aos/dist/aos.js') }}"></script>
    <script src="{{ asset('vendor/swiper/swiper-bundle.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-toggle-switch/dist/hs-toggle-switch.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-toggle-password/dist/hs-toggle-password.js') }}"></script>

    <!-- JS Front -->
    <script src="{{ asset('assets/js/theme.min.js') }}"></script>

    <!-- JS Plugins Init. -->
    <script>
        (function() {

            // INITIALIZATION OF MEGA MENU
            // =======================================================
            new HSMegaMenu('.js-mega-menu', {
                desktop: {
                    position: 'left'
                }
            })


            // INITIALIZATION OF SHOW ANIMATIONS
            // =======================================================
            new HSShowAnimation('.js-animation-link')


            // INITIALIZATION OF BOOTSTRAP VALIDATION
            // =======================================================
            HSBsValidation.init('.js-validate')


            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')


            // INITIALIZATION OF TOGGLE PASSWORD
            // =======================================================
            new HSTogglePassword('.js-toggle-password')

            // INITIALIZATION OF AOS
            // =======================================================
            AOS.init({
                duration: 650,
                once: true
            });

            // INITIALIZATION OF TOGGLE SWITCH
            // =======================================================
            new HSToggleSwitch('.js-toggle-switch')

            // INITIALIZATION OF SWIPER
            // =======================================================
            var swiper = new Swiper('.js-swiper-hero-clients',{
                slidesPerView: 2,
                breakpoints: {
                    380: {
                        slidesPerView: 3,
                        spaceBetween: 15,
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 15,
                    },
                    1024: {
                        slidesPerView: 5,
                        spaceBetween: 15,
                    },
                },
            });


            // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller')

            // INITIALIZATION OF BOOTSTRAP DROPDOWN
            // =======================================================
            HSBsDropdown.init()
        })()
    </script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            var title='Forgot Password - GravityWrite';
            document.title = title;
        });
        // $('#passwordEmail').submit(function(event) {
        //     event.preventDefault();
        //     grecaptcha.ready(function() {
        //         grecaptcha.execute("6LdlkMAqAAAAABA-7qedWPpozkPa3RlQshqvmt2i", {action: 'subscribe_newsletter'}).then(function(token) {
        //             $('#passwordEmail').prepend('<input type="hidden" name="g-recaptcha-response" value="' + token + '">');
        //             $('#passwordEmail').unbind('submit').submit();
        //         });;
        //     });
        // });
    </script>
</body>

</html>
