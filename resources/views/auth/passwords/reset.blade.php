<!DOCTYPE html>
<html lang="en" dir="">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="title" content="Sign in to GravityWrite; Your AI Writing Assistant">
    <meta name="description"
        content="Log in to GravityWrite account and unlock a world of seamless writing experience. Access your drafts, projects, and templates effortlessly. Start writing smarter with GravityWrite today!">
    <!-- Title -->
    <title>GravityWrite | Unleash Your Writing Potential</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/img/favicon.svg') }}">

    <!-- Font -->
    <link rel="stylesheet" href="{{ asset('vendor/bootstrap-icons/font/bootstrap-icons.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="https://app2.gravitywrite.com/vendor/bootstrap-icons/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ asset('vendor/hs-mega-menu/dist/hs-mega-menu.min.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/aos/dist/aos.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/swiper/swiper-bundle.min.css') }}">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="{{ asset('assets/css/theme.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/snippets.min.css') }}">
    <style type="text/css">
        .form-label {
            font-size: 1rem !important;
        }
    </style>
</head>

<body>


    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main">
        <!-- Form -->
        <div class="container content-space-3 content-space-t-lg-4 content-space-b-lg-3">
            <div class="flex-grow-1 mx-auto" style="max-width: 28rem;">
                <!-- Heading -->
                <div class="text-center mb-5 mb-md-7">
                    <h1 class="h2">Reset Password</h1>
                </div>
                <!-- End Heading -->
                @if(session()->has('message'))
                <p class="alert text-black"
                    style="background-color: #F2C85C; border-color: #F5F6FE; border-width: 1px;">
                    {{ session()->get('message') }}
                </p>
                @endif
                @if (session('status'))
                <p class="alert text-black"
                    style="    background-color: #F2C85C; border-color: #F5F6FE; border-width: 1px;">
                    Password Reset Link was successfully sent to your email address.
                </p>
                @endif
                <!-- Form -->
                <form method="POST" action="{{ route('password.request') }}">
                    @csrf

                    <input name="token" value="{{ $token }}" type="hidden">

                    <div>
                        <div class="form-group mb-4">
                            <input id="email" type="email"
                                class="form-control{{ $errors->has('email') ? ' is-invalid' : '' }}" name="email"
                                value="{{ $email ?? old('email') }}" required autocomplete="email" autofocus
                                placeholder="Your email">

                            @if($errors->has('email'))
                            <span class="text-danger">
                                {{ $errors->first('email') }}
                            </span>
                            @endif
                        </div>
                        <div class="form-group mb-4">
                            <div class="password-container">
                                <div class="password-input-wrapper">
                                    <input type="password" class="form-control form-control-lg password-input"
                                        name="password" id="password-input" placeholder="Create a strong password"
                                        aria-label="Create a strong password" required>
                                    <button type="button" id="toggle-password" class="password-toggle">
                                        <i class="bi-eye"></i>
                                    </button>
                                </div>

                                <div class="password-requirements">
                                    <div class="password-strength-text">Password Strength: Weak</div>
                                    <div class="password-strength-meter">
                                        <div class="password-strength-meter-bar strength-weak"></div>
                                    </div>

                                    <div class="requirement-item" data-requirement="uppercase">
                                        <span class="requirement-icon invalid"><i class="bi bi-x-circle"></i></span>
                                        <span class="requirement-text invalid">Contains an uppercase letter</span>
                                    </div>

                                    <div class="requirement-item" data-requirement="lowercase">
                                        <span class="requirement-icon invalid"><i class="bi bi-x-circle"></i></span>
                                        <span class="requirement-text invalid">Contains a lowercase letter</span>
                                    </div>

                                    <div class="requirement-item" data-requirement="number">
                                        <span class="requirement-icon invalid"><i class="bi bi-x-circle"></i></span>
                                        <span class="requirement-text invalid">Contains a number</span>
                                    </div>

                                    <div class="requirement-item" data-requirement="special">
                                        <span class="requirement-icon invalid"><i class="bi bi-x-circle"></i></span>
                                        <span class="requirement-text invalid">Contains a special character</span>
                                    </div>

                                    <div class="requirement-item" data-requirement="length">
                                        <span class="requirement-icon invalid"><i class="bi bi-x-circle"></i></span>
                                        <span class="requirement-text invalid">Is at least 8 characters long</span>
                                    </div>
                                </div>
                            </div>

                            @if($errors->has('password'))
                            <span class="text-danger">
                                {{ $errors->first('password') }}
                            </span>
                            @endif
                        </div>
                        <div class="form-group mb-4">
                            <div class="password-input-wrapper">
                                <input id="password-confirm" type="password" class="form-control form-control-lg password-input"
                                    name="password_confirmation" required
                                    placeholder="Confirm Password">
                            </div>
                        </div>
                    </div>
                    <div class="d-grid">
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-flat btn-block">
                                Reset Password
                            </button>
                        </div>
                    </div>
                </form>
                <!-- End Form -->
            </div>
        </div>
        <!-- End Form -->
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
        <!-- End Form -->
        <script type="text/javascript">
            $(document).ready(function() {
                var title = 'Reset Password - GravityWrite';
                document.title = title;

                // Check password confirmation match
                $('#password-confirm').on('input', function() {
                    const password = $('#password-input').val();
                    const confirmPassword = $(this).val();

                    if (password && confirmPassword && password !== confirmPassword) {
                        $(this).addClass('is-invalid');
                        if (!$(this).next('.invalid-feedback').length) {
                            $(this).after('<div class="invalid-feedback">Passwords do not match</div>');
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                        $(this).next('.invalid-feedback').remove();
                    }
                });

                // Form submission validation
                $("form").on("submit", function(event) {
                    // Check if all password requirements are met
                    const allRequirementsMet = $('.requirement-icon.valid').length === 5;
                    const password = $('#password-input').val();
                    const confirmPassword = $('#password-confirm').val();

                    if (!allRequirementsMet) {
                        event.preventDefault();
                        // Scroll to password field and focus
                        $('html, body').animate({
                            scrollTop: $('#password-input').offset().top - 100
                        }, 300);
                        $('#password-input').focus();
                    } else if (password !== confirmPassword) {
                        event.preventDefault();
                        $('#password-confirm').addClass('is-invalid');
                        if (!$('#password-confirm').next('.invalid-feedback').length) {
                            $('#password-confirm').after('<div class="invalid-feedback">Passwords do not match</div>');
                        }
                        // Scroll to confirm password field
                        $('html, body').animate({
                            scrollTop: $('#password-confirm').offset().top - 100
                        }, 300);
                        $('#password-confirm').focus();
                    }
                });
            });
        </script>

        <!-- Password Validation Script -->
        <link rel="stylesheet" href="{{ asset('assets/css/password-validation.css') }}">
        <script src="{{ asset('assets/js/password-validation.js') }}"></script>
    </main>
    <!-- End Form -->
    <!-- ========== END MAIN CONTENT ========== -->


    <!-- ========== SECONDARY CONTENTS ========== -->
    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;" data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="{{ asset('vendor/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>

    <!-- JS Implementing Plugins -->
    <script src="{{ asset('vendor/hs-header/dist/hs-header.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-mega-menu/dist/hs-mega-menu.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-show-animation/dist/hs-show-animation.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-go-to/dist/hs-go-to.min.js') }}"></script>
    <script src="{{ asset('vendor/aos/dist/aos.js') }}"></script>
    <script src="{{ asset('vendor/swiper/swiper-bundle.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-toggle-switch/dist/hs-toggle-switch.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-toggle-password/dist/hs-toggle-password.js') }}"></script>

    <!-- JS Front -->
    <script src="{{ asset('assets/js/theme.min.js') }}"></script>

    <!-- JS Plugins Init. -->
    <script>
        (function() {

            // INITIALIZATION OF MEGA MENU
            // =======================================================
            new HSMegaMenu('.js-mega-menu', {
                desktop: {
                    position: 'left'
                }
            })


            // INITIALIZATION OF SHOW ANIMATIONS
            // =======================================================
            new HSShowAnimation('.js-animation-link')


            // INITIALIZATION OF BOOTSTRAP VALIDATION
            // =======================================================
            HSBsValidation.init('.js-validate')


            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')


            // INITIALIZATION OF TOGGLE PASSWORD
            // =======================================================
            new HSTogglePassword('.js-toggle-password')

            // INITIALIZATION OF AOS
            // =======================================================
            AOS.init({
                duration: 650,
                once: true
            });

            // INITIALIZATION OF TOGGLE SWITCH
            // =======================================================
            new HSToggleSwitch('.js-toggle-switch')

            // INITIALIZATION OF SWIPER
            // =======================================================
            var swiper = new Swiper('.js-swiper-hero-clients',{
                slidesPerView: 2,
                breakpoints: {
                    380: {
                        slidesPerView: 3,
                        spaceBetween: 15,
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 15,
                    },
                    1024: {
                        slidesPerView: 5,
                        spaceBetween: 15,
                    },
                },
            });


            // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller')

            // INITIALIZATION OF BOOTSTRAP DROPDOWN
            // =======================================================
            HSBsDropdown.init()
        })()
    </script>
</body>

</html>
