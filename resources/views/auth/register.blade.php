<!DOCTYPE html>
<html lang="en" dir="">

<head>
    <!-- Required Meta Tags Always Come First -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="title" content="Sign in to GravityWrite; Your AI Writing Assistant">
    <meta name="description"
        content="Log in to GravityWrite account and unlock a world of seamless writing experience. Access your drafts, projects, and templates effortlessly. Start writing smarter with GravityWrite today!">
    <!-- Title -->
    <title>GravityWrite | Unleash Your Writing Potential</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/img/favicon.svg') }}">

    <!-- Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">

    <!-- CSS Implementing Plugins -->
    <link rel="stylesheet" href="{{ asset('vendor/bootstrap-icons/font/bootstrap-icons.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/hs-mega-menu/dist/hs-mega-menu.min.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/aos/dist/aos.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/swiper/swiper-bundle.min.css') }}">

    <!-- CSS Front Template -->
    <link rel="stylesheet" href="{{ asset('assets/css/theme.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/snippets.min.css') }}">

    <style type="text/css">
        .form-label {
            font-size: 1rem !important;
        }

        .autocomplete-off {
            position: fixed !important;
            opacity: 0 !important;
            top: -1000px !important;
            left: -1000px !important;
            height: 0 !important;
            width: 0 !important;
            border: 0 !important;
        }
        #content {
            min-height: 100vh;
        }
    </style>
</head>

<body>

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" role="main" class="align-content-center">
        <!-- Form -->
        <div class="container content-space-3 content-space-t-lg-4 content-space-b-lg-3">
            <div class="flex-grow-1 mx-auto" style="max-width: 29rem;">
                <!-- Heading -->
                <div class="text-center mb-5 mb-md-7">
                    <div class="text-center mb-5 mb-md-7">
                        <img src="{{ asset('assets/img/logo-dual-dark.png') }}" width="45%" alt="">
                        <h1 class="h2 mt-6">Welcome back</h1>
                        <p>Login to manage your account.</p>
                    </div>
                    <!-- End Heading -->

                    <!-- Form -->
                    <form method="POST" autocomplete="off" action="{{ url('/register') }}"
                        class="js-validate needs-validation" style="text-align: left;" novalidate>
                        @csrf

                        <!-- Form -->
                        <div class="text-center mb-4 bottom-img" style="margin-left: -13px;">
                            <a href="{{ route('passport.socialite.redirect', 'google') }}">
                                <img src="{{ asset('assets/img/auth/google_sign_in.svg') }}" alt="Gravity Write"
                                    style="display: flex;width: 489px;margin-bottom: -7px;">
                            </a>
                            <img class="mt-4 mb-2" src="{{ asset('assets/img/auth/dash_separator.svg') }}" alt="Gravity Write">
                        </div>

                        <div class="mb-3">
                            <label class="form-label" for="SimpleSignupName">Full Name <span class="text-danger">*</span></label>
                            <input type="name" class="form-control form-control-lg" name="name"
                                id="SimpleSignupName" placeholder="John Doe" aria-label="Enter your full name"
                                value="" required autofocus>
                            <span class="invalid-feedback">Please enter a valid name.</span>
                        </div>
                        <!-- End Form -->

                        <!-- Form -->
                        <div class="mb-3">
                            <label class="form-label" for="SimpleSignupEmail">Email Address <span class="text-danger">*</span></label>
                            <input type="email" class="form-control form-control-lg" autocomplete="false"
                                name="email" id="SimpleSignupEmail" placeholder="<EMAIL>"
                                aria-label="Enter your email address"
                                value="{{ old('email', $prefill_email ?? request()->email) }}" required>
                            <span class="invalid-feedback">Please enter a valid email address.</span>
                        </div>
                        <!-- End Form -->

                        <!-- Form -->
                        <div class="mb-3 password_hide">
                            <label class="form-label" for="password-input">Create Password <span class="text-danger">*</span></label>

                            <div class="password-container">
                                <div class="password-input-wrapper">
                                    <input type="password" class="form-control form-control-lg password-input"
                                        name="password" id="password-input" placeholder="Create a strong password (min 8 characters)"
                                        aria-label="Create a strong password" required>
                                    <button type="button" id="toggle-password" class="password-toggle">
                                        <i class="bi-eye-slash"></i>
                                    </button>
                                </div>

                                <div class="password-requirements">
                                    <div class="password-strength-text">Password Strength: Weak</div>
                                    <div class="password-strength-meter">
                                        <div class="password-strength-meter-bar strength-weak"></div>
                                    </div>

                                    <div class="requirement-item" data-requirement="uppercase">
                                        <span class="requirement-icon invalid"><i class="bi bi-x-circle"></i></span>
                                        <span class="requirement-text invalid">Contains an uppercase letter (A-Z)</span>
                                    </div>

                                    <div class="requirement-item" data-requirement="lowercase">
                                        <span class="requirement-icon invalid"><i class="bi bi-x-circle"></i></span>
                                        <span class="requirement-text invalid">Contains a lowercase letter (a-z)</span>
                                    </div>

                                    <div class="requirement-item" data-requirement="number">
                                        <span class="requirement-icon invalid"><i class="bi bi-x-circle"></i></span>
                                        <span class="requirement-text invalid">Contains a number (0-9)</span>
                                    </div>

                                    <div class="requirement-item" data-requirement="special">
                                        <span class="requirement-icon invalid"><i class="bi bi-x-circle"></i></span>
                                        <span class="requirement-text invalid">Contains a special character (!, @, #, etc.)</span>
                                    </div>

                                    <div class="requirement-item" data-requirement="length">
                                        <span class="requirement-icon invalid"><i class="bi bi-x-circle"></i></span>
                                        <span class="requirement-text invalid">Is at least 8 characters long</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Form -->

                        <div class="mb-3"><small class="text-danger"></small></div>

                        <div class="d-grid mb-3">
                            <a href="{{ url('oauth.callback.google') }}" style="display: none"
                                class="btn btn-primary btn-lg google_button">Create Account</a>
                            <button type="submit" class="btn btn-primary btn-lg create_button">
                                Create Account
                            </button>
                        </div>

                        {{-- List all errors --}}
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        <div class="text-center">
                            <p>Already have an account? <a class="link" href="{{ route('login') }}">Log in here</a>
                            </p>
                        </div>
                    </form>
                    <!-- End Form -->
                </div>
            </div>
            <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
            <script src="{{ asset('assets/js/focus-input.js') }}"></script>
            <!-- End Form -->
            <script type="text/javascript">
                $(document).ready(function() {
                    var title = 'Register for Free GravityWrite Account';
                    document.title = title;

                    focusOnInput();

                    // Form submission validation
                    $("form").on("submit", function(event) {
                        // Check if all password requirements are met
                        const allRequirementsMet = $('.requirement-icon.valid').length === 5;

                        if (!allRequirementsMet) {
                            event.preventDefault();
                            // Scroll to password field and focus
                            $('html, body').animate({
                                scrollTop: $('#password-input').offset().top - 100
                            }, 300);
                            $('#password-input').focus();
                        }
                    });
                });

                $('#SimpleSignupEmail').keyup(function () {
                    let string=$('#SimpleSignupEmail').val().toLowerCase();

                    if (/@gmail\.com$/.test(string)) {
                        $('.password_hide').hide();
                        $('.create_button').hide();
                        $('.google_button').show();
                        window.location.href = "{{ route('passport.socialite.redirect', 'google') }}";
                    } else{
                        $('.password_hide').show();
                        $('.create_button').show();
                        $('.google_button').hide();
                    }
                });
            </script>

            <!-- Password Validation Script -->
            <link rel="stylesheet" href="{{ asset('assets/css/password-validation.css') }}">
            <script src="{{ asset('assets/js/password-validation.js') }}"></script>
    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== SECONDARY CONTENTS ========== -->
    <!-- Go To -->
    <a class="js-go-to go-to position-fixed" href="javascript:;" style="visibility: hidden;"
        data-hs-go-to-options='{
       "offsetTop": 700,
       "position": {
         "init": {
           "right": "2rem"
         },
         "show": {
           "bottom": "2rem"
         },
         "hide": {
           "bottom": "-2rem"
         }
       }
     }'>
        <i class="bi-chevron-up"></i>
    </a>
    <!-- ========== END SECONDARY CONTENTS ========== -->

    <!-- JS Global Compulsory  -->
    <script src="{{ asset('vendor/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>

    <!-- JS Implementing Plugins -->
    <script src="{{ asset('vendor/hs-header/dist/hs-header.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-mega-menu/dist/hs-mega-menu.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-show-animation/dist/hs-show-animation.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-go-to/dist/hs-go-to.min.js') }}"></script>
    <script src="{{ asset('vendor/aos/dist/aos.js') }}"></script>
    <script src="{{ asset('vendor/swiper/swiper-bundle.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-nav-scroller/dist/hs-nav-scroller.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-toggle-switch/dist/hs-toggle-switch.min.js') }}"></script>
    <script src="{{ asset('vendor/hs-toggle-password/dist/hs-toggle-password.js') }}"></script>

    <!-- JS Front -->
    <script src="{{ asset('assets/js/theme.min.js') }}"></script>

    <!-- JS Plugins Init. -->
    <script>
        (function() {

            // INITIALIZATION OF MEGA MENU
            // =======================================================
            new HSMegaMenu('.js-mega-menu', {
                desktop: {
                    position: 'left'
                }
            })


            // INITIALIZATION OF SHOW ANIMATIONS
            // =======================================================
            new HSShowAnimation('.js-animation-link')


            // INITIALIZATION OF BOOTSTRAP VALIDATION
            // =======================================================
            HSBsValidation.init('.js-validate')


            // INITIALIZATION OF GO TO
            // =======================================================
            new HSGoTo('.js-go-to')


            // INITIALIZATION OF TOGGLE PASSWORD
            // =======================================================
            new HSTogglePassword('.js-toggle-password')

            // INITIALIZATION OF AOS
            // =======================================================
            AOS.init({
                duration: 650,
                once: true
            });

            // INITIALIZATION OF TOGGLE SWITCH
            // =======================================================
            new HSToggleSwitch('.js-toggle-switch')

            // INITIALIZATION OF SWIPER
            // =======================================================
            var swiper = new Swiper('.js-swiper-hero-clients', {
                slidesPerView: 2,
                breakpoints: {
                    380: {
                        slidesPerView: 3,
                        spaceBetween: 15,
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 15,
                    },
                    1024: {
                        slidesPerView: 5,
                        spaceBetween: 15,
                    },
                },
            });


            // INITIALIZATION OF NAV SCROLLER
            // =======================================================
            new HsNavScroller('.js-nav-scroller')

            // INITIALIZATION OF BOOTSTRAP DROPDOWN
            // =======================================================
            HSBsDropdown.init()
        })()
    </script>
</body>

</html>
