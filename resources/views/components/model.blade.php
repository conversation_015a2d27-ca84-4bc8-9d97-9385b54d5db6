@php
    $element = 'div';
    $formAttrs = '';
    if ($attributes->has('form')) {
        $element = 'form';
        $enctype = isset($form[2]) ? $form[2] : 'multipart/form-data';
        $formAttrs = "action=$form[0] method=$form[1] enctype=$enctype";
    }
@endphp

<div class="modal fade" id="{{ $id }}" tabindex="-1" role="dialog" aria-labelledby="{{ $id }}Label">
  <div class="modal-dialog modal-dialog-centered modal-lg ">
  <{{ $element }}
        class="modal-dialog modal-dialog-scrollable modal-lg" role="document"
        {{ $formAttrs }}
        >

        @if($attributes->has('form'))
            @csrf
        @endif

        <div class="modal-content">

            @if(isset($header))
            <div class="modal-header">
                {{ $header }}
            </div>
            @endif

            @if(isset($body))
            <div class="modal-body pt-4 pb-2">
                {{ $body }}
            </div>
            @endif

            @if(isset($footer))
            <div class="modal-footer p-3">
                {{ $footer }}
            </div>
            @endif

        </div>

    </{{ $element }}>
  </div>
</div>
