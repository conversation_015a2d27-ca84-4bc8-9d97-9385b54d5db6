#!/bin/bash
# Usage: ./deploy.sh [environment] [github_username] [github_access_token]

set -e  # Exit immediately if a command exits with a non-zero status

# Get parameters from command line or use defaults
ENVIRONMENT=${1:-staging}
GIT_USERNAME=${2:-""}
GIT_ACCESS_TOKEN=${3:-""}

# Set branch based on environment
if [[ "$ENVIRONMENT" == "production" ]]; then
  BRANCH="main"
elif [[ "$ENVIRONMENT" == "staging" ]]; then
  BRANCH="staging"
else
  BRANCH="development"
fi

echo "👨🏻‍💻 Deploying to $ENVIRONMENT environment from $BRANCH branch... 🚀"

# 1. Configure Git credentials if provided
if [[ -n "$GIT_USERNAME" && -n "$GIT_ACCESS_TOKEN" ]]; then
  echo "📍 Setting up Git credentials..."
  git config --global credential.helper store
  echo "https://$GIT_USERNAME:$<EMAIL>" > ~/.git-credentials
  chmod 600 ~/.git-credentials
fi

# 2. Pull the latest code
echo "📍 Pulling latest code from $BRANCH branch..."
git pull origin $BRANCH || { echo "💢 Error ❌: git pull failed"; exit 1; }

# 3. Update permissions for Laravel directories
echo "📍 Updating permissions for Laravel directories..."
sudo chown -R www-data:www-data storage bootstrap/cache
echo "  ➡️ Setting directory permissions to 775..."
sudo find storage bootstrap/cache -type d -exec chmod 775 {} \;
echo "  ➡️ Setting file permissions to 664..."
sudo find storage bootstrap/cache -type f -exec chmod 664 {} \;

# 4. Install dependencies
echo "📍 Installing Composer dependencies..."
export COMPOSER_ALLOW_SUPERUSER=1

# Check if composer.lock is in sync with composer.json
if composer validate --no-check-all --no-check-publish 2>&1 | grep -q "Warning: The lock file is not up to date"; then
  echo "  ➡️ Lock file is out of sync with composer.json, updating..."
  composer update --ignore-platform-reqs --no-interaction --prefer-dist ||
    { echo "💢 Error ❌: composer update failed"; exit 1; }
else
  composer install --ignore-platform-reqs --optimize-autoloader --no-interaction --prefer-dist ||
    { echo "💢 Error ❌: composer install failed"; exit 1; }
fi

# 5. Optimize application and run migrations
echo "📍 Finalizing deployment..."

# Clear optimization cache
echo "  ➡️ Clearing optimization cache..."
php artisan optimize:clear || { echo "💢 Error ❌: Failed to clear optimization cache"; exit 1; }

# Run database migrations
echo "  ➡️ Running database migrations..."
php artisan migrate --force || { echo "💢 Error ❌: Database migration failed"; exit 1; }

# Cache configuration
echo "  ➡️ Caching configuration..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize

# 6. Clean up credentials if they were set
if [[ -f ~/.git-credentials ]]; then
  echo "📍 Cleaning up Git credentials..."
  git config --global --unset credential.helper
  rm -f ~/.git-credentials
fi

echo "✅ Deployment completed successfully!"
