<p align="center"><a href="https://gravitywrite.com" target="_blank"><img src="https://auth.gravitywrite.com/assets/img/logo-dual-dark.png" width="400" alt="GravityWrite Auth"></a></p>

# GravityWrite SSO Authentication Service

A robust Laravel-based Single Sign-On (SSO) authentication service implementing OAuth2 with custom implementations of Laravel Passport grants, social authentication, and secure token management for the GravityWrite ecosystem.

## Features

- **Custom OAuth2 Implementation**
  - Built on Laravel Passport with custom grant types
  - Secure token management with refresh token support
  - Customized authorization flow with session tracking
  - Client management API for application registration
  - Wildcard redirect URI support for dynamic domains

- **Single Sign-On (SSO) Capabilities**
  - Centralized authentication for multiple applications
  - Seamless user experience across the GravityWrite ecosystem
  - Support for web and mobile application authentication

- **Social Authentication**
  - Integration with multiple OAuth providers via Laravel Socialite
  - Support for social login in both web and mobile applications
  - Custom token generation for social authentication

- **Advanced Security Features**
  - Email provider validation to prevent disposable emails
  - OAuth session tracking for security monitoring
  - IP and user agent tracking for suspicious activity detection
  - Password confirmation for sensitive actions

- **User Management**
  - User registration with email verification
  - Password reset functionality
  - Account management capabilities

- **API-First Design**
  - Comprehensive JSON API responses
  - Consistent error handling
  - Standardized response formats

- **Developer-Friendly**
  - Docker support for easy development setup
  - Comprehensive documentation
  - Modular and extensible architecture
  - Automated code style enforcement with Laravel Pint
  - Support for development environments with wildcard redirect URIs

## SSO Implementation

The SSO implementation allows users to authenticate once and access multiple applications within the GravityWrite ecosystem. Key features include:

1. **Web-based SSO**: Users can log in through the central authentication service and be redirected to client applications with valid tokens.

2. **Mobile SSO**: Mobile applications can authenticate users through social providers and receive OAuth tokens for API access.

3. **Token Management**: Comprehensive token lifecycle management including refresh tokens for extended sessions.

4. **Session Tracking**: All OAuth sessions are tracked for security monitoring and audit purposes.

## Quick Links

- [Deployment Guide](DEPLOYMENT.md) - Comprehensive deployment instructions
- [Security Policy](SECURITY.md) - Security features and vulnerability reporting

## Documentation

- [GitHub Workflow README](.github/workflows/README.md) - Details about the CI/CD and code style workflows
- [Setup Secrets Guide](.github/SETUP_SECRETS.md) - Instructions for configuring GitHub environments, secrets and variables
- [Pint Configuration Documentation](docs/PINT_CONFIG.md) - Details about the Laravel Pint code style configuration
- [Wildcard Redirect URIs](docs/wildcard-redirect-uris.md) - Guide for implementing and using wildcard patterns in OAuth redirect URIs
