# Wildcard Redirect URIs for Laravel Passport

This document explains how to use wildcard redirect URIs with Laravel Passport in the GravityWrite Auth application.

## Overview

By default, Laravel Passport requires exact matches for redirect URIs during the OAuth authorization process. This means that if you want to use multiple subdomains or dynamic domains (like ngrok for development), you would need to register each one as a separate client or update the client's redirect URIs every time.

Our custom implementation adds support for wildcard patterns in redirect URIs, allowing a single OAuth client to accept redirects from multiple dynamic domains that match a pattern.

## Key Features

- **Wildcard Subdomain Support**: Match any subdomain of a specific domain
- **Multiple Pattern Support**: Register multiple patterns for a single client
- **Secure Implementation**: Maintains OAuth security standards while adding flexibility
- **Easy Integration**: Works with existing Laravel Passport setup
- **Development Friendly**: Perfect for working with dynamic domains like ngrok

## Supported Wildcard Patterns

The following wildcard patterns are supported:

1. **Subdomain wildcards**: `https://*.example.com/callback`
   - Matches any subdomain of example.com with the specified path
   - Example: `https://app.example.com/callback`, `https://dev.example.com/callback`

2. **Ngrok wildcards**: `https://*.ngrok-free.app/auth/callback`
   - Matches any ngrok subdomain with the specified path
   - Example: `https://abcd1234.ngrok-free.app/auth/callback`

3. **Multiple patterns**: You can register multiple patterns for a single client by separating them with commas
   - Example: `https://*.example.com/callback,https://*.ngrok-free.app/callback`

## Security Considerations

When using wildcard redirect URIs, consider the following security implications:

1. **Scope of wildcards**: Only use wildcards for domains you control or trust. For example, `*.example.com` should only be used if you control the entire example.com domain.

2. **Specificity**: Make your patterns as specific as possible. Include the full path to reduce the risk of malicious redirects.

3. **Development vs. Production**: Consider using different OAuth clients for development and production environments, with appropriate wildcard patterns for each.

4. **Regular audits**: Regularly review your OAuth clients and their redirect URI patterns to ensure they're still appropriate.

## How to Use

### Registering a Client with Wildcard Redirect URIs

When creating or updating an OAuth client, you can specify wildcard patterns in the redirect URI field:

```php
$client = new Client();
$client->name = 'My Application';
$client->redirect = 'https://*.example.com/auth/callback,https://*.ngrok-free.app/auth/callback';
$client->save();
```

### Using the OAuth Authorization Endpoint

Once you have registered a client with wildcard redirect URIs, you can use it in your authorization requests:

```
https://your-auth-server.com/oauth/authorize?
  client_id=your-client-id&
  redirect_uri=https://abc123.ngrok-free.app/auth/callback&
  response_type=code&
  scope=&
  state=random-state-value
```

The redirect URI in the request will be validated against the wildcard patterns registered for the client.

### Testing Wildcard Redirect URIs

You can run the unit tests to verify that the wildcard redirect URI functionality is working correctly:

```bash
php artisan test --filter=WildcardRedirectUriValidatorTest
```

## Implementation Details

The wildcard redirect URI functionality is implemented through the following components:

1. **WildcardRedirectUriValidator**: A custom validator that implements the `RedirectUriValidatorInterface` to support wildcard patterns in redirect URIs.

2. **CustomAuthCodeGrant**: A custom implementation of the OAuth2 server's `AuthCodeGrant` class that uses our custom validator for redirect URI validation.

3. **OAuthServiceProvider**: A service provider that registers our custom components with the application:
   - Binds the `WildcardRedirectUriValidator` to the `RedirectUriValidatorInterface`
   - Extends the `AuthorizationServer` to use our custom `CustomAuthCodeGrant`

4. **CustomClientRepository**: A repository that customizes client validation logic for the OAuth authorization process.

## Troubleshooting

If you encounter issues with wildcard redirect URIs, check the following:

1. Make sure the OAuthServiceProvider is registered in your application (it should be registered in the AppServiceProvider).

2. Verify that your redirect URI patterns are correctly formatted and follow the supported patterns.

3. Check the Laravel logs for any errors related to redirect URI validation.

4. Run the unit tests to verify that the wildcard functionality is working correctly.

5. Clear the application cache using `php artisan optimize:clear` after making changes to the implementation.

6. Ensure your OAuth client has the correct wildcard redirect URI pattern configured in the database.

7. If you're using ngrok, make sure the subdomain in your authorization request matches the pattern in your client configuration.

## Common Issues and Solutions

### "App\Repositories\CustomClientRepository::validateRedirectUri() has #[\Override] attribute, but no matching parent method exists"

This error occurs when the `CustomClientRepository` class tries to override a method that doesn't exist in its parent class. The solution is to:

1. Remove the `validateRedirectUri` method from the `CustomClientRepository` class
2. Create a custom `CustomAuthCodeGrant` class that extends the `AuthCodeGrant` class
3. Override the `validateRedirectUri` method in the `CustomAuthCodeGrant` class
4. Register the custom grant in the `OAuthServiceProvider`

### "Invalid redirect URI"

This error can occur if:

1. The redirect URI in the request doesn't match any of the patterns registered for the client
2. The wildcard pattern is incorrectly formatted
3. The custom validator is not being used during the validation process

Check your client configuration and make sure the redirect URI in the request matches one of the patterns.

## References

- [Laravel Passport Documentation](https://laravel.com/docs/11.x/passport)
- [OAuth 2.0 Redirection Endpoint](https://tools.ietf.org/html/rfc6749#section-3.1.2)
- [OAuth 2.0 for Native Apps](https://tools.ietf.org/html/rfc8252)
- [League OAuth2 Server Documentation](https://oauth2.thephpleague.com/)
