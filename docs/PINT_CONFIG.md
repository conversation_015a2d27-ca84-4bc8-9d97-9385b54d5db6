# Laravel Pint Configuration

This document explains the Laravel Pint configuration used in this project for automated code style fixing.

## Overview

[Laravel Pint](https://laravel.com/docs/pint) is an opinionated PHP code style fixer for minimalists. Pint is built on top of PHP-CS-Fixer and makes it simple to ensure that your code style stays clean and consistent.

Our project uses a customized configuration defined in `pint.json` at the root of the repository.

## Configuration Details

The `pint.json` file defines the code style rules that <PERSON><PERSON> Pint will enforce when run. Our configuration:

- Uses the "per" preset as a base
- Customizes numerous rules to match our project's coding standards

## Key Rules

Here are some of the key rules defined in our configuration:

### Code Structure
- `array_indentation`: Ensures arrays are properly indented
- `blank_line_after_namespace`: Ensures there's a blank line after namespace declarations
- `method_chaining_indentation`: Ensures proper indentation in method chaining
- `ordered_class_elements`: Organizes class elements in a specific order

### Syntax Preferences
- `array_syntax`: Uses short array syntax (`[]` instead of `array()`)
- `concat_space`: Ensures one space around string concatenation operator
- `not_operator_with_space`: Adds spaces around the not operator
- `ordered_imports`: Alphabetically sorts imports

### Modern PHP Features
- `ternary_to_null_coalescing`: Converts ternary operators to null coalescing when possible
- `assign_null_coalescing_to_coalesce_equal`: Uses the coalesce equal operator when possible
- `nullable_type_declaration_for_default_null_value`: Uses nullable type declarations for parameters with null defaults

### Disabled Rules
Several rules are explicitly disabled to match our coding preferences:
- `declare_strict_types`: Not requiring strict type declarations
- `final_class`: Not requiring classes to be final
- `use_arrow_functions`: Not requiring arrow functions
- `void_return`: Not requiring void return type declarations

## Full Configuration

For the complete list of rules and their settings, please refer to the [`pint.json`](../pint.json) file in the repository root.

## Usage

The code style is automatically enforced by the GitHub Actions workflow defined in [`.github/workflows/lint.yml`](../.github/workflows/lint.yml), which runs on every push.

To run Pint locally:

```bash
# Install Laravel Pint if not already installed
composer require laravel/pint --dev

# Run Pint to fix code style issues
./vendor/bin/pint
```

## Related Resources

- [Laravel Pint Documentation](https://laravel.com/docs/pint)
- [PHP-CS-Fixer Documentation](https://github.com/FriendsOfPHP/PHP-CS-Fixer)
- [PSR-12 Coding Standards](https://www.php-fig.org/psr/psr-12/)
