# Deployment Guide

This document provides comprehensive information about deploying the GravityWrite SSO application to different environments.

## Deployment Methods

There are two primary methods for deploying the application:

1. **Automated Deployment via GitHub Actions**
2. **Manual Deployment**

## 1. Automated Deployment via GitHub Actions

The project is configured with GitHub Actions workflows that automatically deploy the application when changes are pushed to specific branches.

### Deployment Triggers

- Push to `main` branch: Deploys to production environment
- Push to `staging` branch: Deploys to staging environment
- Manual trigger: Can be run manually from GitHub Actions tab

### Required Configuration

Before using the automated deployment, you need to set up the required environment secrets, repository variables, and repository secrets in your GitHub repository:

#### Environment Secrets (for both staging and production)

- `SSH_PRIVATE_KEY`: SSH private key for connecting to the server
- `SSH_HOST`: Hostname or IP address of the server
- `SSH_USER`: Username for SSH connection to the server
- `DEPLOY_PATH`: Path to the application environment on the server

#### Repository Variables

- `GIT_USERNAME`: Your GitHub username
- `GIT_USER_EMAIL`: Your GitHub email address
- `GIT_USER_NAME`: Your full name for Git commits

#### Repository Secrets

- `GIT_ACCESS_TOKEN`: GitHub Personal Access Token with appropriate permissions

See the [Setup Secrets Guide](.github/SETUP_SECRETS.md) for detailed instructions on setting up these configurations.

### Workflow Process

The automated deployment workflow:

1. Checks out the code
2. Sets up PHP 8.3
3. Determines the target environment based on the branch
4. Configures Git with the repository variables (GIT_USER_NAME, GIT_USER_EMAIL)
5. Sets up SSH with the environment secret (SSH_PRIVATE_KEY)
6. Runs the deployment script on the server which:
   - Pulls the latest code from the appropriate branch using GIT_USERNAME and GIT_ACCESS_TOKEN
   - Installs Composer dependencies
   - Optimizes the application and runs migrations

For more details about the workflow, see the [Workflow README](.github/workflows/README.md).

## 2. Manual Deployment

If you need to deploy the application manually, you can use the included deployment scripts.

### Using deploy.sh

The `deploy.sh` script is the primary deployment script used by the GitHub Actions workflow. It can also be used for manual deployments.

```bash
# Deploy to staging (default)
./deploy.sh

# Deploy to production
./deploy.sh production

# Deploy with GitHub credentials
./deploy.sh [environment] [github_username] [github_access_token]
```

The script performs the following actions:
- Pulls the latest code from the appropriate branch (using GitHub credentials if provided)
- Installs Composer dependencies
- Runs database migrations
- Optimizes the application (config cache, route cache, view cache)
- Cleans up any temporary credentials

## Deployment Requirements

Regardless of the deployment method, ensure the following requirements are met:

### Server Requirements

- PHP >= 8.3
- Composer
- Git
- MySQL/MariaDB
- Node.js & NPM (if building assets on the server)

### Application Requirements

- Proper environment configuration (.env file)
- Database connection
- Required directories are writable:
  - `storage/`
  - `bootstrap/cache/`

## Post-Deployment Tasks

After deployment, you may need to perform additional tasks:

1. Clear application cache:
   ```bash
   php artisan cache:clear
   ```

2. Restart queue workers (if using queues):
   ```bash
   php artisan queue:restart
   ```

3. Verify the application is running correctly

## Troubleshooting

If you encounter issues during deployment:

1. Check the deployment logs
2. Verify that all environment variables are correctly set
3. Ensure the server meets all requirements
4. Check file permissions
5. Review the application logs in `storage/logs/`

## Rollback Procedure

If you need to roll back to a previous version:

1. Identify the commit hash of the stable version
2. Check out that version:
   ```bash
   git checkout <commit-hash>
   ```
3. Run the deployment script again

## Additional Resources

- [Laravel Deployment Documentation](https://laravel.com/docs/10.x/deployment)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
