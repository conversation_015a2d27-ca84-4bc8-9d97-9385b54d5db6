<?php

namespace Tests;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

abstract class TestCase extends BaseTestCase
{
    use RefreshDatabase;

    /**
     * Setup the test environment.
     */
    #[\Override]
    protected function setUp(): void
    {
        parent::setUp();

        // Verify we're using the test database (SQLite in-memory)
        $this->verifyTestDatabaseIsolation();

        // Ensure we're in testing environment
        $this->assertEquals('testing', app()->environment());
    }

    /**
     * Verify that tests are running against the isolated test database.
     * This prevents accidental data loss in development database.
     */
    protected function verifyTestDatabaseIsolation(): void
    {
        $connection = Config::get('database.default');
        $database = Config::get('database.connections.' . $connection . '.database');

        // Ensure we're using SQLite in-memory database for tests
        $this->assertEquals(
            'sqlite',
            $connection,
            'Tests must use SQLite database for isolation. Current connection: ' . $connection,
        );

        $this->assertEquals(
            ':memory:',
            $database,
            'Tests must use in-memory SQLite database. Current database: ' . $database,
        );

        // Double-check by verifying the actual database connection
        $actualConnection = DB::connection()->getDriverName();
        $this->assertEquals(
            'sqlite',
            $actualConnection,
            'Database driver must be SQLite for test isolation. Current driver: ' . $actualConnection,
        );
    }

    /**
     * Verify that the test database is completely isolated.
     * This method can be called in tests to ensure isolation.
     */
    protected function assertDatabaseIsolation(): void
    {
        $this->verifyTestDatabaseIsolation();

        // Verify that we can create and drop tables without affecting development database
        $testTableName = 'test_isolation_verification_' . uniqid();

        // Create a temporary test table
        DB::statement("CREATE TABLE {$testTableName} (id INTEGER PRIMARY KEY, test_data TEXT)");

        // Verify table exists
        $this->assertTrue(
            DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name='{$testTableName}'") !== [],
            'Test table should be created in isolated database',
        );

        // Clean up test table
        DB::statement("DROP TABLE {$testTableName}");

        // Verify table is gone
        $this->assertTrue(
            DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name='{$testTableName}'") === [],
            'Test table should be dropped from isolated database',
        );
    }
}
