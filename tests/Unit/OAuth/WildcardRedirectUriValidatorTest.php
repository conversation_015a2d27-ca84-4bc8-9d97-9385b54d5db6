<?php

namespace Tests\Unit\OAuth;

use App\OAuth\WildcardRedirectUriValidator;
use PHPUnit\Framework\TestCase;

class WildcardRedirectUriValidatorTest extends TestCase
{
    /**
     * Test that the validator correctly validates exact matches.
     */
    public function test_validates_exact_match(): void
    {
        $validator = new WildcardRedirectUriValidator('https://example.com/callback');

        $this->assertTrue($validator->validateRedirectUri('https://example.com/callback'));
        $this->assertFalse($validator->validateRedirectUri('https://example.com/other-callback'));
        $this->assertFalse($validator->validateRedirectUri('https://other-example.com/callback'));
    }

    /**
     * Test that the validator correctly validates multiple exact matches.
     */
    public function test_validates_multiple_exact_matches(): void
    {
        $validator = new WildcardRedirectUriValidator([
            'https://example.com/callback',
            'https://other-example.com/callback',
        ]);

        $this->assertTrue($validator->validateRedirectUri('https://example.com/callback'));
        $this->assertTrue($validator->validateRedirectUri('https://other-example.com/callback'));
        $this->assertFalse($validator->validateRedirectUri('https://third-example.com/callback'));
    }

    /**
     * Test that the validator correctly validates wildcard domain patterns.
     */
    public function test_validates_wildcard_domain_patterns(): void
    {
        $validator = new WildcardRedirectUriValidator('https://*.example.com/callback');

        $this->assertTrue($validator->validateRedirectUri('https://sub.example.com/callback'));
        $this->assertTrue($validator->validateRedirectUri('https://another-sub.example.com/callback'));
        $this->assertFalse($validator->validateRedirectUri('https://example.com/callback'));
        $this->assertFalse($validator->validateRedirectUri('https://sub.other-example.com/callback'));
    }

    /**
     * Test that the validator correctly validates ngrok-style wildcard patterns.
     */
    public function test_validates_ngrok_wildcard_patterns(): void
    {
        $validator = new WildcardRedirectUriValidator('https://*.ngrok-free.app/auth/callback');

        $this->assertTrue($validator->validateRedirectUri('https://abcd1234.ngrok-free.app/auth/callback'));
        $this->assertTrue($validator->validateRedirectUri('https://another-subdomain.ngrok-free.app/auth/callback'));
        $this->assertFalse($validator->validateRedirectUri('https://ngrok-free.app/auth/callback'));
        $this->assertFalse($validator->validateRedirectUri('https://abcd1234.ngrok-free.app/different/callback'));
    }

    /**
     * Test that the validator correctly validates wildcard patterns with different paths.
     */
    public function test_validates_wildcard_patterns_with_paths(): void
    {
        $validator = new WildcardRedirectUriValidator('https://*.example.com/auth/callback');

        $this->assertTrue($validator->validateRedirectUri('https://sub.example.com/auth/callback'));
        $this->assertTrue($validator->validateRedirectUri('https://sub.example.com/auth/callback?code=123'));
        $this->assertFalse($validator->validateRedirectUri('https://sub.example.com/different/callback'));
    }

    /**
     * Test that the validator correctly validates wildcard patterns with root paths.
     */
    public function test_validates_wildcard_patterns_with_root_path(): void
    {
        $validator = new WildcardRedirectUriValidator('https://*.example.com/');

        $this->assertTrue($validator->validateRedirectUri('https://sub.example.com/'));
        $this->assertTrue($validator->validateRedirectUri('https://sub.example.com/any/path'));
        $this->assertTrue($validator->validateRedirectUri('https://sub.example.com/auth/callback'));
    }

    /**
     * Test that the validator correctly validates multiple wildcard patterns.
     */
    public function test_validates_multiple_wildcard_patterns(): void
    {
        $validator = new WildcardRedirectUriValidator([
            'https://*.example.com/callback',
            'https://*.ngrok-free.app/auth/callback',
        ]);

        $this->assertTrue($validator->validateRedirectUri('https://sub.example.com/callback'));
        $this->assertTrue($validator->validateRedirectUri('https://abcd1234.ngrok-free.app/auth/callback'));
        $this->assertFalse($validator->validateRedirectUri('https://sub.other-domain.com/callback'));
    }

    /**
     * Test that the validator correctly handles invalid URIs.
     */
    public function test_handles_invalid_uris(): void
    {
        $validator = new WildcardRedirectUriValidator('https://*.example.com/callback');

        $this->assertFalse($validator->validateRedirectUri('not-a-valid-uri'));
        $this->assertFalse($validator->validateRedirectUri('http://'));
    }
}
