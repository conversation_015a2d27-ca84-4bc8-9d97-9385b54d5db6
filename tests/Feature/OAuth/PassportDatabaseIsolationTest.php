<?php

namespace Tests\Feature\OAuth;

use App\Models\Passport\Client;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class PassportDatabaseIsolationTest extends TestCase
{
    /**
     * Test that Passport tables are created in the test database.
     */
    public function test_passport_tables_exist_in_test_database(): void
    {
        // Skip if Passport is not installed
        if ( ! class_exists(\Laravel\Passport\Passport::class)) {
            $this->markTestSkipped('Laravel Passport is not installed');
        }

        // Verify we're using the test database
        $this->assertDatabaseIsolation();

        // Check that Passport tables exist
        $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table'");
        $tableNames = array_column($tables, 'name');

        $expectedTables = [
            'oauth_clients',
            'oauth_access_tokens',
            'oauth_refresh_tokens',
            'oauth_auth_codes',
            'oauth_personal_access_clients',
        ];

        foreach ($expectedTables as $table) {
            $this->assertContains($table, $tableNames, "Table {$table} should exist in test database");
        }
    }

    /**
     * Test that we can create OAuth clients safely in the test database.
     */
    public function test_create_oauth_client_in_test_database(): void
    {
        // Skip if Passport is not installed
        if ( ! class_exists(\Laravel\Passport\Passport::class)) {
            $this->markTestSkipped('Laravel Passport is not installed');
        }

        // Verify database isolation
        $this->assertDatabaseIsolation();

        // Start with empty oauth_clients table
        $this->assertDatabaseEmpty('oauth_clients');

        // Create a test OAuth client
        $client = Client::create([
            'id' => 'test-client-' . uniqid(),
            'name' => 'Test OAuth Client',
            'secret' => 'test-secret-' . uniqid(),
            'redirect' => 'http://localhost/test/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);

        // Verify client was created
        $this->assertDatabaseHas('oauth_clients', [
            'id' => $client->id,
            'name' => 'Test OAuth Client',
        ]);

        // Verify we can retrieve the client
        $retrievedClient = Client::find($client->id);
        $this->assertNotNull($retrievedClient);
        $this->assertEquals('Test OAuth Client', $retrievedClient->name);

        // Clean up
        $client->delete();
        $this->assertDatabaseMissing('oauth_clients', [
            'id' => $client->id,
        ]);
    }

    /**
     * Test that we can create users and associate them with OAuth tokens safely.
     */
    public function test_create_user_with_oauth_tokens_in_test_database(): void
    {
        // Skip if Passport is not installed
        if ( ! class_exists(\Laravel\Passport\Passport::class)) {
            $this->markTestSkipped('Laravel Passport is not installed');
        }

        // Verify database isolation
        $this->assertDatabaseIsolation();

        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Create a test OAuth client
        $client = Client::create([
            'id' => 'test-client-' . uniqid(),
            'name' => 'Test OAuth Client for User',
            'secret' => 'test-secret-' . uniqid(),
            'redirect' => 'http://localhost/test/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);

        // Verify both user and client exist
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('oauth_clients', [
            'id' => $client->id,
        ]);

        // Test that we can create access tokens (this would be done by Passport normally)
        DB::table('oauth_access_tokens')->insert([
            'id' => 'test-token-' . uniqid(),
            'user_id' => $user->id,
            'client_id' => $client->id,
            'name' => 'Test Token',
            'scopes' => json_encode([]),
            'revoked' => false,
            'created_at' => now(),
            'updated_at' => now(),
            'expires_at' => now()->addHour(),
        ]);

        // Verify token was created
        $this->assertDatabaseHas('oauth_access_tokens', [
            'user_id' => $user->id,
            'client_id' => $client->id,
        ]);

        // Clean up (RefreshDatabase will handle this, but let's be explicit)
        DB::table('oauth_access_tokens')->where('user_id', $user->id)->delete();
        $client->delete();
        $user->delete();

        // Verify cleanup
        $this->assertDatabaseMissing('oauth_access_tokens', [
            'user_id' => $user->id,
        ]);
        $this->assertDatabaseMissing('oauth_clients', [
            'id' => $client->id,
        ]);
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Test that wildcard redirect URI validation works in test environment.
     */
    public function test_wildcard_redirect_uri_validation_in_test_database(): void
    {
        // Skip if Passport is not installed
        if ( ! class_exists(\Laravel\Passport\Passport::class)) {
            $this->markTestSkipped('Laravel Passport is not installed');
        }

        // Verify database isolation
        $this->assertDatabaseIsolation();

        // Create a client with wildcard redirect URI
        $client = Client::create([
            'id' => 'wildcard-client-' . uniqid(),
            'name' => 'Wildcard Test Client',
            'secret' => 'wildcard-secret-' . uniqid(),
            'redirect' => 'https://*.ngrok-free.app/auth/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);

        // Verify client was created with wildcard redirect
        $this->assertDatabaseHas('oauth_clients', [
            'id' => $client->id,
            'redirect' => 'https://*.ngrok-free.app/auth/callback',
        ]);

        // Test our wildcard validator if it exists
        if (class_exists(\App\OAuth\WildcardRedirectUriValidator::class)) {
            $validator = new \App\OAuth\WildcardRedirectUriValidator($client->redirect);

            // Test valid wildcard matches
            $this->assertTrue($validator->validateRedirectUri('https://test123.ngrok-free.app/auth/callback'));
            $this->assertTrue($validator->validateRedirectUri('https://another-sub.ngrok-free.app/auth/callback'));

            // Test invalid matches
            $this->assertFalse($validator->validateRedirectUri('https://ngrok-free.app/auth/callback'));
            $this->assertFalse($validator->validateRedirectUri('https://test123.ngrok-free.app/different/callback'));
        }

        // Clean up
        $client->delete();
        $this->assertDatabaseMissing('oauth_clients', [
            'id' => $client->id,
        ]);
    }

    /**
     * Test that multiple tests don't interfere with each other.
     */
    public function test_multiple_tests_dont_interfere(): void
    {
        // This test should start with clean tables
        $this->assertDatabaseEmpty('users');

        if (class_exists(\Laravel\Passport\Passport::class)) {
            $this->assertDatabaseEmpty('oauth_clients');
            $this->assertDatabaseEmpty('oauth_access_tokens');
        }

        // Create some test data
        $user = User::factory()->create(['email' => '<EMAIL>']);

        if (class_exists(\Laravel\Passport\Passport::class)) {
            $client = Client::create([
                'id' => 'interference-test-' . uniqid(),
                'name' => 'Interference Test Client',
                'secret' => 'interference-secret',
                'redirect' => 'http://localhost/interference/callback',
                'personal_access_client' => false,
                'password_client' => false,
                'revoked' => false,
            ]);
        }

        // Verify data exists
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);

        if (isset($client)) {
            $this->assertDatabaseHas('oauth_clients', ['id' => $client->id]);
        }

        // RefreshDatabase will clean this up for the next test
    }
}
