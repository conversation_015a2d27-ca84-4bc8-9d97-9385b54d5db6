<?php

namespace Tests\Feature\OAuth;

use App\Models\Passport\Client;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class PassportSSOAuthenticationFlowsTest extends TestCase
{
    use RefreshDatabase;

    protected Client $client;

    #[\Override]
    protected function setUp(): void
    {
        parent::setUp();

        // Create a test OAuth client
        $this->client = Client::create([
            'id' => 'test-sso-client',
            'name' => 'GravityWriteDefaultRedirect',
            'secret' => 'test-secret',
            'redirect' => 'http://localhost/test/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);
    }

    /**
     * Test Scenario 1: Direct Login Access - Login page loads without redirect loops
     */
    public function test_direct_login_access(): void
    {
        $this->assertDatabaseIsolation();

        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertSee('Welcome back');
        $response->assertSee('Login to manage your account');
    }

    /**
     * Test Scenario 2: Direct Register Access - Register page loads without redirect loops
     */
    public function test_direct_register_access(): void
    {
        $this->assertDatabaseIsolation();

        $response = $this->get(route('register'));

        $response->assertStatus(200);
        $response->assertViewIs('auth.register');
        $response->assertSee('Create Account');
    }

    /**
     * Test Scenario 3: Login with Email Parameter - Currently redirects to register due to isRegister() logic
     */
    public function test_login_with_email_parameter(): void
    {
        $this->assertDatabaseIsolation();

        $response = $this->get(route('login', ['email' => '<EMAIL>']));

        // Current behavior: email parameter triggers registration flow
        $response->assertRedirect(route('register', ['email' => '<EMAIL>']));
    }

    /**
     * Test Scenario 4: Standard OAuth Flow - Redirects to login when not authenticated
     */
    public function test_standard_oauth_flow(): void
    {
        $this->assertDatabaseIsolation();

        $oauthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        $response = $this->get($oauthUrl);

        $response->assertRedirect(route('login'));
    }

    /**
     * Test Scenario 6: From client redirect with is_register key (No Email) - Redirects to registration page
     */
    public function test_oauth_with_is_register_no_email(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should redirect to register
        $response = $this->get(route('login'));

        $response->assertRedirect(route('register'));
    }

    /**
     * Test Scenario 7: From client redirect with is_register key (with Email) - Redirects to registration page with pre-filled email
     */
    public function test_oauth_with_is_register_with_new_email(): void
    {
        $this->assertDatabaseIsolation();

        $email = '<EMAIL>';

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should redirect to register with email
        $response = $this->get(route('login'));

        $response->assertRedirect(route('register', ['email' => $email]));
    }

    /**
     * Test NEW Scenario 1: From client redirect with is_register=1 and existing email - Show login form with pre-filled email
     */
    public function test_oauth_with_is_register_existing_email(): void
    {
        $this->assertDatabaseIsolation();

        // Create an existing user
        $user = User::factory()->create(['email' => '<EMAIL>']);

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $user->email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should show login with pre-filled email
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertViewHas('prefill_email', $user->email);
    }

    /**
     * Test NEW Scenario 2: From client redirect with is_register=1 and new email - Redirect to registration with pre-filled email
     */
    public function test_oauth_with_is_register_new_email(): void
    {
        $this->assertDatabaseIsolation();

        $email = '<EMAIL>';

        // Ensure user doesn't exist
        $this->assertDatabaseMissing('users', ['email' => $email]);

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should redirect to register with email
        $response = $this->get(route('login'));

        $response->assertRedirect(route('register', ['email' => $email]));
    }

    /**
     * Test NEW Scenario 3: From client redirect with is_register=1 and new Gmail ID - Directly trigger Google SSO for registration
     */
    public function test_oauth_with_is_register_new_gmail(): void
    {
        $this->assertDatabaseIsolation();

        $email = '<EMAIL>';

        // Ensure user doesn't exist
        $this->assertDatabaseMissing('users', ['email' => $email]);

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should redirect to Google SSO
        $response = $this->get(route('login'));

        $response->assertRedirect(route('passport.socialite.redirect', 'google'));
    }

    /**
     * Test NEW Scenario 4: From client redirect with is_register=1 and no email parameter - Redirect to registration page (same as scenario 6)
     */
    public function test_oauth_with_is_register_no_email_parameter(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should redirect to register
        $response = $this->get(route('login'));

        $response->assertRedirect(route('register'));
    }

    /**
     * Test session cleanup to prevent cross-tab interference
     */
    public function test_session_cleanup_prevents_interference(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate stale session data
        Session::put('prefill_email', '<EMAIL>');
        Session::put('google_sso_email', '<EMAIL>');

        // Direct login access should clean up stale data
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $this->assertNull(Session::get('prefill_email'));
        $this->assertNull(Session::get('google_sso_email'));
    }

    /**
     * Test email existence checking functionality
     */
    public function test_email_existence_checking(): void
    {
        $this->assertDatabaseIsolation();

        // Create a user
        $user = User::factory()->create(['email' => '<EMAIL>']);

        // Create a controller instance that uses the trait to test the method
        $controller = new \App\Http\Controllers\Auth\LoginController();

        // Test existing email
        $this->assertTrue($controller->checkEmailExists($user->email));

        // Test non-existing email
        $this->assertFalse($controller->checkEmailExists('<EMAIL>'));
    }

    /**
     * Test that login page works when default OAuth client doesn't exist
     */
    public function test_login_access_without_default_oauth_client(): void
    {
        $this->assertDatabaseIsolation();

        // Ensure no default client exists
        Client::where('name', 'GravityWriteDefaultRedirect')->delete();

        // Direct login access should work without errors
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertSee('Welcome back');

        // Should not have authRequest in session since no default client exists
        $this->assertNull(session('authRequest'));
    }

    /**
     * Test that register page works when default OAuth client doesn't exist
     */
    public function test_register_access_without_default_oauth_client(): void
    {
        $this->assertDatabaseIsolation();

        // Ensure no default client exists
        Client::where('name', 'GravityWriteDefaultRedirect')->delete();

        // Direct register access should work without errors
        $response = $this->get(route('register'));

        $response->assertStatus(200);
        $response->assertViewIs('auth.register');
        $response->assertSee('Create Account');
    }

    /**
     * Test session persistence issue: Multiple OAuth requests in same browser session
     * This test simulates the exact issue described where the second request fails
     */
    public function test_multiple_oauth_requests_same_session(): void
    {
        $this->assertDatabaseIsolation();

        // First OAuth request (no is_register parameter)
        $firstOAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        // Simulate first OAuth request
        Session::put('redirect_uri', $firstOAuthUrl);
        Session::put('is_first_time', true);

        // First request should redirect to login
        $response1 = $this->get(route('login'));
        $response1->assertStatus(200);
        $response1->assertViewIs('auth.login');

        // Now simulate second OAuth request with is_register=true and email
        $secondOAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ]);

        // Simulate the second OAuth request by setting up session as if coming from OAuth redirect
        // This simulates what happens when the user clicks a different OAuth link
        $this->withSession([
            'url' => ['previous' => $secondOAuthUrl],
        ]);

        // Clear previous session data and set new OAuth request
        Session::put('redirect_uri', $secondOAuthUrl);
        Session::put('is_first_time', true);

        // Second request should redirect to Google SSO (since it's a Gmail address for registration)
        $response2 = $this->get(route('login'));
        $response2->assertRedirect(route('passport.socialite.redirect', 'google'));
    }

    /**
     * Test the core session persistence issue: is_first_time flag blocking subsequent requests
     */
    public function test_is_first_time_flag_reset_for_new_oauth_requests(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate first OAuth request (login flow)
        $firstOAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        Session::put('redirect_uri', $firstOAuthUrl);
        Session::put('is_first_time', true);

        // First request should work and set is_first_time to false
        $response1 = $this->get(route('login'));
        $response1->assertStatus(200);
        $response1->assertViewIs('auth.login');

        // Verify is_first_time was set to false
        $this->assertFalse(Session::get('is_first_time'));

        // Now simulate a NEW OAuth request with is_register=1
        $secondOAuthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => '<EMAIL>',
        ]);

        // Manually set the new OAuth request (simulating what happens when user clicks new OAuth link)
        Session::put('redirect_uri', $secondOAuthUrl);
        Session::put('is_first_time', true); // This should be reset for new OAuth requests

        // Second request should process the new parameters
        $response2 = $this->get(route('login'));
        $response2->assertRedirect(route('register', ['email' => '<EMAIL>']));
    }

    /**
     * Test that clearPreviousOAuthSession method works correctly
     */
    public function test_clear_previous_oauth_session(): void
    {
        $this->assertDatabaseIsolation();

        // Set up session data that should be cleared
        Session::put('redirect_uri', '/old/oauth/url');
        Session::put('is_first_time', false);
        Session::put('prefill_email', '<EMAIL>');
        Session::put('google_sso_email', '<EMAIL>');
        Session::put('oauth_params_backup', ['old' => 'data']);
        Session::put('authRequest', ['old' => 'request']);

        // Create a controller instance to test the method
        $controller = new \App\Http\Controllers\Auth\LoginController();

        // Use reflection to access the protected method
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('clearPreviousOAuthSession');
        $method->setAccessible(true);

        // Call the method
        $method->invoke($controller);

        // Verify all OAuth-related session data was cleared
        $this->assertNull(Session::get('redirect_uri'));
        $this->assertNull(Session::get('is_first_time'));
        $this->assertNull(Session::get('prefill_email'));
        $this->assertNull(Session::get('google_sso_email'));
        $this->assertNull(Session::get('oauth_params_backup'));
        $this->assertNull(Session::get('authRequest'));
    }
}
