<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class DatabaseIsolationTest extends TestCase
{
    /**
     * Test that we're using SQLite in-memory database for tests.
     */
    public function test_using_sqlite_in_memory_database(): void
    {
        $connection = Config::get('database.default');
        $database = Config::get('database.connections.' . $connection . '.database');

        $this->assertEquals('sqlite', $connection);
        $this->assertEquals(':memory:', $database);

        // Verify the actual connection
        $this->assertEquals('sqlite', DB::connection()->getDriverName());
    }

    /**
     * Test that we're in testing environment.
     */
    public function test_running_in_testing_environment(): void
    {
        $this->assertEquals('testing', app()->environment());
        $this->assertEquals('testing', config('app.env'));
    }

    /**
     * Test that database isolation verification works.
     */
    public function test_database_isolation_verification(): void
    {
        // This should not throw any exceptions
        $this->assertDatabaseIsolation();
    }

    /**
     * Test that we can safely create and delete data without affecting development database.
     */
    public function test_safe_database_operations(): void
    {
        // Start with empty database
        $this->assertDatabaseEmpty('users');

        // Create test data
        $user = User::factory()->create([
            'name' => 'Test User for Isolation',
            'email' => '<EMAIL>',
        ]);

        // Verify data exists
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);

        // Delete the user
        $user->delete();

        // Verify data is gone
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Test that RefreshDatabase trait cleans up between tests.
     */
    public function test_refresh_database_cleans_up_between_tests(): void
    {
        // This test should start with a clean database
        $this->assertDatabaseEmpty('users');

        // Create some test data
        User::factory()->count(3)->create();

        // Verify data exists
        $this->assertEquals(3, User::count());

        // The RefreshDatabase trait will clean this up after the test
    }

    /**
     * Test that the previous test's data is not present (verifies RefreshDatabase works).
     */
    public function test_previous_test_data_is_cleaned_up(): void
    {
        // This should be empty because RefreshDatabase cleaned up the previous test
        $this->assertDatabaseEmpty('users');
        $this->assertEquals(0, User::count());
    }

    /**
     * Test that we can run migrations safely in the test database.
     */
    public function test_migrations_run_safely(): void
    {
        // Verify that essential tables exist (created by migrations)
        $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table'");
        $tableNames = array_column($tables, 'name');

        $this->assertContains('users', $tableNames);
        $this->assertContains('migrations', $tableNames);

        // If Passport is installed, these tables should exist too
        if (class_exists(\Laravel\Passport\Passport::class)) {
            $this->assertContains('oauth_clients', $tableNames);
            $this->assertContains('oauth_access_tokens', $tableNames);
            $this->assertContains('oauth_refresh_tokens', $tableNames);
        }
    }

    /**
     * Test that test configuration is properly loaded.
     */
    public function test_test_configuration_is_loaded(): void
    {
        // Verify test-specific configurations
        $this->assertEquals('array', config('cache.default'));
        $this->assertEquals('array', config('session.driver'));
        $this->assertEquals('array', config('mail.default'));
        $this->assertEquals('sync', config('queue.default'));

        // Verify testing flags (these might be null if not configured, which is fine)
        $telescopeEnabled = config('telescope.enabled');
        $pulseEnabled = config('pulse.enabled');

        if ($telescopeEnabled !== null) {
            $this->assertFalse($telescopeEnabled);
        }

        if ($pulseEnabled !== null) {
            $this->assertFalse($pulseEnabled);
        }

        // Verify bcrypt rounds are reduced for faster testing
        $this->assertEquals(4, config('hashing.bcrypt.rounds'));
    }

    /**
     * Test that we can create OAuth clients safely in test database.
     */
    public function test_oauth_client_creation_in_test_database(): void
    {
        // Skip if Passport is not installed
        if ( ! class_exists(\Laravel\Passport\Passport::class)) {
            $this->markTestSkipped('Laravel Passport is not installed');
        }

        // Create a test OAuth client
        $client = new \App\Models\Passport\Client([
            'id' => 'test-client-id',
            'name' => 'Test Client',
            'secret' => 'test-client-secret',
            'redirect' => 'http://localhost/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);

        $client->save();

        // Verify client exists in test database (use the actual ID that was generated)
        $this->assertDatabaseHas('oauth_clients', [
            'id' => $client->id,
            'name' => 'Test Client',
        ]);

        // Clean up
        $client->delete();

        $this->assertDatabaseMissing('oauth_clients', [
            'id' => $client->id,
        ]);
    }
}
