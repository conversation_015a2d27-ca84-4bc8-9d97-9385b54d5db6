name: Deploy Application

on:
  push:
    branches: [main, staging, development]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production

jobs:
  deploy:
    name: Deploy Application
    runs-on: ubuntu-latest
    environment: ${{ github.ref == 'refs/heads/main' && 'production' || github.ref == 'refs/heads/staging' && 'staging' || 'development' }}
    env:
      COMPOSER_PROCESS_TIMEOUT: 0
      COMPOSER_NO_INTERACTION: 1
      COMPOSER_NO_AUDIT: 1

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'

      - name: Set environment variables
        id: env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "name=production" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/staging" ]]; then
            echo "name=staging" >> $GITHUB_OUTPUT
          else
            echo "name=development" >> $GITHUB_OUTPUT
          fi
          echo "Deploying to ${{ steps.env.outputs.name }} environment"

      - name: Configure Git
        run: |
          git config --global user.name "${{ vars.GIT_USER_NAME }}"
          git config --global user.email "${{ vars.GIT_USER_EMAIL }}"

      - name: Add SSH key
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Deploy to server
        run: |
          # Ensure target directory exists
          ssh -o StrictHostKeyChecking=no ${{ vars.SSH_USER }}@${{ vars.SSH_HOST }} "cd ${{ vars.DEPLOY_PATH }} || mkdir -p ${{ vars.DEPLOY_PATH }}"

          # Run deployment script
          ssh -o StrictHostKeyChecking=no ${{ vars.SSH_USER }}@${{ vars.SSH_HOST }} "cd ${{ vars.DEPLOY_PATH }} && bash -s" < deploy.sh ${{ steps.env.outputs.name }} "${{ vars.GIT_USERNAME }}" "${{ secrets.GIT_ACCESS_TOKEN }}"

          echo "🥳 Deployment to ${{ steps.env.outputs.name }} completed successfully ✅!"
