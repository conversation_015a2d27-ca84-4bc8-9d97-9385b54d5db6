name: Fix Code Style

on:
  push:
    branches-ignore:
      - 'main'
      - 'staging'

jobs:
  lint:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: true
      matrix:
        php: [8.3]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: json, dom, curl, libxml, mbstring
          coverage: none

      - name: Install Pint
        run: composer global require laravel/pint

      # @docs https://cs.symfony.com/doc/rules/
      - name: Run Pint
        run: pint

      - name: Install Rector
        run: composer require --dev driftingly/rector-laravel

      # check ./rector.php for configuration
      - name: Check Typos
        run: |
          if [[ "$RUNNER_OS" == "Linux" || "$RUNNER_OS" == "macOS" ]]; then
            composer test:rector
          fi

      - name: Install Peck
        run: composer require peckphp/peck

      - name: Install Aspell
        shell: bash
        run: |
          if [[ "$RUNNER_OS" == "Linux" ]]; then
            sudo apt-get update && sudo apt-get install -y aspell aspell-en
          elif [[ "$RUNNER_OS" == "macOS" ]]; then
            brew install aspell
          fi

      # check ./peck.json for configuration
      - name: Check Typos
        run: |
          if [[ "$RUNNER_OS" == "Linux" || "$RUNNER_OS" == "macOS" ]]; then
            composer test:typos
          fi

      - name: Commit linted files
        uses: stefanzweifel/git-auto-commit-action@v5
