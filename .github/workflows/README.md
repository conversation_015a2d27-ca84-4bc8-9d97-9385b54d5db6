# GitHub CI/CD Workflows

This directory contains streamlined GitHub Actions workflows for automating deployment and code quality processes.

## Workflows Overview

This repository contains the following workflows:

1. **Deployment Workflow** - Automates application deployment
2. **Code Style Workflow** - Automatically fixes code style issues

## Deployment Workflow

The `deploy.yml` workflow automatically deploys your application to the appropriate environment based on the branch.

### Deployment Triggers

- Push to `main` branch: Deploys to production environment
- Push to `staging` branch: Deploys to staging environment
- Manual trigger: Can be run manually from GitHub Actions tab

### Required Configuration

The following configuration needs to be set up in your GitHub repository settings:

#### Environment Secrets

These secrets are configured for each environment (staging, production):

- `SSH_PRIVATE_KEY`: SSH private key for connecting to the server
- `SSH_HOST`: Hostname or IP address of the server
- `SSH_USER`: Username for SSH connection to the server
- `DEPLOY_PATH`: Path to the application environment on the server

#### Repository Variables

These variables are configured at the repository level:

- `GIT_USERNAME`: Your GitHub username
- `GIT_USER_EMAIL`: Your GitHub email address
- `GIT_USER_NAME`: Your full name for Git commits

#### Repository Secrets

These secrets are configured at the repository level:

- `GIT_ACCESS_TOKEN`: GitHub Personal Access Token with appropriate permissions

For detailed instructions on setting up these secrets, please refer to the [Setup Secrets Guide](../SETUP_SECRETS.md).

### Deployment Process

The workflow performs these straightforward steps:

1. Checks out the code
2. Sets up PHP 8.3 with required extensions
3. Determines the target environment based on the branch (production or staging)
4. Configures Git with the repository variables (GIT_USER_NAME, GIT_USER_EMAIL)
5. Sets up SSH with the environment secret (SSH_PRIVATE_KEY)
6. Runs the deployment script on the server which:
   - Pulls the latest code from the appropriate branch using GIT_USERNAME and GIT_ACCESS_TOKEN
   - Installs Composer dependencies
   - Optimizes the application and runs migrations

### Troubleshooting

If deployment fails, check:
1. That all required environment secrets, repository variables, and repository secrets are correctly configured
2. That the SSH key has the correct permissions on the server
3. That the deployment path exists and is writable
4. That the Git credentials (GIT_USERNAME, GIT_USER_EMAIL, GIT_USER_NAME, GIT_ACCESS_TOKEN) are valid
5. The GitHub Actions logs for specific error messages

## Code Style Workflow

The `lint.yml` workflow automatically fixes code style issues using Laravel Pint.

### Workflow Triggers

- Runs on every push to the repository except for the `main` and `staging` branches
- This ensures that code style fixes are only applied during development, not in production or staging environments

### Workflow Process

The workflow performs these steps:

1. Checks out the code
2. Sets up PHP 8.3 with required extensions
3. Installs Laravel Pint globally
4. Runs Pint to fix code style issues according to the rules in `pint.json`
5. Automatically commits any changes made by Pint

### Configuration

The code style rules are defined in the project's `pint.json` file. See the [Pint Configuration Documentation](../../docs/PINT_CONFIG.md) for details on the code style rules.

## Additional Resources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Setup Secrets Guide](../SETUP_SECRETS.md)
- [Deploy Script](../../deploy.sh)
- [Deployment Documentation](../../DEPLOYMENT.md)
- [Laravel Pint Documentation](https://laravel.com/docs/pint)
