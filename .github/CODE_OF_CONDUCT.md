# Contributing Guide

Thank you for considering contributing to the GravityWrite SSO Authentication Service! This document provides guidelines and instructions for development and contribution.

## Development Setup

### Requirements

- PHP >= 8.3
- Composer
- Node.js & NPM
- MySQL 8.0+
- Redis (optional, for queue processing)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/websitelearners/gw-sso.git
cd gw-sso
```

2. Install PHP dependencies:
```bash
composer install
```

3. Install JavaScript dependencies:
```bash
npm install
```

4. Copy the environment file:
```bash
cp .env.example .env
```

5. Generate application key:
```bash
php artisan key:generate
```

6. Configure your database in the `.env` file:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

7. Configure OAuth settings in the `.env` file:
```env
PASSPORT_PERSONAL_ACCESS_CLIENT_ID=
PASSPORT_PERSONAL_ACCESS_CLIENT_SECRET=
```

8. Run database migrations:
```bash
php artisan migrate
```

9. Install Passport:
```bash
php artisan passport:install
```

10. Update your `.env` file with the generated client credentials from the previous step.

## Development Workflow

### Starting the Development Environment

Start all services with the custom composer script:
```bash
composer dev
```

This will concurrently run:
- Laravel development server
- Queue worker
- Log viewer
- Vite for frontend assets

Alternatively, run individual services:

```bash
# Start the development server
php artisan serve

# Compile assets
npm run dev
```

### Code Style

This project uses Laravel Pint for automated code style enforcement. The code style is automatically fixed on every push to development branches (excluding `main` and `staging`) through GitHub Actions.

To run Pint locally:

```bash
# Install Laravel Pint if not already installed
composer require laravel/pint --dev

# Run Pint to fix code style issues
./vendor/bin/pint
```

For details on the code style configuration, see the [Pint Configuration Documentation](docs/PINT_CONFIG.md).

### Testing

Run the tests using PHPUnit:
```bash
php artisan test
```

### Branch Strategy

- `main` - Production branch
- `staging` - Staging environment branch
- Feature branches should be created from `staging` and named according to the feature being developed (e.g., `feature/user-authentication`)

### Pull Request Process

1. Create a feature branch from `staging`
2. Make your changes
3. Ensure all tests pass
4. Submit a pull request to the `staging` branch
5. Wait for code review and approval
6. Once approved, your changes will be merged

## Project Architecture

### Directory Structure

- `app/Abstracts/` - Abstract classes for request handling and responses
- `app/Http/Controllers/Auth/` - Authentication controllers with custom OAuth implementations
- `app/Http/Controllers/Api/` - API controllers for client management
- `app/Models/Passport/` - Custom Passport model implementations
- `app/Repositories/` - Custom repositories including OAuth client repository
- `app/Rules/` - Custom validation rules including email provider validation
- `app/Traits/` - Reusable traits for Passport and JSON responses
- `routes/` - Application routes (web.php, api.php, oauth.php)

### Key Components

- **Custom Client Repository**: Implements custom validation logic for OAuth clients
- **Passport Service Provider**: Configures and extends Laravel Passport
- **Social Authentication**: Handles authentication via third-party providers
- **Session Tracking**: Monitors OAuth sessions for security purposes

## API Endpoints

### OAuth Routes
- `POST /oauth/token` - Get access token
- `POST /oauth/token/refresh` - Refresh token
- `GET /oauth/authorize` - Authorization request

### Authentication Routes
- `POST /login` - User login
- `POST /register` - User registration
- `POST /password/email` - Send password reset link
- `POST /password/reset` - Reset password
- `GET /oauth/{provider}/redirect` - Social authentication redirect
- `GET /oauth/{provider}/callback` - Social authentication callback
- `POST /sso/{provider}/login` - Mobile SSO login

### Client Management Routes
- `GET /api/oauth/clients` - List OAuth clients
- `POST /api/oauth/clients` - Create OAuth client
- `GET /api/oauth/clients/{client_id}` - Get OAuth client
- `PUT /api/oauth/clients/{client_id}` - Update OAuth client
- `DELETE /api/oauth/clients/{client_id}` - Delete OAuth client

## Configuration

### Social Authentication

To enable social authentication, add the following to your `.env` file:

```env
# Social Authentication Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://your-domain.com/oauth/google/callback

FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret
FACEBOOK_REDIRECT_URI=https://your-domain.com/oauth/facebook/callback
```

### Email Verification

To enable email verification, configure your mail settings in the `.env` file:

```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

## Troubleshooting

### Common Issues

1. **Passport Installation Issues**:
   - Ensure you have run `php artisan migrate` before installing Passport
   - Check that your database credentials are correct

2. **Social Authentication Issues**:
   - Verify that your OAuth provider credentials are correct
   - Ensure redirect URIs match exactly what's configured in the provider

3. **Token Issues**:
   - Check that client IDs and secrets are correctly configured
   - Verify that token expiration times are properly set

For more help, please open an issue on the GitHub repository.
