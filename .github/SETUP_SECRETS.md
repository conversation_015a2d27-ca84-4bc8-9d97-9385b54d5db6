# Setting Up GitHub Secrets and Variables for CI/CD

This guide explains how to set up the required secrets and variables for the GitHub Actions CI/CD workflow.

## Required Configuration

The following configuration needs to be set up in your GitHub repository settings:

### Environment Secrets

These secrets are configured for each environment (staging, production) under the repository's Environment settings:

- `SSH_PRIVATE_KEY`: SSH private key for connecting to the server
- `SSH_HOST`: Hostname or IP address of the server
- `SSH_USER`: Username for SSH connection to the server
- `DEPLOY_PATH`: Path to the application environment on the server

### Repository Variables

These variables are configured at the repository level and are used across all environments:

- `GIT_USERNAME`: Your GitHub username
- `GIT_USER_EMAIL`: Your GitHub email address
- `GIT_USER_NAME`: Your full name for G<PERSON> commits

### Repository Secrets

These secrets are configured at the repository level and are used across all environments:

- `GIT_ACCESS_TOKEN`: GitHub Personal Access Token with appropriate permissions

## How to Add Secrets to Your GitHub Repository

1. Go to your GitHub repository
2. Click on "Settings" tab
3. In the left sidebar, click on "Secrets and variables" > "Actions"
4. Click on "New repository secret"
5. Add each of the required secrets for all environments

## How to Configure GitHub Environments, Secrets and Variables

### Setting Up Environments

1. Go to your GitHub repository
2. Click on "Settings" tab
3. In the left sidebar, click on "Environments"
4. Click on "New environment"
5. Create two environments: `staging` and `production`
6. For each environment, add the required environment secrets

### Setting Up Environment Secrets

For each environment (staging and production), add these secrets:

#### SSH Private Key

To get the content of your SSH key:

```bash
cat ~/.ssh/id_rsa
```

Copy the entire output, including the `-----BEGIN OPENSSH PRIVATE KEY-----` and `-----END OPENSSH PRIVATE KEY-----` lines.

Add this as `SSH_PRIVATE_KEY` in each environment.

#### SSH Host

Add the hostname or IP address of your server for each environment, for example:
- `staging.example.com` for staging environment's `SSH_HOST`
- `example.com` for production environment's `SSH_HOST`

#### SSH User

Add the username you use to SSH into your server for each environment, for example:
- `deploy` for staging environment's `SSH_USER`
- `deploy` for production environment's `SSH_USER`

#### Deploy Path

Add the full path to your application on the server for each environment, for example:
- `/var/www/staging` for staging environment's `DEPLOY_PATH`
- `/var/www/production` for production environment's `DEPLOY_PATH`

### Setting Up Repository Variables

1. Go to your GitHub repository
2. Click on "Settings" tab
3. In the left sidebar, click on "Secrets and variables" > "Actions"
4. Click on the "Variables" tab
5. Click on "New repository variable"
6. Add the following variables:
   - `GIT_USERNAME`: Your GitHub username
   - `GIT_USER_EMAIL`: Your GitHub email address
   - `GIT_USER_NAME`: Your full name for Git commits

### Setting Up Repository Secrets

1. Go to your GitHub repository
2. Click on "Settings" tab
3. In the left sidebar, click on "Secrets and variables" > "Actions"
4. Click on the "Secrets" tab
5. Click on "New repository secret"
6. Add the following secret:
   - `GIT_ACCESS_TOKEN`: Your GitHub Personal Access Token with appropriate permissions

## Testing the Workflow

After setting up all the secrets and variables, you can test the workflow by:

1. Making a commit and pushing to either the `staging` or `main` branch
2. Going to the "Actions" tab in your GitHub repository
3. Selecting the "Deploy Application" workflow
4. Clicking on "Run workflow" to manually trigger a deployment

## Troubleshooting

If you encounter issues with the deployment:

1. Check the workflow logs in the GitHub Actions tab
2. Verify that all secrets and variables are correctly set for each environment
3. Ensure your SSH keys have permission to access the respective servers
4. Confirm that the paths specified in the `DEPLOY_PATH` secrets exist on the servers
5. Check that the SSH user has the necessary permissions to run the deployment commands
6. Verify that the branch names match what's expected in the workflow file
7. Ensure that your `GIT_ACCESS_TOKEN` has the necessary permissions and hasn't expired
