<?php

use App\Helpers\JsonResponseHelper;
use Illuminate\Auth\AuthenticationException as AuthAuthenticationException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Laravel\Passport\Exceptions\AuthenticationException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
        then: function (): void {
            Route::middleware('api')
                ->prefix('oauth')
                ->name('passport.')
                ->group(base_path('routes/oauth.php'));
        },
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->appendToGroup('web', [
            App\Http\Middleware\PreventBackHistory::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        $exceptions->render(function (Throwable $exception) {
            if ($exception instanceof AuthenticationException || $exception instanceof AuthAuthenticationException) { // Passport Exceptions
                return;
            }

            if ( ! isset($exception->status) || $exception->status != Response::HTTP_UNPROCESSABLE_ENTITY) {
                $handler = (new JsonResponseHelper());

                return $handler->returnResponse($exception, $handler->getResponse($exception));
            }
        });

        $exceptions->report(function (Exception $exception): void {
            $message = $exception->getMessage() ?? 'Message Not Found';
            $origin = $exception->getTrace()[0] ?? 'Trace Not Found';
            $originClass = $origin['class'] ?? 'Class Not Found';
            $originMethod = $origin['function'] ?? 'Method Not Found';

            Log::error("*** [ERROR] HTTP Exception {$originClass}@{$originMethod}: {$message} ***", [
                'request' => request()->all(),
                'user_id' => auth()->id ?? null,
                'trace' => $exception->getTrace(),
            ]);

            // You can add code here to send emails
        })->stop();
    })->create();
