<?php

use App\Http\Controllers\Api\ClientController;
use App\Http\Controllers\Auth\SocialiteController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', fn(Request $request) => $request->user())->middleware('auth:api');
Route::post('sso/{driver}/login', [SocialiteController::class, 'mobileSsoLogin']);

Route::prefix('oauth')->group(function (): void {
    Route::get('/clients', [ClientController::class, 'forUser']);
    Route::post('/clients', [ClientController::class, 'store']);
    Route::get('/clients/{client_id}', [ClientController::class, 'show']);
    Route::put('/clients/{client_id}', [ClientController::class, 'update']);
    Route::delete('/clients/{client_id}', [ClientController::class, 'destroy']);
});
