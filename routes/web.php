<?php

use App\Http\Controllers\Auth\ApproveAuthorizationController;
use App\Http\Controllers\Auth\AuthorizationController;
use App\Http\Controllers\Auth\DenyAuthorizationController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\LogoutController;
use App\Http\Controllers\Auth\SocialiteController;
use App\Http\Middleware\EnsureEmailIsVerified;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

Route::get('/', fn() => to_route('login'));
Route::post('/force/login', [LoginController::class, 'forceLogin'])->name('login.force');

Auth::routes(['verify' => true]);
// Override passport routes
Route::prefix('oauth')->as('passport.')->group(function (): void {
    // Register our custom authorization routes
    Route::get('authorize', [AuthorizationController::class, 'authorize'])->middleware(EnsureEmailIsVerified::class)->name('authorizations.authorize');
    Route::post('authorize', [ApproveAuthorizationController::class, 'approve'])->middleware(EnsureEmailIsVerified::class)->name('authorizations.approve');
    Route::delete('authorize', [DenyAuthorizationController::class, 'deny'])->middleware(EnsureEmailIsVerified::class)->name('authorizations.deny');

    // Socialite
    Route::get('/{driver}/redirect', [SocialiteController::class, 'redirect'])->name('socialite.redirect');
    Route::get('/{driver}/callback', [SocialiteController::class, 'callback'])->name('socialite.callback');
});

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

Route::get('global/logout', [LogoutController::class, 'logoutAll'])->name('logout.all');
