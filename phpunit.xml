<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_STORE" value="array"/>

        <!-- Database Configuration for Testing -->
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="DB_FOREIGN_KEYS" value="true"/>

        <!-- Application Configuration -->
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>

        <!-- OAuth/Passport Testing Configuration -->
        <env name="PASSPORT_PERSONAL_ACCESS_CLIENT_ID" value="test-client-id"/>
        <env name="PASSPORT_PERSONAL_ACCESS_CLIENT_SECRET" value="test-client-secret"/>

        <!-- Disable external services during testing -->
        <env name="GOOGLE_CLIENT_ID" value="test-google-client-id"/>
        <env name="GOOGLE_CLIENT_SECRET" value="test-google-client-secret"/>
        <env name="EMAIL_VERIFIER_KEY" value="test-email-verifier-key"/>
        <env name="MAILER_LITE_API_KEY" value="test-mailer-lite-key"/>
    </php>
</phpunit>
